FROM node:24.4.1-alpine

ARG GITHUB_TOKEN
ARG PORT
ARG DATADOG_API_KEY
ARG DATADOG_SITE


ENV GITHUB_TOKEN=$GITHUB_TOKEN
ENV DATADOG_API_KEY=${DATADOG_API_KEY}
ENV DATADOG_SITE=${DATADOG_SITE}

WORKDIR /home/<USER>

COPY . .
RUN ls -al



# Skip prepare script (husky) when installing dependencies
ENV NPM_CONFIG_IGNORE_SCRIPTS=true

RUN npm ci --legacy-peer-deps
RUN npm i -g serve
RUN npm run build

# Optionally remove node_modules after build to reduce image size
RUN rm -rf node_modules
# Install only production dependencies for runtime
RUN npm ci --omit=dev --legacy-peer-deps

RUN npm install -g @datadog/datadog-ci
# RUN DATADOG_API_KEY=$DATADOG_API_KEY DATADOG_SITE=$DATADOG_SITE npx @datadog/datadog-ci sourcemaps upload ./dist/assets \
#   --service=internal-dashboard \
#   --release-version=1.0.0 \
#   --minified-path-prefix="https://business-admin.korapay.com/assets"

ENV APP_PORT=6100
EXPOSE $APP_PORT

# Change ownership to a non-root user
#RUN useradd -m appuser
#RUN chown -R appuser:appuser /home/<USER>
#USER appuser

# CMD ["sh", "-c", "serve -s dist -l ${APP_PORT}"]
