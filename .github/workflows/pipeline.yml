name: Admin Dashboard Deploy to Staging

on:
  push:
    branches: [review]

jobs:
  deploy:
    name: Deploy
    runs-on: self-hosted

    strategy:
      matrix:
        node-version: [24.x]

    steps:
      - name: Send Slack Notification
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: workflow,commit,repo,ref,author
          custom_payload: |
            {
              text: ':rocket: Starting Deployment to Staging',
              attachments: [{
                text: `${process.env.AS_WORKFLOW}\n\n(${process.env.AS_COMMIT}) of ${process.env.AS_REPO}@${process.env.AS_REF} by ${process.env.AS_AUTHOR}`,
                author_name: '${{ secrets.ACTION_SLACK_AUTHOR_NAME }}'
              }]
            }
          channel: ${{ secrets.SLACK_CHANNEL_DEPLOY }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()

      - name: Deploy to Staging
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_SSH_HOST }}
          username: ${{ secrets.STAGING_SSH_USERNAME }}
          key: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}
          passphrase: ${{ secrets.STAGING_SSH_PASSPHRASE }}
          script: |
            cd ~/korapay-client/admin
            ./deploy/deploy-full-ga.sh

      - name: Send Slack Notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,ref,message,author,eventName,workflow
          author_name: '${{ secrets.ACTION_SLACK_AUTHOR_NAME }}'
          channel: ${{ secrets.SLACK_CHANNEL_DEPLOY }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()
