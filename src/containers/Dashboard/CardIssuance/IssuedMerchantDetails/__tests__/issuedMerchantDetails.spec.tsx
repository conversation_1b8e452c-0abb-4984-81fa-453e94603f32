import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';
import { Mock, vi } from 'vitest';



import { useSetUserAccess } from '+hooks';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';



import IssuedMerchantDetails from '..';





vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockedIssuingMerchantDetails = () => {
  return (
    <MockIndexWithRoute route="/dashboard/card-issuance/merchants/:id" initialEntries={['/dashboard/card-issuance/merchants/1234567890']}>
      <IssuedMerchantDetails />
    </MockIndexWithRoute>
  );
};

describe('issuedMerchantDetails component', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({ 'card_issuance_wallet_history.view': true, 'card_issuing_merchant_details.view': true });
  it('Component is accessible', async () => {
    const { container } = render(<MockedIssuingMerchantDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('Renders on initial load with permission', async () => {
    render(<MockedIssuingMerchantDetails />);
    expect(await screen.findByText(/john doe/i)).toBeInTheDocument();
    expect(screen.getByText(/123/i)).toBeInTheDocument();
    expect(screen.getByText(/startup/i)).toBeInTheDocument();
    expect(screen.getByText(/mid/i)).toBeInTheDocument();
    expect(screen.getByText(/active/i)).toBeInTheDocument();
    expect(screen.getByText('Issuing Balance (USD)')).toBeInTheDocument();
    expect(screen.getByText(/200/i)).toBeInTheDocument();
    expect(screen.getByText(/cards issued/i)).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /reserved cards/i })).toBeInTheDocument();
    expect(screen.getByText(/started issuing/i)).toBeInTheDocument();
    expect(screen.getByText(/17 aug 2022/i)).toBeInTheDocument();
  });

  it('displays the issuing wallet history', async () => {
    render(<MockedIssuingMerchantDetails />);
    expect(await screen.findByText('Issuing History')).toBeInTheDocument();
    expect(await screen.findByText('All transactions (3)')).toBeInTheDocument();
    expect(await screen.findByText('12 Jul 2023,')).toBeInTheDocument();
    expect(screen.getByText('12:10 PM')).toBeInTheDocument();
    expect(screen.getByText(/100.00/i)).toBeInTheDocument();
    expect(screen.getByText(/7881.00/i)).toBeInTheDocument();
    expect(screen.getByText(/issuing balance funding/i)).toBeInTheDocument();
    expect(screen.getByText(/KPY-UoIPbyw6N1U74Mx/i)).toBeInTheDocument();
  });
});