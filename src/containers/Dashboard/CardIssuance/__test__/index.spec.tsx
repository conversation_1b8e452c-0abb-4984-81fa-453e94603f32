import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { expect } from 'vitest';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import Issuing from '../index';

function MockedIssuing() {
  return (
    <MockIndexWithRoute route="/dashboard/card-issuance" initialEntries={['/dashboard/card-issuance?activeCurrency=USD', '/']}>
      <Issuing />
    </MockIndexWithRoute>
  );
}

describe('Issuing', () => {
  test('Renders Issuing', async () => {
    render(<MockedIssuing />);
    expect(
      screen.getByText(
        'Kora allows merchants to create, manage and issue payment cards for customers. Here’s an overview of the card issuance service for different merchants on Kora.'
      )
    ).toBeInTheDocument();
  });

  test('Issuing is accessible', async () => {
    const { container } = render(<MockedIssuing />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('renders the Issuing component', async () => {
    render(<MockedIssuing />);

    expect(screen.getByRole('tab', { name: 'Overview' })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: 'Overview' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'USD' })).toBeInTheDocument();
  });

  test('renders the counter bubble after useQueries is called', async () => {
    render(<MockedIssuing />);
    await waitFor(() => {
      expect(screen.getByTestId('counter-bubble')).toBeInTheDocument();
    });
  });

  test('changes the active tab when a tab button is clicked', async () => {
    render(<MockedIssuing />);
    const merchantCardsButton = screen.getByRole('tab', { name: /Approvals/i });
    expect(screen.getByRole('heading', { name: 'Overview' })).toBeInTheDocument();
    expect(merchantCardsButton).toBeInTheDocument();
    await userEvent.click(merchantCardsButton);
    expect(screen.getByRole('heading', { name: /Approvals/i })).toBeInTheDocument();
  });
});
