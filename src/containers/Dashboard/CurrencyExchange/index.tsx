import React, { useEffect, useRef, useState } from 'react';
import { Route, Switch } from 'react-router-dom';

import { useFeedbackHandler, useSearchQuery } from '+hooks';

import Tabs from '../Identity/components/Tabs';
import HoverMenu from '../Shared/HoverMenu';
import Icon from '../Shared/Icons';
import Modal from '../Shared/Modal';
import TransactionDetails from '../TransactionDetails';
import { useFetchConversionLimit, useUpdateConversionLimit } from './components/currencyExchangeHelper';
import CurrencyPairs from './CurrencyPairs';
import ConversionTransactions from './Transactions';

const Conversions = () => {
  const searchQuery = useSearchQuery();

  const [openModal, setOpenModal] = useState(false);
  const tabs = [
    { name: 'Transactions', key: 'transactions' },
    {
      name: 'Currency Pairs',
      key: 'currency_pairs'
    }
  ];

  const { feedbackInit, closeFeedback } = useFeedbackHandler();

  const [limit, setLimit] = useState('');

  const { mutateAsync: updateCurrencyPairMarkup } = useUpdateConversionLimit(feedbackInit);

  const { data, isFetching } = useFetchConversionLimit(feedbackInit, closeFeedback);

  const limitInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (openModal) {
      setTimeout(() => {
        limitInputRef.current?.focus();
      }, 0);
    }
  }, [openModal]);

  useEffect(() => {
    if (data?.data?.max_amount !== undefined) {
      setLimit(data.data.max_amount.toString());
    }
  }, [data]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value.replace(/\s/g, '');
    setLimit(inputValue);
  };

  const activeTab = searchQuery.value.tab || 'transactions';

  return (
    <div className="content-i currency-conversion-page">
      <section className="content-box">
        <div className="ihs">
          <div className="ihs__content">
            <h1 className="ihs__title">Currency Conversions</h1>
            <p className="ihs__description">
              Here, you can manage currency pairs, update mark-ups, and set conversion limits. For single-merchant settings, go to
              merchants.
            </p>
          </div>
          <HoverMenu
            wrapperClassName="currency-conversion-menu-wrapper"
            title="actions"
            trigger={
              <button className="irc__header-btn d-flex align-items-center" disabled={isFetching}>
                <Icon name="setting" width={15} height={15} stroke="#fff" />
                <span className="mx-2">Manage Conversions</span>
                <Icon name="arrowDown" width={10} height={10} stroke="#fff" />
              </button>
            }
          >
            <li>
              <button
                type="button"
                className="btn btn--link"
                onClick={() => {
                  setOpenModal(true);
                }}
              >
                <span className="">
                  <Icon name="upwardTrend" height={12} width={12} /> <span className="ml-2">Set Conversion Limit</span>
                </span>
              </button>
            </li>
          </HoverMenu>
        </div>

        <Tabs activeTab={activeTab} tabs={tabs} searchQuery={searchQuery} />
        <div>
          {activeTab === 'transactions' && <ConversionTransactions />}
          {activeTab === 'currency_pairs' && <CurrencyPairs />}
        </div>
      </section>

      <Modal
        size="md"
        visible={openModal}
        close={() => {
          setOpenModal(false);
        }}
        heading="Set Conversion Limit"
        description="This is the maximum amount that can be converted per transaction. Its equivalent will be applied across all base currencies during conversion."
        firstButtonText="Cancel"
        content={
          <div className="">
            <label className="" htmlFor="inlineFormInputGroup">
              Max. Conversion Amount (per transaction)
            </label>
            <div className="input-group mb-2 border-0">
              <div className="input-group-prepend">
                <div className="input-group-text  py-0">USD</div>
              </div>
              <input
                type="number"
                id="limit"
                name="limit"
                className="form-control"
                placeholder=""
                value={limit}
                ref={limitInputRef}
                min={1}
                onChange={handleInputChange}
              />
            </div>

            <div className="rate-container p-4">
              <div className="d-flex">
                <Icon name="circledCaution" width={40} height={25} className="m-0" />
                <div>
                  <p>
                    <span className="font-weight-bold">Example: (NGN - USD)</span>: If the max conversion amount is capped at 50,000.00 USD,
                    and 1 USD = 1,200 NGN, your limit in NGN becomes ₦60,000,000.
                  </p>
                </div>
              </div>
            </div>
          </div>
        }
        secondButtonText="Confirm"
        secondaryCompletedModal
        secondButtonDisable={limit === '' || Number(limit) <= 0}
        completedDescription="You have successfully approved this funding request."
        completedActionText="Dismiss"
        secondButtonAction={async () =>
          await updateCurrencyPairMarkup({
            max_amount: Number(limit)
          })
        }
      />
    </div>
  );
};
export default function CurrencyExchange() {
  return (
    <div className="currency-conversions__container">
      <Switch>
        <Route exact path="/dashboard/currency-conversions" component={Conversions} />
        <Route path="/dashboard/currency-conversions/:id">
          <TransactionDetails />
        </Route>
      </Switch>
    </div>
  );
}
