import React from 'react';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';

import Typography from '+containers/Dashboard/Shared/Typography';
import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import LoadingPlaceholder from '+shared/LoadingPlaceHolder';
import { switchCurrency } from '+utils';

import './index.scss';

const api = new APIRequest();

const CurrenciesMenu = () => {
  const { feedbackInit } = useFeedbackHandler();
  const { data, refetch, isLoading } = useQuery('SYSTEM_CURRENCIES', () => api.getCurrencyMerchantCount(), {
    refetchOnMount: 'always',
    onError: () => {
      feedbackInit({
        message: `There has been an error in getting this currencies`,
        type: 'danger',
        action: {
          action: () => refetch(),
          name: 'Try again'
        }
      });
    }
  });

  return (
    <div className="menu-container">
      <div className="menu-container__heading">
        <Typography variant="h2">Product Configuration</Typography>
        <Typography variant="subtitle3">
          This is the main hub for managing products, currencies, and merchants with access to various currencies and products on Kora.
        </Typography>
      </div>
      <div className="menu">
        <Typography variant="subtitle3">Available Currencies on Kora.</Typography>
        <div className="table">
          {isLoading ? (
            <LoadingPlaceholder type="table" content={1} background="#f5f6f6" />
          ) : (
            Object.entries(data || {}).map(([key, value]) => (
              <div className="product-config-row" key={key}>
                <div className="product-config-row__currency">
                  <p>{switchCurrency[key as keyof typeof switchCurrency]}</p>
                </div>
                <div className="product-config-row__merchants">
                  <p>{`${value} merchants have access to this currency`}</p>
                </div>
                <div className="product-config-row__merchant">
                  <Link to={`/dashboard/product-config/${key}/merchants`}>Manage Merchants</Link>
                </div>
                <div className="product-config-row__product">
                  <Link to={`/dashboard/product-config/${key}`}>Manage Products</Link>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default CurrenciesMenu;
