@import 'styles/kpy-custom/variables';

.content {
  display: flex;
  flex-direction: column;
  margin-top: 2rem;
  font-family: 'Averta PE';

  & > * + * {
    margin-top: 30px;
  }

  .first-section {
    align-items: center;
    display: flex;
    justify-content: space-between;

    .title-wrapper {
      gap: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;

      span {
        display: flex;
        align-items: center;
        column-gap: 1rem;

        .status {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80px;
          font-size: 14px;
          font-weight: 500;
          height: 24px;
          border-radius: 5px;
        }
      }

      .title {
        font-weight: 600;
        color: #414f5f;
        font-size: 20px;
        margin-bottom: 0.5rem;
      }

      .subtitle {
        font-weight: 500;
        color: #a9afbc;
        font-size: 12px;
        margin-bottom: 0.5rem;
        line-height: 20px;
        max-width: 30rem;
      }
    }

    .controls {
      display: flex;

      .modal-body {
        padding: 0 1rem 1rem 1rem;
      }

      .--enable-btn {
        background-color: white;
        color: #2376f3;
        border: none;
        font-weight: 600;

        &.--more-btn {
          background-color: #eaf2fe;
        }
      }

      .ellipsis__nav {
        box-shadow: 0px 2px 20px 0px #0f182117;
        padding: 0.5rem 0.8rem;
        top: 15rem;
        right: 2rem;

        @media (min-width: $breakpoint-desktop) {
          right: 3rem;
        }

        .ellipsis__item {
          font-weight: 600;

          .active {
            color: #2376f3;
          }

          .disabled {
            color: #f32345;
          }
        }
      }
    }
  }

  .label {
    font-weight: 600;
    font-size: 12px;
    padding: 0px 6px 0px 6px;
    border-radius: 6px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    height: 1.3rem;
    width: 4rem;

    img {
      width: 0.8rem;
      height: 0.8rem;
      object-fit: contain;
      margin-right: 0.3rem;
    }

    &.--low {
      background-color: #e4fff1;
      color: #24b314;
    }

    &.--mid {
      background-color: #fff8e1;
      color: #fa9500;
    }

    &.--high {
      background-color: #ffd2da;
      color: #f32345;
    }

    &.--avg {
      background-color: #f3f0ff;
      color: #856cff;
    }
  }
}

.currency-modal__content {
  font-family: 'Averta PE';

  .radio_container {
    display: flex;
    flex-direction: column;

    label {
      display: flex;
      align-items: center;
      column-gap: 0.5rem;
      font-size: 14px;
      line-height: 20px;
      color: #3e4b5b;
      font-weight: 400;
      max-width: 22rem;
      font-family: 'Averta PE';

      &:first-child {
        margin-top: 0;
      }

      &:hover,
      input {
        cursor: pointer;
      }
    }
  }

  .info-wrapper {
    display: flex;
    align-items: center;

    img {
      width: 2rem;
      height: 1rem;
      object-fit: contain;
      margin-right: 0.5rem;
    }

    p {
      font-weight: 400;
      font-style: italic;
      font-family: 'Averta PE';
      font-size: 14px;
      max-width: 22rem;

      span {
        font-weight: 700;
      }

      &.enable-confirm,
      &.enable,
      &.enable-one,
      &.enable-product,
      &.enable-channel {
        color: #2376f3;
      }

      &.disable-confirm,
      &.disable,
      &.disable-one,
      &.disable-product,
      &.disable-channel {
        color: #f32345;
      }
    }
  }

  .prod-consent-wrapper {
    display: flex;
    align-items: center;
    column-gap: 0.5rem;

    span {
      color: #3e4b5b;
      font-weight: 500;
    }
  }

  .merchant--select {
    display: flex;
    flex-direction: column;
    width: 100%;
    font-family: 'Averta PE';
    margin-bottom: 2rem;

    &.--active {
      margin-bottom: 0;
    }

    h6 {
      color: #414f5f;
      font-weight: 500;
      font-size: 13px;
    }

    &-w {
      width: 100%;

      .list-dropdown--button,
      .list-dropdown--menu {
        width: 100%;
      }

      .list-dropdown--overlay {
        top: 100%;
      }

      & .list-dropdown--search {
        margin-left: 0.7rem;
        margin-right: 0.2rem;
        margin-top: -0.95rem;
      }

      &.--active {
        margin-bottom: 0;
      }
    }
  }
}
