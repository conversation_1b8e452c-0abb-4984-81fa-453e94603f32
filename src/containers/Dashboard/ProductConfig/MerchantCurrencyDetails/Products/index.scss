@import 'styles/kpy-custom/_custom';
@import 'styles/kpy-custom/variables';

.payins-container {
  display: flex;
  flex-direction: column;

  &__first {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 3rem;
    border-bottom: 1px solid #DDE2EC;

    div:first-child {
      font-family: 'Averta PE';
      & > span {
        display: flex;
        align-items: center;
        column-gap: 1rem;
        .status {
          max-width: 107px;
          padding: 2px 8px;
          border-radius: 0.2rem;
        }
      }

      p {
        margin-bottom: 0;
        font-weight: 500;
        color: #a9afbc;
        max-width: 500px;
      }
    }
    div:last-child {
      display: flex;
      align-items: center;
      column-gap: 0.8rem;
      p {
        margin-bottom: 0;
        font-weight: 500;
        color: #a9afbc;
      }

      .--enable-btn {
        background: #eaf2fe;
        border-color: #eaf2fe;
        color: #3e4b5b;
        font-weight: 500;
      }
    }
  }
}
