import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';
import { ProductConfigType } from '+types';

import Products from '../index';

const MockedProducts = ({ children }: { children: React.ReactNode }) => {
  return <MockIndex>{children}</MockIndex>;
};

const configData = {
  enabled: true,
  mobile_money: {
    channels: ['modal', 'web', 'api'],
    enabled: true,
    transaction_limit: { max: 10000000, min: 1000 }
  }
} as unknown as ProductConfigType;

describe('Products', () => {
  it('Products is accessible', async () => {
    const { container } = render(
      <MockedProducts>
        <Products type="pay-ins" label="pay-ins" merchantId="234" currency="NGN" config={configData} merchantsStatus />
      </MockedProducts>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('Should display Products properly', async () => {
    render(
      <MockedProducts>
        <Products type="pay-ins" label="pay-ins" merchantId="234" currency="NGN" config={configData} merchantsStatus />
      </MockedProducts>
    );

    expect(
      await screen.findByText(
        /here you can find the pay-ins products for this merchant. you can modify their limits and payment channels configuration./i
      )
    ).toBeInTheDocument();

    expect(screen.getByText(/allow this merchant to receive mobile money pay-ins in ngn/i)).toBeInTheDocument();
    expect(screen.getByText('Mobile Money Configuration')).toBeInTheDocument();
    expect(screen.getByText('Channels')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Here you can find the checkout and API products for this merchant’s Mobile Money configuration. You can modify these payment channels configuration here.'
      )
    ).toBeInTheDocument();
    expect(screen.getByText('Transaction Limit')).toBeInTheDocument();
    expect(screen.getAllByText('Enabled')).toHaveLength(2);
    expect(screen.getByText('Transaction Limit')).toBeInTheDocument();
    expect(screen.getByText('Payment via Checkout:')).toBeInTheDocument();
    expect(screen.queryByText('Payment via Dashboard:')).not.toBeInTheDocument();
    expect(screen.getByText('Payment via API:')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Here you can find the limits for this merchant’s Mobile Money configuration. You can modify these limits configuration here.'
      )
    ).toBeInTheDocument();
    expect(screen.getByText('Maximum limit per transaction:')).toBeInTheDocument();
    expect(screen.getByText('Minimum limit per transaction:')).toBeInTheDocument();
    expect(screen.getByText('10,000,000.00')).toBeInTheDocument();
    expect(screen.getByText('1,000.00')).toBeInTheDocument();
  });
});
