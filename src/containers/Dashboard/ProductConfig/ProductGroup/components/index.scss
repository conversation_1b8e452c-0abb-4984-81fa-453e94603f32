@import 'styles/kpy-custom/_custom';
@import 'styles/kpy-custom/variables';

.product-cards {
  /* &.--container {
    padding-top: 2rem;
  } */

  &.--content {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: 2rem;

    .product-card {
      margin-bottom: 2rem;
      padding: 1.5rem;
      border-radius: 5px;
      background-color: #fff;
      transition: all 0.3s ease-in-out;
      cursor: pointer;
      font-family: 'Averta PE';

      &:hover {
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
      }

      .product-card__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        font-family: 'Averta PE';
        font-weight: 500;
        font-size: 16px;
        color: #414f5f;
      }

      .product-card__description {
        font-family: 'Averta PE';
        font-weight: 500;
        font-size: 14px;
        color: #a9afbc;
        margin-bottom: 0.5rem;
        max-width: 28ch;
      }

      .product-card__action {
        color: #2376f3;
        display: flex;
        align-items: center;

        .os-icon-ui-46 {
          margin-right: 0.4rem;
          color: #2376f3;
        }

        span:last-child {
          font-weight: 500;
          font-size: 14px;

          a {
            text-decoration: none;
          }
        }
      }
    }
  }
}

// SIDEBAR COMPONENT
.sidebar-comp {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;

  & > .sidebar {
    flex-basis: 25rem;
    flex-grow: 1;

    & > * + * {
      margin-top: 0.75rem;
    }

    & > #tabs-heading {
      text-transform: uppercase;
      font-weight: 500;
      font-size: 12px;
      color: #a9afbc;
      margin-bottom: 0;
    }

    & > .tabs-list {
      display: block;
    }

    & .tab {
      color: #414f5f;
      margin-left: 0;
      opacity: 1;
      padding-block: 0.75rem;
      padding-inline: 1rem;
      text-align: left;
      width: 100%;

      &.active {
        color: #2376f3;
        border-radius: 8px;
        background-color: #f1f6fa;
      }

      &.disabled {
        opacity: 0.4;
      }
    }

    &.tab::before,
    ::after {
      all: unset;
    }
  }

  & > .not-sidebar {
    flex-basis: 0;
    flex-grow: 999;
    min-inline-size: 50%;
  }
}
