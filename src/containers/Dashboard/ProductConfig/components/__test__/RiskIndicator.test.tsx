import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import RiskIndicator from '+dashboard/ProductConfig/components/RiskIndicator';

describe('Risk Indicator', () => {
  it('Risk Indicator is accessible', async () => {
    const { container } = render(<RiskIndicator riskLevel="medium_risk" />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('should render mid content when risk level is mid', () => {
    render(<RiskIndicator riskLevel="medium_risk" />);
    expect(screen.getByText('MID')).toBeInTheDocument();
  });

  it('should render high content when risk level is high', () => {
    render(<RiskIndicator riskLevel="high_risk" />);
    expect(screen.getByText('HIGH')).toBeInTheDocument();
  });
  it('should render low content when risk level is low', () => {
    render(<RiskIndicator riskLevel="low_risk" />);
    expect(screen.getByText('LOW')).toBeInTheDocument();
  });
  it('should render above average content when risk level is above average', () => {
    render(<RiskIndicator riskLevel="above_average_risk" />);
    expect(screen.getByText('ABOVE AVG')).toBeInTheDocument();
  });
});
