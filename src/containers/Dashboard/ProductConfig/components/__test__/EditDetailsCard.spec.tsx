import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { Mock, vi } from 'vitest';



import EditDetailsCard from '+dashboard/ProductConfig/components/EditDetailsCard';
import { useSetUserAccess } from '+hooks';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';





vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockedEditDetailsCard = ({ children }: { children: React.ReactNode }) => {
  return <MockIndex>{children}</MockIndex>;
};

describe('EditDetailsCard', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({ 'transaction_config_details.update': true });
  it('EditDetails is accessible', async () => {
    const { container } = render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api']}
          type="Pay-ins"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('render correctly', () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api']}
          type="mobile_money"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );
    expect(screen.getByText('Channels')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Here you can find the checkout and API products for this merchant’s Mobile Money configuration. You can modify these payment channels configuration here.'
      )
    ).toBeInTheDocument();
    expect(screen.queryByText('Payment via Dashboard:')).not.toBeInTheDocument();
    expect(screen.getByText('Payment via API:')).toBeInTheDocument();
    expect(screen.getAllByText('Enabled')).toHaveLength(1);
    expect(screen.getAllByText('Disabled')).toHaveLength(1);
  });
  it('render the available channels', () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api', 'modal']}
          type="mobile_money"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );

    expect(screen.queryByText('Payment via Dashboard:')).not.toBeInTheDocument();
    expect(screen.getByText('Payment via API:')).toBeInTheDocument();
    expect(screen.getByText('Payment via Checkout:')).toBeInTheDocument();
    expect(screen.getAllByText('Enabled')).toHaveLength(2);
  });

  it('EditDetailsCard should with withdrawal limit', async () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="withdrawal_limit"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: {
                other_accounts: 1000,
                settlement_account: 5000
              },
              settlement_account: 6000,
              other_accounts: 4000
            },
            limit: { min: 8000, max: 100_000 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    expect(screen.getAllByText(/withdrawal limit/i)).toHaveLength(2);
    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.getAllByText(/settlement/i)).toHaveLength(2);
    expect(screen.getByText(/non-settlement/i)).toBeInTheDocument();
    expect(screen.getAllByText(/limit per day/i)).toHaveLength(2);
    expect(screen.getAllByText(/limit per transaction/i)).toHaveLength(2);
    expect(screen.getByText('1,000.00')).toBeInTheDocument();
    expect(screen.getByText('5,000.00')).toBeInTheDocument();
    expect(screen.getByText('6,000.00')).toBeInTheDocument();
    expect(screen.getByText('4,000.00')).toBeInTheDocument();
  });

  it('should not display withdrawal limit validation error when per day limit exceeds transaction limit and disables confirm button', async () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="withdrawal_limit"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: {
                other_accounts: 6000,
                settlement_account: 5000
              },
              settlement_account: 3000,
              other_accounts: 4000
            },
            limit: { min: 10000, max: 100_000 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    const editButton = screen.getByText('Edit');
    userEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText(/Withdrawal limits cannot be more than transaction limits/i)).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('You can edit the limits for this product category by clicking into the input fields')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Min transaction limit: 10,000.00')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('Max transaction limit: 100,000.00')).toBeInTheDocument();
    });

    const settlementDayInput = screen.getByPlaceholderText(/Enter settlement day value/i);
    expect(settlementDayInput).toHaveValue('5,000.00');

    fireEvent.change(settlementDayInput, { target: { value: ******** } });

    expect(screen.queryByText(/Withdrawal limit must be less than the transaction limit/i)).not.toBeInTheDocument();

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    userEvent.click(consentCheckbox);

    const confirmButton = screen.getByText('Confirm & Edit');
    expect(confirmButton).toBeDisabled();
  });

  it('should display withdrawal limit validation error when per day limit exceeds transaction limit and disables confirm button', async () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="withdrawal_limit"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: {
                other_accounts: 6000,
                settlement_account: 5000
              },
              settlement_account: 3000,
              other_accounts: 4000
            },
            limit: { min: 10000, max: 100_000 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    const editButton = screen.getByText('Edit');
    userEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText(/Withdrawal limits cannot be more than transaction limits/i)).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('You can edit the limits for this product category by clicking into the input fields')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('Min transaction limit: 10,000.00')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('Max transaction limit: 100,000.00')).toBeInTheDocument();
    });

    const settlementInput = screen.getByPlaceholderText(/Enter settlement transaction value/i);
    expect(settlementInput).toHaveValue('3,000.00');

    fireEvent.change(settlementInput, { target: { value: ******** } });

    expect(screen.getByText(/Withdrawal limit per transaction must be less than the max transaction limit/i)).toBeInTheDocument();

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    userEvent.click(consentCheckbox);

    const confirmButton = screen.getByText('Confirm & Edit');
    expect(confirmButton).toBeDisabled();
  });

  it('should display withdrawal limit validation error when single transaction limit exceeds daily limit and disables confirm button', async () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="withdrawal_limit"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: {
                other_accounts: 2000,
                settlement_account: 5000
              },
              settlement_account: 6000,
              other_accounts: 4000
            },
            limit: { min: 1000, max: 100_000 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    const editButton = screen.getByText('Edit');
    userEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText(/Single transaction limit must be less than the daily limit/i)).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('You can edit the limits for this product category by clicking into the input fields')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Min transaction limit: 1,000.00')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Max transaction limit: 100,000.00')).toBeInTheDocument();
    });

    const settlementDayInput = screen.getByPlaceholderText(/Enter settlement day value/i);
    expect(settlementDayInput).toHaveValue('5,000.00');

    fireEvent.change(settlementDayInput, { target: { value: 600000 } });

    expect(screen.getByText(/Single transaction limit must be less than the daily limit/i)).toBeInTheDocument();

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    userEvent.click(consentCheckbox);

    const confirmButton = screen.getByText('Confirm & Edit');
    expect(confirmButton).toBeDisabled();
  });

  it('Should enable confirm button when consent is checked', async () => {
    let payload: unknown;
    server.use(
      http.patch('/admin/settings/merchants/configuration', async ({ request }) => {
        payload = await request.json();
        return HttpResponse.json({}, { status: 200 });
      })
    );
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="withdrawal_limit"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: {
                other_accounts: 4000,
                settlement_account: 5000
              },
              settlement_account: 3000,
              other_accounts: 1000
            },
            limit: { min: 1000, max: 100000 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    const editButton = screen.getByText('Edit');
    userEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText(/Withdrawal limits cannot be more than transaction limits/i)).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('You can edit the limits for this product category by clicking into the input fields')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('Min transaction limit: 1,000.00')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('Max transaction limit: 100,000.00')).toBeInTheDocument();
    });

    expect(screen.queryByText(/Withdrawal limit must be less than the transaction limit/i)).not.toBeInTheDocument();

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    userEvent.click(consentCheckbox);

    await waitFor(() => {
      expect(screen.getByTestId('second-button')).toBeEnabled();
    });
    userEvent.click(screen.getByTestId('second-button'));

    await waitFor(() =>
      expect(payload).toEqual({
        currency: 'NGN',
        payment_type: 'disbursement',
        payment_method: 'bank_account',
        type: 'single_merchant',
        data: {
          enabled: true,
          withdrawal_limit: { daily: { settlement_account: 5000, other_accounts: 4000 }, settlement_account: 3000, other_accounts: 1000 }
        },
        account_id: '23'
      })
    );

    await waitFor(() => {
      expect(screen.getByText('You have made changes to the Withdrawal limit for NGN Bank Account')).toBeInTheDocument();
    });
  });

  it('should display withdrawal limit validation error when single transaction limit less than min transaction limit and disables confirm button', async () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="withdrawal_limit"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: {
                other_accounts: 8000,
                settlement_account: 9000
              },
              settlement_account: 6000,
              other_accounts: 4000
            },
            limit: { min: 2000, max: 100000 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    const editButton = screen.getByText('Edit');
    userEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText('You can edit the limits for this product category by clicking into the input fields')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('Min transaction limit: 2,000.00')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Max transaction limit: 100,000.00')).toBeInTheDocument();
    });

    const settlementTransactionInput = screen.getByPlaceholderText(/Enter settlement transaction value/i);
    expect(settlementTransactionInput).toHaveValue('6,000.00');

    fireEvent.change(settlementTransactionInput, { target: { value: 100000 } });

    expect(screen.getByText(/Withdrawal limit per transaction must be more than the min transaction limit/i)).toBeInTheDocument();

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    userEvent.click(consentCheckbox);

    const confirmButton = screen.getByText('Confirm & Edit');
    expect(confirmButton).toBeDisabled();
  });

  it('EditDetailsCard should not have Edit when permission is not  granted', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'transaction_config_details.update': false });
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api', 'modal']}
          type="mobile_money"
          disableEdit
        />
      </MockedEditDetailsCard>
    );
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });
});
