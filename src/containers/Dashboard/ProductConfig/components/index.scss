@import 'styles/kpy-custom/variables';

.risk-label {
  font-weight: 600;
  font-size: 12px;
  padding: 0px 6px 0px 6px;
  border-radius: 6px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  height: 1.4rem;
  width: 4rem;

  img {
    width: 0.8rem;
    height: 0.8rem;
    object-fit: contain;
    margin-right: 0.3rem;
  }

  &.--low {
    background-color: #e4fff1;
    color: #24b314;
  }

  &.--mid {
    background-color: #fff8e1;
    color: #fa9500;
  }

  &.--high {
    background-color: #ffd2da;
    color: #f32345;
  }

  &.--avg {
    background-color: #f3f0ff;
    color: #856cff;
    width: 7rem;
  }
}

.editable-card {
  display: flex;
  flex-direction: column;
  margin-bottom: 5rem;

  &__header {
    font-family: 'Averta PE';
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 0.5rem;
    text-transform: capitalize;
  }

  &__reset {
    background: #fff8e1;
    padding: 0.5rem 0.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 6px;
    margin-bottom: 10px;
    max-height: 40px;

    &.--text {
      font-size: 14px;
      font-weight: 500;
      color: #414f5f;
      margin-bottom: 0;
    }

    &.--action {
      font-size: 16px;
      font-weight: 600;
      color: #2376f3;
      margin-bottom: 0;
      cursor: pointer;
    }
  }

  &__body {
    background-color: #f9fbfd;
    max-width: 990px;
    width: 100%;
    padding: 2rem 1.5rem 1rem;
    display: flex;
    flex-direction: column;

    .first-section {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1rem;

      .card-title {
        max-width: 500px;
        color: #94a7b7;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
      }

      .card-action {
        color: #2376f3;
        font-size: 15px;
        font-weight: 500;
      }
    }

    .second-section {
      display: flex;
      flex-direction: column;

      .menu {
        display: flex;
        align-items: center;
        column-gap: 0.3rem;
        color: #414f5f;
        margin-bottom: 1.1rem;

        p {
          font-weight: 300;
          font-size: 14px;
          margin-bottom: 0;
        }

        span {
          font-weight: 500;
          font-size: 14px;
        }
      }
    }
  }
}

.currency-modal__content {
  .radio_container {
    margin-top: 0;

    &.--channels {
      margin-top: 0;
      margin-bottom: 1rem;

      .channel-item {
        margin-bottom: 0.5rem;
        font-weight: 500 !important;
        font-size: 15px !important;
      }

      .channel-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        column-gap: 1rem;
        margin-bottom: 1rem;
        width: 100%;

        span {
          color: #414f5f;
          font-weight: 300;
          font-size: 14px;
        }

        input {
          width: 170px;
          height: 40px;
          margin-right: 2rem;
          border-radius: 4px;
          border: 1px solid #dfe3e8;
          padding: 0 1rem;
          font-size: 14px;
          font-weight: 500;
          color: #414f5f;
          outline: none;
          transition: all 0.3s ease-in-out;

          &:focus {
            border: 1px solid #2376f3;
          }
        }
      }
    }
  }

  .info-wrapper {
    &.--channel-info {
      margin-bottom: 1rem;

      .enable {
        margin-bottom: 0rem;
      }
    }
  }
}

.section-info-comp {
  flex-grow: 2;

  & > * + * {
    margin-top: 10px !important;
  }

  & > .title-and-status {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
}

.vba-edit-count-form {
  .vba-form-container {
    margin-bottom: 24px;
  }

  .form-input-vba-edit-flex {
    display: flex;
    justify-content: space-between;
  }

  .form-input-vba-edit-count {
    width: 100%;
    height: 40px;
    margin-right: 2rem;
    border-radius: 4px;
    border: 1px solid #dfe3e8;
    padding: 0 1rem;
    font-size: 14px;
    font-weight: 500;
    color: #414f5f;
    outline: none;
    transition: all 0.3s ease-in-out;

    &:focus {
      border: 1px solid #2376f3;
    }
  }

  .pt-11 {
    padding-top: 10px;
  }

  .vba-edit-count-label {
    font-family: 'Averta PE';
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }

  .mt-24 {
    margin-top: 24px;
  }

  .vba-limit-form-margin-bottom-5 {
    margin-bottom: 5px;
  }

  .vba-limit-form-margin-bottom-24 {
    margin-bottom: 24px;
  }

  .vba-edit-text-area-label {
    font-family: 'Averta PE';
    font-size: 16px;
    font-weight: 400;
    letter-spacing: -0.003em;
  }

  .vba-edit-form-label {
    font-family: 'Averta PE';
    font-size: 14px;
    font-weight: 500;
  }

  .vba-edit-blue-label-wrapper {
    display: flex;
  }

  .vba-limit-padding-top-11 {
    padding-top: 11px;
  }

  .vba-edit-text-label {
    font-family: 'Averta PE';
    font-size: 14px;
    color: #2376f3;
    overflow-wrap: break-word;
  }

  .vba-limit-medium-font {
    font-weight: 400;
  }

  .vba-limit-light-font {
    font-weight: 300;
  }
}

