.withdrawal-limit-content {
  font-family: 'Averta PE', 'Roboto', sans-serif;
  display: flex;
  flex-direction: column;
  gap: 10px;

  &__header {
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    &-info {
      display: flex;
      flex-direction: column;
      background-color: #fff7ed;
      padding: 10px;
      justify-content: center;
      border-radius: 10px;
      margin-bottom: 10px;
      p {
        font-size: 13px;
        font-weight: 500;
        color: #915200;
        margin: 0;
      }
    }

    & > p {
      font-size: 13px;
      font-weight: 500;
      color: #414f5f;
      margin-top: 15px;
      margin-block: 5px;
    }
  }

  &__body {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;

    &-input {
      display: flex;
      flex-direction: column;
      gap: 10px;

      & > span {
        text-transform: capitalize;
        color: #292b2c;
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 5px;
      }

      &-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;

        & > span {
          color: #3e4b5b;
          font-size: 13px;
          font-weight: 400;
        }

        & > input {
          min-width: 250px;
          height: 40px;
          border: 1px solid #dde2ec;
          border-radius: 5px;
          padding: 0 10px;
        }
      }
    }
  }
}

.withdrawal-limit-details {
  &__header {
    font-size: 13px;
    font-weight: 500;
    color: #414f5f;
    margin-top: 5px;
  }
}
