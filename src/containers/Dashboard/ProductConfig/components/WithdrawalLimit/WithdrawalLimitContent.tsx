import React, { useEffect, useState } from 'react';

import { TransactionLimitType, WithdrawalLimitType } from '+types';
import { backwardAmountInput, cleanInput, formatAmount, formatWithCommas } from '+utils';

import './index.scss';

const limitsLabel = {
  day: 'Limit per day',
  transaction: 'Limit per transaction'
};

type limitsLabelType = keyof typeof limitsLabel;

const WithdrawalLimitContent = ({
  content,
  transactionLimit,
  handleWithdrawalLimitChange,
  merchantConfigType
}: {
  content: WithdrawalLimitType;
  transactionLimit: TransactionLimitType;
  handleWithdrawalLimitChange: (data: WithdrawalLimitType) => void;
  merchantConfigType?: 'default' | 'custom_merchants' | 'all_merchants' | null;
}) => {
  const { web } = content;
  const defaultWithdrawalLimit =
    merchantConfigType === 'custom_merchants'
      ? {
          settlement: {
            day: 0,
            transaction: 0
          },
          'non-settlement': {
            day: 0,
            transaction: 0
          }
        }
      : {
          settlement: {
            day: web.daily.settlement_account,
            transaction: web.settlement_account
          },
          'non-settlement': {
            day: web.daily.other_accounts,
            transaction: web.other_accounts
          }
        };
  const [withdrawalLimit, setWithdrawalLimit] = useState(defaultWithdrawalLimit);
  type WithdrawalLimitType = keyof typeof withdrawalLimit;

  useEffect(() => {
    handleWithdrawalLimitChange({
      web: {
        daily: { settlement_account: +withdrawalLimit.settlement.day, other_accounts: +withdrawalLimit['non-settlement'].day },
        settlement_account: +withdrawalLimit.settlement.transaction,
        other_accounts: +withdrawalLimit['non-settlement'].transaction
      }
    });
  }, [withdrawalLimit]);

  const handleChange = (type: WithdrawalLimitType, item: limitsLabelType, value: string) => {
    const formattedValue = backwardAmountInput(value.replace(/,/g, '').replace(/[<>%$-]/gi, ''));
    setWithdrawalLimit({
      ...withdrawalLimit,
      [type]: {
        ...withdrawalLimit[type],
        [item]: String(formattedValue === '' ? 0 : formattedValue)
      }
    });
  };

  return (
    <section className="withdrawal-limit-content">
      <section className="withdrawal-limit-content__header">
        <div className="withdrawal-limit-content__header-info">
          <p>Withdrawal limits cannot be more than transaction limits</p>
        </div>
        <p>Min transaction limit: {formatAmount(transactionLimit.min)}</p>
        <p>Max transaction limit: {formatAmount(transactionLimit.max)}</p>
      </section>
      <section className="withdrawal-limit-content__body">
        {Object.entries(withdrawalLimit).map(([values, keys]) => {
          return (
            <div className="withdrawal-limit-content__body-input" key={values}>
              <span>{values}</span>
              {Object.entries(keys).map(([value, key]) => {
                return (
                  <div className="withdrawal-limit-content__body-input-item" key={value}>
                    <span>{limitsLabel[value as limitsLabelType]}</span>
                    <input
                      value={formatWithCommas(formatAmount(String(key)))}
                      placeholder={`Enter ${values} ${value} value`}
                      type="numeric"
                      onChange={e => handleChange(values as WithdrawalLimitType, value as limitsLabelType, cleanInput(e.target.value))}
                    />
                  </div>
                );
              })}
            </div>
          );
        })}
      </section>
    </section>
  );
};

export default WithdrawalLimitContent;
