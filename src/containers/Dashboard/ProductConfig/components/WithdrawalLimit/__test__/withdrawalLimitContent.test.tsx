import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { describe, vi } from 'vitest';

import { WithdrawalLimitType } from '+types';

import WithdrawalLimitContent from '../WithdrawalLimitContent';

describe('WithdrawalLimitContent', () => {
  const mockHandleWithdrawalLimitChange = vi.fn();
  const mockTransactionLimit = { max: 1_000_000.0, min: 10_000.0 };

  const mockContent: WithdrawalLimitType = {
    web: {
      daily: {
        other_accounts: 500_000.0,
        settlement_account: 2_000_000.0
      },
      settlement_account: 5_000_000.0,
      other_accounts: 1_000_000.0
    }
  };

  it('should render the component correctly', () => {
    render(
      <WithdrawalLimitContent
        content={mockContent}
        transactionLimit={mockTransactionLimit}
        handleWithdrawalLimitChange={mockHandleWithdrawalLimitChange}
      />
    );

    expect(screen.getAllByText(/Settlement/i)).toHaveLength(2);
    expect(screen.getByText(/Non-Settlement/i)).toBeInTheDocument();
    expect(screen.getByText(/Withdrawal limits cannot be more than transaction limits/i)).toBeInTheDocument();
    expect(screen.getAllByText(/Limit per day/i)).toHaveLength(2);
    expect(screen.getAllByText(/Limit per transaction/i)).toHaveLength(2);
  });

  it('should display the correct initial values', () => {
    render(
      <WithdrawalLimitContent
        content={mockContent}
        transactionLimit={mockTransactionLimit}
        handleWithdrawalLimitChange={mockHandleWithdrawalLimitChange}
      />
    );

    const settlementDayInput = screen.getByPlaceholderText(/Enter settlement day value/i);
    const settlementTransactionInput = screen.getByPlaceholderText(/Enter settlement transaction value/i);
    const otherAccountsDayInput = screen.getByPlaceholderText(/Enter non-settlement day value/i);
    const otherAccountsTransactionInput = screen.getByPlaceholderText(/Enter non-settlement transaction value/i);

    expect(settlementDayInput).toHaveValue('2,000,000.00');
    expect(settlementTransactionInput).toHaveValue('5,000,000.00');
    expect(otherAccountsDayInput).toHaveValue('500,000.00');
    expect(otherAccountsTransactionInput).toHaveValue('1,000,000.00');
  });

  it('should call handleWithdrawalLimitChange when input values change', () => {
    render(
      <WithdrawalLimitContent
        content={mockContent}
        transactionLimit={mockTransactionLimit}
        handleWithdrawalLimitChange={mockHandleWithdrawalLimitChange}
      />
    );

    const settlementDayInput = screen.getByPlaceholderText(/Enter settlement day value/i);
    const settlementTransactionInput = screen.getByPlaceholderText(/Enter settlement transaction value/i);
    const otherAccountsDayInput = screen.getByPlaceholderText(/Enter non-settlement day value/i);
    const otherAccountsTransactionInput = screen.getByPlaceholderText(/Enter non-settlement transaction value/i);

    fireEvent.change(settlementDayInput, { target: { value: 600000 } });
    fireEvent.change(settlementTransactionInput, { target: { value: 1000000 } });
    fireEvent.change(otherAccountsDayInput, { target: { value: 100000 } });
    fireEvent.change(otherAccountsTransactionInput, { target: { value: 200000 } });

    expect(mockHandleWithdrawalLimitChange).toHaveBeenCalledWith({
      web: {
        daily: {
          other_accounts: 1000,
          settlement_account: 6000
        },
        settlement_account: 10000,
        other_accounts: 2000
      }
    });
  });

  it('should handle empty content gracefully', () => {
    const emptyContent: WithdrawalLimitType = {
      web: {
        daily: {
          other_accounts: 0,
          settlement_account: 0
        },
        settlement_account: 0,
        other_accounts: 0
      }
    };

    render(
      <WithdrawalLimitContent
        content={emptyContent}
        transactionLimit={mockTransactionLimit}
        handleWithdrawalLimitChange={mockHandleWithdrawalLimitChange}
      />
    );

    const settlementDayInput = screen.getByPlaceholderText(/Enter settlement day value/i);
    const settlementTransactionInput = screen.getByPlaceholderText(/Enter settlement transaction value/i);
    const otherAccountsDayInput = screen.getByPlaceholderText(/Enter non-settlement day value/i);
    const otherAccountsTransactionInput = screen.getByPlaceholderText(/Enter non-settlement transaction value/i);
    expect(settlementDayInput).toHaveValue('0.00');
    expect(settlementTransactionInput).toHaveValue('0.00');
    expect(otherAccountsDayInput).toHaveValue('0.00');
    expect(otherAccountsTransactionInput).toHaveValue('0.00');
  });

  it('should display max and min transaction limit information', () => {
    render(
      <WithdrawalLimitContent
        content={mockContent}
        transactionLimit={mockTransactionLimit}
        handleWithdrawalLimitChange={mockHandleWithdrawalLimitChange}
      />
    );

    expect(screen.getByText(/Max transaction limit/i)).toBeInTheDocument();
    expect(screen.getByText(/Min transaction limit:/i)).toBeInTheDocument();
    expect(screen.getByText(/1,000,000/i)).toBeInTheDocument();
    expect(screen.getByText(/10,000/i)).toBeInTheDocument();
  });
});
