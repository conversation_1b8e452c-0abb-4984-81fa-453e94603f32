import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe } from 'vitest';

import { WithdrawalLimitType } from '+types';

import WithdrawalLimitDetails from '../WithdrawalLimitDetails';

describe('WithdrawalLimitDetails', () => {
  const mockContent: WithdrawalLimitType = {
    web: {
      daily: {
        other_accounts: 500000,
        settlement_account: 2000000
      },
      settlement_account: 5000000,
      other_accounts: 1000000
    }
  };

  it('should render the component correctly with all labels and values', () => {
    render(<WithdrawalLimitDetails content={mockContent} />);

    // Headers
    expect(screen.getAllByText(/Settlement/i)).toHaveLength(2);
    expect(screen.getByText(/Non-Settlement/i)).toBeInTheDocument();

    // Labels
    expect(screen.getAllByText(/Limit per day/i)).toHaveLength(2);
    expect(screen.getAllByText(/Limit per transaction/i)).toHaveLength(2);

    expect(screen.getByText('2,000,000.00')).toBeInTheDocument(); 
    expect(screen.getByText('5,000,000.00')).toBeInTheDocument(); 
    expect(screen.getByText('500,000.00')).toBeInTheDocument(); 
    expect(screen.getByText('1,000,000.00')).toBeInTheDocument(); 
  });

  it('should handle empty content gracefully', () => {
    const emptyContent: WithdrawalLimitType = {
      web: {
        daily: {
          other_accounts: 0,
          settlement_account: 0
        },
        settlement_account: 0,
        other_accounts: 0
      }
    };
    render(<WithdrawalLimitDetails content={emptyContent} />);

    expect(screen.getAllByText('0.00')).toHaveLength(4);
  });
});
