import React from 'react';

import { WithdrawalLimitType } from '+types';
import { capitalize, formatAmount } from '+utils/formatting';

const limitsLabel = {
  day: 'Limit per day',
  transaction: 'Limit per transaction'
};

const WithdrawalLimitDetails = ({ content }: { content: WithdrawalLimitType }) => {
  const { web } = content;
  const withdrawalLimit = {
    settlement: {
      day: web.daily.settlement_account,
      transaction: web.settlement_account
    },
    'non-settlement': {
      day: web.daily.other_accounts,
      transaction: web.other_accounts
    }
  };

  return Object.entries(withdrawalLimit).map(([keys, values]) => (
    <div key={keys} className='withdrawal-limit-details'>
      <p className="withdrawal-limit-details__header">{capitalize(keys)}</p>
      {Object.entries(values).map(([key, value]) => {
        return (
          <div key={key} className="menu">
            <p>{limitsLabel[key as keyof typeof limitsLabel]}:</p> <span>{formatAmount(Number(value ?? 0))}</span>
          </div>
        );
      })}
    </div>
  ));
};

export default WithdrawalLimitDetails;
