import React from 'react';
import { useParams } from 'react-router-dom';

import Icon from '+containers/Dashboard/Shared/Icons';
import Table from '+containers/Dashboard/Shared/Table';
import { PayinType } from '+types';
import { capitalize, capitalizeRemovedash, cardStatus, getDate, getTime, history, isAllowed, switchStatus } from '+utils';

import useGetCustomerTransactionDetails from '../hooks/useGetCustomerTransactionDetails';

import '../../index.scss';

import LargeExportModal from '+containers/Dashboard/Shared/LargeExportModal';
import { useSetUserAccess } from '+hooks';
import useStore from '+store';

const CustomerTransactionDetails = () => {
  const { customer_name } = useParams<{ customer_name: string }>();
  const customerName = customer_name ? decodeURIComponent(customer_name) : 'Not Available';
  const { customerDetails, transactions, paging, page, isLoadingTransaction, exportFile, showLargeExportModal, setLargeExportModal } =
    useGetCustomerTransactionDetails('payins');
  const summary = customerDetails?.data?.data || {};
  const totalPayins = summary.count ?? 0;
  const statusArr = summary.status || [];
  const successfulPayins = statusArr.find((s: { status: string }) => s.status === 'success')?.count ?? 0;
  const failedPayins = statusArr.find((s: { status: string }) => s.status === 'failed')?.count ?? 0;
  const { profile } = useStore(state => state);
  const userAccess = useSetUserAccess();

  const getPayinsTableDef = () => ({
    className: ' --history-table',
    rowURL: '',
    rowKey: 'reference',
    emptyStateHeading: 'No records yet',
    emptyStateMessage: 'There are no pay-ins yet.',
    annotations: 'transaction(s)',
    fields: (iter: PayinType) => ({
      data: {
        status: (
          <>
            <span className={`status-pill smaller ${switchStatus(iter.status === 'requires_auth' ? 'pending' : iter.status)}`} />
            <span>{cardStatus[iter.status as keyof typeof cardStatus] || capitalizeRemovedash(iter.status || 'N/A')}</span>
          </>
        ),
        transaction_id: <span className="trxn-id">{iter.payment?.reference || iter.reference}</span>,
        merchant: <span className="merch-name">{capitalize(iter?.account?.name || 'Not Available')}</span>,
        type: iter.payment_source_type === 'reserved_bank_account' ? 'Deposit' : 'Pay-in',
        'Date / Time': (
          <>
            <span>{getDate(iter.transaction_date)}</span>
            <span className="annotation">{getTime(iter.transaction_date)}</span>
          </>
        ),
        Amount: (
          <>
            <span>
              <strong>{iter.amount ?? iter.amount}</strong>
            </span>
            <span className="annotation">{iter.currency}</span>
          </>
        )
      }
    })
  });

  const payins = getPayinsTableDef();
  const tableDataKeys = Object.keys(payins.fields({} as PayinType).data);

  return (
    <div className="content-i">
      <div className="content-box">
        <div className="os-tabs-w">
          <div className="row">
            <div className="">
              <button type="button" className="btn btn-link mb-2" onClick={() => history.goBack()}>
                <i className="os-icon os-icon-arrow-left7" />
                <span>Back to Customers</span>
              </button>
            </div>
          </div>
          <div className="customer-details-header">
            <span className="">
              <Icon name="merchantIconCircled" />
            </span>
            <h3 className="up-header light">{customerName || 'Not Available'}</h3>
          </div>
          <span className="form-desc no-underline mb-3">An overview of all this customers payins with the merchant</span>
          <section className="merchants-summary">
            <div>
              <p>Total Payins</p>
              <p>{totalPayins || '--'} </p>
            </div>
            <div>
              <p>Successful Payins</p>
              <p>{successfulPayins || '--'}</p>
            </div>
            <div>
              <p>Failed Payins</p>
              <p className="failed-transactions">{failedPayins || '--'}</p>
            </div>
          </section>
        </div>
        <Table
          className={payins.className}
          data={transactions || []}
          renderFields
          hasPagination
          loading={isLoadingTransaction}
          tableHeadings={tableDataKeys}
          annotation={payins.annotations}
          rowKey={payins.rowKey}
          rowURL={payins.rowURL}
          emptyStateHeading={payins.emptyStateHeading}
          tableWrapperClassName="table-responsive"
          cursors={paging}
          current={parseInt(page, 10)}
          filterType="pay-in"
          emptyStateMessage={payins.emptyStateMessage}
          filterHasAdvancedFilter={false}
          totalCount={paging?.count}
          filterExportAction={exportFile}
          filterShowExport={isAllowed(userAccess, ['pay_ins.export']) as boolean}
        >
          {payins.fields}
        </Table>
      </div>
      <LargeExportModal close={() => setLargeExportModal(false)} email={profile.email as string} visible={showLargeExportModal} />
    </div>
  );
};

export default CustomerTransactionDetails;
