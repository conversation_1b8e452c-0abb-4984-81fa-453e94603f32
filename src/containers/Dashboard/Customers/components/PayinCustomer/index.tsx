import React from 'react';
import { useHistory, useParams } from 'react-router-dom';

import Icon from '+containers/Dashboard/Shared/Icons';
import LargeExportModal from '+containers/Dashboard/Shared/LargeExportModal';
import Table from '+containers/Dashboard/Shared/Table';
import { useSetUserAccess } from '+hooks';
import { CustomerTransactionType } from '+types';
import { capitalize, formatAmount, isAllowed } from '+utils';
import useStore from '+store';

import useGetCustomerTransaction from '../hooks/useGetCustomerTransaction';

const PayinCustomers = () => {
  const { id } = useParams<{ id: string }>();
  const history = useHistory();
  const { profile } = useStore(state => state);
  const {
    isLoading,
    customers,
    page,
    paging,
    isLoadingCount,
    customersTransactionsCount,
    activeCurrency,
    exportFile,
    showCustomerLargeExportModal,
    setCustomerLargeExportModal
  } = useGetCustomerTransaction('payins');

  const handleRowClick = (row: CustomerTransactionType) => {
    history.push(`/dashboard/merchants/customers/${id}/details/${encodeURIComponent(row.customer_name)}/${row.customer_id}`);
  };
  const userAccess = useSetUserAccess();
  const payinCustomers = {
    className: ' --history-table',
    rowKey: 'customer_id',
    emptyStateHeading: 'No customer records yet',
    emptyStateMessage: 'There are no payin customers yet.',
    annotations: 'customer(s)',
    fields: (iter: CustomerTransactionType) => ({
      data: {
        'pay-in customers': (
          <span className="trxn-id">
            <span className="payins-customer-icon-wrap">
              <Icon name="merchantIconCircled" className="payins-customer-icon" />
            </span>
            {capitalize(iter.customer_name || 'Not Available')}
          </span>
        ),
        'customer email': iter.customer_email || 'Not Available',
        'total pay-in amount': (
          <>
            <span>
              <strong>{formatAmount(iter.amount)}</strong>
            </span>
            <span className="annotation">{activeCurrency}</span>
          </>
        )
      }
    })
  };

  const tableDataKeys = Object.keys(payinCustomers.fields({} as CustomerTransactionType).data);

  return (
    <div>
      <section className="merchants-summary">
        <div>
          <p>Total Customer</p>
          <p>{customersTransactionsCount}</p>
          <p>All pay-in customers</p>
        </div>
      </section>

      <div className="row">
        <div className="col-sm-12">
          <Table
            className={payinCustomers.className || ''}
            data={customers}
            loading={isLoading}
            rowKey={payinCustomers.rowKey}
            rowFn={handleRowClick}
            renderFields
            hasPagination
            tableHeadings={tableDataKeys}
            annotation={payinCustomers.annotations}
            current={parseInt(page, 10)}
            emptyStateHeading={payinCustomers.emptyStateHeading || ''}
            tableWrapperClassName="table-responsive"
            emptyStateMessage={payinCustomers.emptyStateMessage || ''}
            filterType="customers-transaction"
            cursors={paging}
            isLoadingCount={isLoadingCount}
            filterActiveCurrency={activeCurrency}
            showDateFilter={false}
            filterHasAdvancedFilter={false}
            type="customers-transaction"
            filterExportAction={exportFile}
            filterShowExport={isAllowed(userAccess, ['pay_ins.export']) as boolean}
            totalItems={customersTransactionsCount}
          >
            {payinCustomers?.fields}
          </Table>
        </div>
        <LargeExportModal
          close={() => setCustomerLargeExportModal(false)}
          email={profile.email as string}
          visible={showCustomerLargeExportModal}
        />
      </div>
    </div>
  );
};

export default PayinCustomers;
