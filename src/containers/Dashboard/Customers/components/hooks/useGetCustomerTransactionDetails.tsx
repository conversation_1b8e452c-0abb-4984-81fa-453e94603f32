import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

import { useFeedbackHandler, useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import { ApiType, FileFormatType } from '+types';
import { APIDownload, filteredOutObjectProperty, getDate, queriesParams } from '+utils';

const api = new APIRequest(process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE);

const useGetCustomerTransactionDetails = (apiType: ApiType) => {
  const [showLargeExportModal, setLargeExportModal] = useState(false);
  const { id: merchantId, customerId } = useParams<{ id: string; customerId: string }>();
  const { feedbackInit } = useFeedbackHandler();
  const searchQuery = useSearchQuery();
  const activeCurrencyTab = searchQuery.value.currency || 'NGN';
  const page = searchQuery.value.page || '1';
  const limit = searchQuery.value.limit || '10';
  const status = searchQuery.value.status || [];
  const paginationType = searchQuery.value.pageType || 'cursor';
  const sortingParams = JSON.stringify({
    status: typeof status === 'string' ? [status] : status,
    paginationType: paginationType,
    ...filteredOutObjectProperty(searchQuery.value, [
      queriesParams.tab,
      queriesParams.previousLimit,
      'customer_name',
      queriesParams.totalItems,
      queriesParams.dateFrom,
      queriesParams.dateTo,
      queriesParams.status
    ])
  });

  const { data, isFetching, refetch } = useQuery(
    [`${apiType.toUpperCase()}_CUSTOMER_DETAILS_${customerId}`],
    () => api.getCustomerTransactionDetails(apiType, merchantId, activeCurrencyTab, customerId),
    {
      keepPreviousData: true,
      onError: () => {
        let message = `There has been an error getting your ${apiType} customer details.`;
        if (searchQuery.value?.sorterType)
          message = `There has been an error in finding results for your ${searchQuery.value?.sorterType}. `;
        feedbackInit({
          message,
          type: 'danger',
          action: {
            action: () => refetch(),
            name: 'Try again'
          }
        });
      },
      enabled: !!customerId
    }
  );

  const exportFile = async (format: FileFormatType, close: () => void, fieldsToExport: string | string[]) => {
    try {
      const res = await api.exportTransactions(apiType, JSON.parse(sortingParams), format, activeCurrencyTab, fieldsToExport);
      if (res.status === 202) {
        setLargeExportModal(true);
      } else {
        const type = format === 'csv' ? 'csv' : 'xlsx';
        APIDownload(res, `Pay-ins at ${getDate(Date.now())}`, type);
        feedbackInit({
          title: 'Export Successful',
          message: (
            <>
              {' '}
              - Successfully exported <strong>payins transactions.</strong>
            </>
          ),
          type: 'success'
        });
      }
      close();
    } catch (error) {
      feedbackInit({
        title: 'Export Failed',
        message: `There has been an error exporting your pay-ins`,
        type: 'danger',
        componentLevel: true
      });
    }
  };
  const { data: transactions, isFetching: isFetchingTransaction } = useQuery(
    [
      `${apiType.toUpperCase()}_CUSTOMER_TRANSACTIONS`,
      filteredOutObjectProperty(searchQuery.value, [
        queriesParams.tab,
        queriesParams.previousLimit,
        queriesParams.dateFrom,
        queriesParams.dateTo
      ]),
      customerId
    ],
    () => api.getOneCustomerTransactions(apiType, customerId, activeCurrencyTab, limit, JSON.parse(sortingParams)),
    {
      keepPreviousData: true,
      onError: () => {
        let message = `There has been an error getting your ${apiType} customer transactions.`;
        if (searchQuery.value?.sorterType)
          message = `There has been an error in finding results for your ${searchQuery.value?.sorterType}. `;
        feedbackInit({
          message,
          type: 'danger',
          action: {
            action: () => refetch(),
            name: 'Try again'
          }
        });
      },
      enabled: !!customerId
    }
  );

  const allTransaction = transactions?.data;
  const paging = transactions?.paging;

  return {
    isLoading: isFetching,
    customerDetails: data ?? [],
    activeCurrencyTab,
    transactions: allTransaction ?? [],
    paging: paging ?? {},
    page,
    isLoadingTransaction: isFetchingTransaction,
    showLargeExportModal,
    setLargeExportModal,
    exportFile
  };
};

export default useGetCustomerTransactionDetails;
