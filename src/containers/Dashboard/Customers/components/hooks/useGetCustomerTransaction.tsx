import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

import { useFeedbackHandler, useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import { ApiType, FileFormatType } from '+types';
import { APIDownload, filteredOutObjectProperty, getDate, queriesParams } from '+utils';

import { customersTab } from '../..';

const api = new APIRequest(process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE);

const useGetCustomerTransaction = (apiType: ApiType) => {
  const [showCustomerLargeExportModal, setCustomerLargeExportModal] = useState(false);
  const { id } = useParams<{ id: string }>();
  const { feedbackInit } = useFeedbackHandler();
  const searchQuery = useSearchQuery();
  const activeCurrency = searchQuery.value.currency || 'NGN';
  const subTab = searchQuery.value.subTab || customersTab[`${apiType}-customers`];
  const page = searchQuery.value.page || '1';
  const paginationType = searchQuery.value.pageType || 'cursor';
  const limit = searchQuery.value.limit || '10';
  const sortingParams = JSON.stringify({
    paginationType: paginationType,
    ...filteredOutObjectProperty(searchQuery.value, [
      queriesParams.tab,
      queriesParams.limit,
      queriesParams.sorterType,
      queriesParams.status,
      queriesParams.page,
      queriesParams.totalItems,
      queriesParams.previousLimit,
      queriesParams.subTab,
      queriesParams.currency
    ])
  });

  const merchantId = id;

  const { data, isFetching, refetch } = useQuery(
    [
      `${apiType.toUpperCase()}_CUSTOMERS`,
      filteredOutObjectProperty(searchQuery.value, [queriesParams.tab, queriesParams.previousLimit]),
      activeCurrency
    ],
    () => api.getCustomerTransactions(apiType, merchantId, activeCurrency, limit, JSON.parse(sortingParams)),
    {
      keepPreviousData: true,
      onError: () => {
        let message = `There has been an error getting your ${apiType}.`;
        if (searchQuery.value?.sorterType)
          message = `There has been an error in finding results for your ${searchQuery.value?.sorterType}. `;
        feedbackInit({
          message,
          type: 'danger',
          action: {
            action: () => refetch(),
            name: 'Try again'
          }
        });
      },
      enabled: subTab === customersTab[`${apiType}-customers`]
    }
  );

  const { data: transactionsCount, isFetching: isLoadingCount } = useQuery(
    [`${apiType.toUpperCase()}_CUSTOMERS_COUNT`, apiType, activeCurrency, sortingParams],
    () => api.getCustomerTransactioncount(apiType, merchantId, activeCurrency),
    {
      onError: () => {
        feedbackInit({
          message: `There has been an error getting your ${apiType} customers count.`,
          type: 'danger'
        });
      }
    }
  );

  const exportFile = async (format: FileFormatType, close: () => void, fieldsToExport: string | string[]) => {
    try {
      const res = await api.exportCustomerTransactions(
        apiType,
        merchantId,
        activeCurrency,
        JSON.parse(sortingParams),
        format,
        fieldsToExport
      );
      if (res.status === 202) {
        setCustomerLargeExportModal(true);
        close();
      } else {
        const type = format === 'csv' ? 'csv' : 'excel';
        APIDownload(res, `Pay-ins at ${getDate(Date.now())}`, type);
        feedbackInit({
          title: 'Export Successful',
          message: (
            <>
              {' '}
              - Successfully exported <strong>payins transactions.</strong>
            </>
          ),
          type: 'success'
        });
        close();
      }
    } catch (error) {
      feedbackInit({
        title: 'Export Failed',
        message: `There has been an error exporting your ${apiType} customers`,
        type: 'danger',
        componentLevel: true
      });
    }
  };

  const customersTransactionsCount = transactionsCount?.data?.data?.count || [];
  const paging = data?.data?.paging;

  return {
    customers: data?.data?.data || [],
    isLoading: isFetching,
    page,
    paging,
    isLoadingCount,
    activeCurrency,
    customersTransactionsCount,
    exportFile,
    showCustomerLargeExportModal,
    setCustomerLargeExportModal
  };
};

export default useGetCustomerTransaction;
