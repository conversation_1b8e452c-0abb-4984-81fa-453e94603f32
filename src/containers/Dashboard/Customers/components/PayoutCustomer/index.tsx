import React from 'react';
import { useHistory, useParams } from 'react-router-dom';

import Icon from '+containers/Dashboard/Shared/Icons';
import LargeExportModal from '+containers/Dashboard/Shared/LargeExportModal';
import Table from '+containers/Dashboard/Shared/Table';
import { useSetUserAccess } from '+hooks';
import { CustomerTransactionType } from '+types';
import { capitalize, formatAmount, isAllowed } from '+utils';
import useStore from '+store';

import useGetCustomerTransaction from '../hooks/useGetCustomerTransaction';

const PayoutCustomers = () => {
  const { id } = useParams<{ id: string }>();
  const history = useHistory();
  const { profile } = useStore(state => state);
  const {
    isLoading,
    customers,
    page,
    paging,
    isLoadingCount,
    customersTransactionsCount,
    activeCurrency,
    exportFile,
    showCustomerLargeExportModal,
    setCustomerLargeExportModal
  } = useGetCustomerTransaction('payouts');

  const handleRowClick = (row: CustomerTransactionType) => {
    history.push(`/dashboard/merchants/customers/${id}/payout-details/${encodeURIComponent(row.customer_name)}/${row.customer_id}`);
  };
  const userAccess = useSetUserAccess();
  const payoutCustomers = {
    className: ' --history-table',
    rowKey: 'customer_id',
    emptyStateHeading: 'No customer records yet',
    emptyStateMessage: 'There are no payout customers yet.',
    annotations: 'customer(s)',
    fields: (iter: CustomerTransactionType) => ({
      data: {
        'payout customers': (
          <span className="trxn-id">
            <span className="payins-customer-icon-wrap">
              <Icon name="merchantIconCircled" />
            </span>
            {capitalize(iter.customer_name || 'Not Available')}
          </span>
        ),
        'customer email': iter.customer_email || 'Not Available',
        'total payout amount': (
          <>
            <span>
              <strong>{formatAmount(iter.amount)}</strong>
            </span>
            <span className="annotation">{activeCurrency}</span>
          </>
        )
      }
    })
  };

  const tableDataKeys = Object.keys(payoutCustomers.fields({} as CustomerTransactionType).data);

  return (
    <div>
      <section className="merchants-summary">
        <div>
          <p>Total Customer</p>
          <p>{customersTransactionsCount}</p>
          <p>All payout customers</p>
        </div>
      </section>

      <div className="row">
        <div className="col-sm-12">
          <Table
            className={payoutCustomers.className || ''}
            data={customers}
            loading={isLoading}
            rowKey={payoutCustomers.rowKey}
            rowFn={handleRowClick}
            renderFields
            hasPagination
            tableHeadings={tableDataKeys}
            annotation={payoutCustomers.annotations}
            current={parseInt(page, 10)}
            emptyStateHeading={payoutCustomers.emptyStateHeading || ''}
            tableWrapperClassName="table-responsive"
            emptyStateMessage={payoutCustomers.emptyStateMessage || ''}
            filterType="customers-transaction"
            cursors={paging}
            isLoadingCount={isLoadingCount}
            filterActiveCurrency={activeCurrency}
            showDateFilter={false}
            filterHasAdvancedFilter={false}
            type="customers-transaction"
            filterShowExport={isAllowed(userAccess, ['payouts.export']) as boolean}
            totalItems={customersTransactionsCount}
            filterExportAction={exportFile}
          >
            {payoutCustomers?.fields}
          </Table>
        </div>
        <LargeExportModal
          close={() => setCustomerLargeExportModal(false)}
          email={profile.email as string}
          visible={showCustomerLargeExportModal}
        />
      </div>
    </div>
  );
};

export default PayoutCustomers;
