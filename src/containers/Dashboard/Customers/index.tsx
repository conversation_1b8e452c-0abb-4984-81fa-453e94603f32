import React, { useEffect, useState } from 'react';
import { useMutation } from 'react-query';
import { useParams } from 'react-router-dom';

import { useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import { capitalizeRemovedash, history } from '+utils';

import Icon from '../Shared/Icons';
import PayinCustomers from './components/PayinCustomer';
import PayoutCustomers from './components/PayoutCustomer';

import './index.scss';

export const customersTab = {
  'payins-customers': 'payins-customers',
  'payouts-customers': 'payouts-customers'
} as const;

const baseURL = process.env.REACT_APP_MIDDLEWARE_API_BASE;
const apiRequest = new APIRequest(baseURL);

const AllCustomers = () => {
  const { id } = useParams<{ id: string }>();
  const searchQuery = useSearchQuery();
  const subTab = searchQuery.value.subTab || customersTab['payins-customers'];
  const [merchant, setMerchant] = useState<{ name?: string; id?: string; kyc_status?: string; status?: string }>({});
  const merchantMutation = useMutation((id: string) => apiRequest.getMerchant(id));

  useEffect(() => {
    if (id) {
      merchantMutation.mutateAsync(id).then(setMerchant);
    }
  }, [id]);

  return (
    <div className="content-i">
      <div className="content-box">
        <div className="">
          <button type="button" className="back-merchant" onClick={() => history.goBack()}>
            <i className="os-icon os-icon-arrow-left7" />
            <span>Go Back</span>
          </button>
        </div>
        {(merchant.name || merchant.id || merchant.kyc_status) && (
          <div className="mb-4">
            <h1 id="merchant-name" className="up-header light">
              <span className="customer-name">{merchant.name}</span>
              {merchant?.kyc_status === 'unverified' || merchant?.status === 'unsupported' ? (
                <section className="merchant-details-unverified-banner">
                  <Icon name="warningOrange" fontSize={15} />{' '}
                  <span>{merchant?.status === 'unsupported' ? 'Unsupported' : 'Unverified'}</span>
                </section>
              ) : merchant?.kyc_status === 'verified' ? (
                <section className="merchant-details-verified-banner">
                  <Icon name="checkRounded" width={15} height={15} />
                  <span>Verified</span>
                </section>
              ) : null}
            </h1>
            {merchant.id && <div className="user-profile-id">ID: {merchant.id}</div>}
          </div>
        )}
        <section className="os-tabs-w">
          <div className="os-tabs-controls os-tabs-complex mb-4">
            <ul className="nav nav-tabs">
              {Object.entries(customersTab).map(([key, value]) => (
                <li className="nav-item" key={key}>
                  <button
                    type="button"
                    className={`nav-link ${subTab === value ? 'active' : ''}`}
                    onClick={() => {
                      searchQuery.setQuery({ subTab: value }, true);
                    }}
                  >
                    {capitalizeRemovedash(value)}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </section>
        {subTab === customersTab['payins-customers'] && <PayinCustomers />}
        {subTab === customersTab['payouts-customers'] && <PayoutCustomers />}
      </div>
    </div>
  );
};

const Customers = () => {
  return <AllCustomers />;
};

export default Customers;
