@import '+styles/kpy-custom/variables';
.generate-wrapper {
  .report-text-column-process{
    font-weight: 600;
    color: $grey-dark-light;
    margin-left: 18px !important;
    padding-top: 16px !important;
  }
  .search-component {
    width: 50%;
    margin-bottom: 50px;
  }
  .empty-text-container {
    display: flex;
    margin-top: 30px;
    cursor: pointer;
  }
  .empty-text {
    color: $kpyblue-meduim-full;
    font-size: 19px;
    font-weight: 500;
  }
  .report-text-column-retry{
    font-weight: 600;
    color: $grey-dark-light;
    margin-left: 11px !important;
     margin-right: 5px;
    padding-top: 13px !important
  }
  .report-text-column-sucess{
    font-weight: 600;
    color: $grey-dark-light;
    margin-left: -16px !important;
    padding-top: 13px !important
  }
  
  .date-created-column{
    color:$grey-light-medium;
    margin-left: 5px;
  }
  .center-tooltip-div{
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: justify;
    width: 100%;
    height: 100%;
  }
}
