import React from 'react';
import { render, screen } from '@testing-library/react';
import selectEvent from 'react-select-event';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { IGenerateReportModalProps } from '+types';

import GenerateReportModal from '../GenerateReportModal';

const MockedGenerateReportModal = ({
  visible,
  closeModal,
  generateReportMutation,
  generateReportSuccessResponse,
  isGenerateReporError
}: IGenerateReportModalProps) => {
  return (
    <MockIndex>
      <GenerateReportModal
        visible={visible}
        closeModal={closeModal}
        generateReportMutation={generateReportMutation}
        generateReportSuccessResponse={generateReportSuccessResponse}
        isGenerateReporError={isGenerateReporError}
      />
    </MockIndex>
  );
};

describe('GenerateReportModal', () => {
  test('If the modal is accessible', async () => {
    const { container } = render(
      <MockedGenerateReportModal
        visible
        closeModal={vi.fn()}
        generateReportMutation={vi.fn()}
        generateReportSuccessResponse={false}
        isGenerateReporError={false}
      />
    );

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('Renders the modal and shows the initial form view and Generate Report Button is not enable', () => {
    render(
      <MockedGenerateReportModal
        visible
        closeModal={vi.fn()}
        generateReportMutation={vi.fn()}
        generateReportSuccessResponse={false}
        isGenerateReporError={false}
      />
    );

    expect(screen.getByText('Generate Processor Report')).toBeInTheDocument();
    expect(screen.getByLabelText('Currency Type')).toBeInTheDocument();
    expect(screen.getByLabelText('Processor Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Date & Time Range')).toBeInTheDocument();
    expect(screen.getByTestId('second-button')).toBeDisabled();
  });
  test('Generates report with correct inputs and generate button is enabled', async () => {
    render(
      <MockedGenerateReportModal
        visible
        closeModal={vi.fn()}
        generateReportMutation={vi.fn()}
        generateReportSuccessResponse={false}
        isGenerateReporError={false}
      />
    );
    await selectEvent.select(screen.getByLabelText(/Currency Type/i), ['NGN']);
    await screen.findByText('NGN');
    await selectEvent.select(screen.getByLabelText(/Processor Name/i), ['Sterling Bank']);
    await screen.findByText('Sterling Bank');
    const dateRange = screen.getByLabelText(/Date & Time Range/i);
    await userEvent.click(dateRange);
    await screen.findByText(/Date & Time Range/i);
    const startCalendar = screen.getByTestId('start-calendar');
    const startElements = startCalendar.querySelectorAll('.react-calendar__month-view__days__day');
    const endCalendar = screen.getByTestId('end-calendar');
    const endElements = endCalendar.querySelectorAll('.react-calendar__month-view__days__day');
    expect(startElements.length).toBeGreaterThan(0);
    expect(endElements.length).toBeGreaterThan(0);
    expect(startElements[1]).toBeInTheDocument();
    expect(endElements[1]).toBeInTheDocument();
    await userEvent.click(startElements[1]);
    await userEvent.click(endElements[1]);
    const getDateRangeButton = screen.getByTestId('second-button');
    expect(getDateRangeButton).toBeEnabled();
    await userEvent.click(getDateRangeButton);
    const generateReportButton = screen.getByText('Generate Report');
    expect(generateReportButton).toBeInTheDocument();
    expect(generateReportButton).toBeEnabled();
  });

  test('Renders the modal with a succesful response', () => {
    render(
      <MockedGenerateReportModal
        visible
        closeModal={vi.fn()}
        generateReportMutation={vi.fn()}
        generateReportSuccessResponse
        isGenerateReporError={false}
      />
    );

    expect(screen.getByText('Success')).toBeInTheDocument();
    expect(
      screen.getByText('Your processor report has been generated. Check your work email for link to download report.')
    ).toBeInTheDocument();
  });
  test('Renders the modal with a error response', () => {
    render(
      <MockedGenerateReportModal
        visible
        closeModal={vi.fn()}
        generateReportMutation={vi.fn()}
        generateReportSuccessResponse={false}
        isGenerateReporError
      />
    );

    expect(screen.getByText('Error')).toBeInTheDocument();
    expect(screen.getByText('There has been an error in generating this report.Please try again')).toBeInTheDocument();
  });
});
