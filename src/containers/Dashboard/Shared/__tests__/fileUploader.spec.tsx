/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import FileUploader from '../FileUploader';

const initialProps = {
  text: 'Browse file to upload',
  maxFileSizeMb: 10,
  infoText: `Max file size 10 MB`,
  uploaded: false,
  uploading: false,
  onChange: vi.fn(),
  clearUploads: vi.fn(),
  fileName: ''
};

const renderComponent = (props = {}) => ({
  user: userEvent,
  ...render(<FileUploader {...initialProps} {...props} />)
});

describe('file uploader', () => {
  it('renders', () => {
    renderComponent();

    expect(screen.getByText(/browse file to upload/i)).toBeInTheDocument();
  });

  it('is accessible', async () => {
    const { container } = renderComponent();
    const result = await axe(container);

    expect(result).toHaveNoViolations();
  });

  it('can upload files within size limit', async () => {
    const file = new File(['hello'], 'hello.png', { type: 'image/png' });

    const { user } = renderComponent({ fileName: 'hello' });
    const input = screen.getByLabelText(/browse file to upload/i) as HTMLInputElement;
    await user.upload(input, file);

    expect(input?.files?.[0]).toStrictEqual(file);
    expect(input?.files?.item(0)).toStrictEqual(file);
    expect(input.files).toHaveLength(1);
    expect(screen.queryByText(/file size exceeds/i)).not.toBeInTheDocument();
  });

  it('shows error for files exceeding size limit', async () => {
    // 11MB file, limit is 10MB
    const bigFile = new File([new ArrayBuffer(11 * 1024 * 1024)], 'big.pdf', { type: 'application/pdf' });
    const onChange = vi.fn();
    const { user } = renderComponent({ onChange });
    const input = screen.getByLabelText(/browse file to upload/i) as HTMLInputElement;
    await user.upload(input, bigFile);
    expect(screen.getByText(/file size exceeds the maximum allowed size/i)).toBeInTheDocument();
    expect(onChange).not.toHaveBeenCalled();
  });

  it('shows error for drag-and-drop files exceeding size limit', async () => {
    const bigFile = new File([new ArrayBuffer(11 * 1024 * 1024)], 'big.pdf', { type: 'application/pdf' });
    const onChange = vi.fn();
    renderComponent({ onChange });
    const label = screen.getByText(/browse file to upload/i).closest('label');
    // Simulate drag over
    label && fireEvent.dragEnter(label);
    // Simulate drop with file
    label &&
      fireEvent.drop(label, {
        dataTransfer: {
          files: [bigFile],
          clearData: vi.fn()
        }
      });
    expect(screen.getByText(/file size exceeds the maximum allowed size/i)).toBeInTheDocument();
    expect(onChange).not.toHaveBeenCalled();
  });

  it('displays error message when file size exceeds maxFileSizeMb', async () => {
    const bigFile = new File([new ArrayBuffer(11 * 1024 * 1024)], 'big.pdf', { type: 'application/pdf' });
    const { user } = renderComponent();
    const input = screen.getByLabelText(/browse file to upload/i) as HTMLInputElement;
    await user.upload(input, bigFile);
    expect(screen.getByText('File size exceeds the maximum allowed size of 10MB.')).toBeInTheDocument();
  });

  it('displays uploaded file name when uploaded prop is true', () => {
    renderComponent({ uploaded: true, fileName: 'test-document.pdf' });
    expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    expect(screen.getByTitle('file')).toBeInTheDocument();
  });

  it('calls clearUploads when cancel icon is clicked', async () => {
    const clearUploads = vi.fn();
    const { user } = renderComponent({ uploaded: true, fileName: 'test-document.pdf', clearUploads });
    const cancelIcon = screen.getByTitle('cancel uploads');
    await user.click(cancelIcon);
    expect(clearUploads).toHaveBeenCalledTimes(1);
  });

  it('has an input associated with the label', () => {
    renderComponent();

    const input = screen.getByLabelText(/browse file to upload/i) as HTMLInputElement;
    expect(input).toBeInTheDocument();
    expect(input.type).toBe('file');
  });

  it('changes text when drag is active', () => {
    renderComponent();
    const label = screen.getByText(/browse file to upload/i).closest('label');

    label && fireEvent.dragEnter(label);

    expect(screen.getByText('Drop file here...')).toBeInTheDocument();

    label && fireEvent.dragLeave(label);

    expect(screen.getByText('Browse file to upload')).toBeInTheDocument();
  });

  it('adds drag-active class to label when dragging', () => {
    renderComponent();
    const label = screen.getByText(/browse file to upload/i).closest('label');

    expect(label?.classList.contains('drag-active')).toBe(false);

    label && fireEvent.dragEnter(label);

    expect(label?.classList.contains('drag-active')).toBe(true);
  });
});
