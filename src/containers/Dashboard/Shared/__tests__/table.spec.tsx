/* eslint-disable react/no-children-prop */
/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { history } from '+utils';

import Table from '../Table';

const mockHistoryPush = vi.spyOn(history, 'push').mockImplementation(str => str);

const rowData = [
  { name: '<PERSON>', age: 30, email: '<EMAIL>' },
  { name: '<PERSON>', age: 25, email: 'jane<PERSON><EMAIL>' },
  { name: '<PERSON>', age: 40, email: '<EMAIL>' }
];
const defaultProps = {
  header: 'My Table',
  className: 'my-table',
  tableHeadings: ['Name', 'Age', 'Email'],
  loading: false,
  totalItems: 20,
  pageSize: 10,
  current: 1,
  limitAction: vi.fn(),
  rowFn: vi.fn(),
  annotation: 'Showing 1-10 of 20',
  emptyStateHeading: 'No results found',
  emptyStateMessage: 'Please try again later',
  loaderBackground: '#fff',
  hideTableHeadings: false,
  isTableBordered: true,
  hideTable: false,
  hasPagination: true,
  renderFields: false,
  children: <div>Table Content</div>,
  data: rowData
};
function MockedTable() {
  return (
    <MockIndex>
      <Table {...defaultProps} />
    </MockIndex>
  );
}

describe('Table', () => {
  it('renders table headings', () => {
    render(<MockedTable />);
    const headings = screen.getAllByTestId('columnheader');
    expect(headings).toHaveLength(3);
    expect(headings[0]).toHaveTextContent('Name');
    expect(headings[1]).toHaveTextContent('Age');
    expect(headings[2]).toHaveTextContent('Email');
  });

  it('renders table content', () => {
    render(<MockedTable />);
    const content = screen.getByText('Table Content');
    expect(content).toBeInTheDocument();
  });

  it('calls limitAction when pagination limit is changed', () => {
    render(
      <MockIndex>
        <Table
          {...defaultProps}
          children={iter => ({ data: { name: <div>{iter.name}</div>, age: <div>{iter.age}</div>, email: <div>{iter.email}</div> } })}
          renderFields
        />
      </MockIndex>
    );
    const limitSelect = screen.getByTestId('pagination_limit');
    fireEvent.change(limitSelect, { target: { value: '25' } });
    expect(defaultProps.limitAction).toHaveBeenCalledWith(25);
  });

  it('calls rowFn when a row is clicked', () => {
    render(
      <MockIndex>
        <Table
          {...defaultProps}
          children={iter => ({ data: { name: <div>{iter.name}</div>, age: <div>{iter.age}</div>, email: <div>{iter.email}</div> } })}
          renderFields
          data={rowData}
          rowURL="/users"
          rowKey="email"
        />
      </MockIndex>
    );
    const row = screen.getByText('<EMAIL>');
    fireEvent.click(row);
    expect(defaultProps.rowFn).toHaveBeenCalled();
  });

  it('should render an empty state if there is no data and hideTable is false', () => {
    render(
      <MockIndex>
        <Table
          header="Test Table"
          renderFields
          hideTable={false}
          data={[]}
          emptyStateHeading="No Data"
          emptyStateMessage="There is no data."
        />
      </MockIndex>
    );
    expect(screen.getByText('No Data')).toBeInTheDocument();
    expect(screen.getByText('There is no data.')).toBeInTheDocument();
  });

  it('should not render the table headings if hideTableHeadings is true', () => {
    render(
      <MockIndex>
        <Table
          header="Test Table"
          children={iter => ({ data: { name: <div>{iter.name}</div> } })}
          renderFields
          tableHeadings={defaultProps.tableHeadings}
          data={rowData}
          hideTable
        />
      </MockIndex>
    );
    expect(screen.queryByText('Name')).not.toBeInTheDocument();
    expect(screen.queryByText('Age')).not.toBeInTheDocument();
    expect(screen.queryByText('Country')).not.toBeInTheDocument();
  });

  it('should render the correct number of rows', () => {
    render(
      <MockIndex>
        <Table
          header="Test Table"
          children={iter => ({ data: { name: <div>{iter.name}</div> } })}
          renderFields
          tableHeadings={defaultProps.tableHeadings}
          data={rowData}
        />
      </MockIndex>
    );
    expect(screen.getAllByRole('button').length).toBe(6);
  });

  it('should call the rowFn when a row is clicked and rowFn is defined', () => {
    const rowFn = vi.fn();
    render(
      <MockIndex>
        <Table
          header="Test Table"
          children={iter => ({ data: { name: <div>{iter.name}</div> } })}
          renderFields
          tableHeadings={defaultProps.tableHeadings}
          data={[{ name: 'John' }]}
          rowFn={rowFn}
        />
      </MockIndex>
    );
    fireEvent.click(screen.getByText('John'));
    expect(rowFn).toHaveBeenCalled();
  });

  it('should navigate to the correct rowURL when a row is clicked and rowURL is defined', () => {
    const rowURL = '/users';
    render(
      <MockIndex>
        <Table
          header="Test Table"
          children={iter => ({ data: { name: <div>{iter.name}</div> } })}
          renderFields
          tableHeadings={defaultProps.tableHeadings}
          data={[{ name: 'John' }]}
          rowURL={rowURL}
          rowKey="name"
        />
      </MockIndex>
    );
    fireEvent.click(screen.getByText('John'));
    expect(mockHistoryPush).toHaveBeenCalledWith('/users/John', null);
  });

  it('should not navigate to a rowURL if rowKey is not defined', () => {
    const rowURL = '/users';
    render(
      <MockIndex>
        <Table
          header="Test Table"
          children={iter => ({ data: { name: <div>{iter}</div> } })}
          renderFields
          tableHeadings={defaultProps.tableHeadings}
          data={['John']}
          rowURL={rowURL}
        />
      </MockIndex>
    );
    fireEvent.click(screen.getByText('John'));
    expect(mockHistoryPush).toHaveBeenCalledWith('/users/', null);
  });

  it('should render a loading placeholder if loading is true', () => {
    render(
      <MockIndex>
        <Table header="Test Table" tableHeadings={defaultProps.tableHeadings} loading />
      </MockIndex>
    );
    expect(screen.getByTestId('loading-placeholder')).toBeInTheDocument();
  });

  it('should not render a loading placeholder if loading is false', () => {
    render(
      <MockIndex>
        <Table header="Test Table" tableHeadings={defaultProps.tableHeadings} data={rowData} />
      </MockIndex>
    );
    expect(screen.queryByTestId('loading-placeholder')).not.toBeInTheDocument();
  });

  it('should render a pagination component if hasPagination is true', async () => {
    render(
      <MockIndex>
        <Table
          header="Test Table"
          children={iter => ({ data: { name: <div>{iter.name}</div>, age: <div>{iter.age}</div>, email: <div>{iter.email}</div> } })}
          tableHeadings={defaultProps.tableHeadings}
          data={rowData}
          hasPagination
          renderFields
        />
      </MockIndex>
    );

    expect(screen.getByTestId('pagination-component')).toBeInTheDocument();
  });
});
