import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';

import { useSearchQuery } from '+hooks';

import AdvancedCursorPagination from '../AdvancedCursorPagination';

vi.mock('+hooks', () => ({
  useSearchQuery: vi.fn()
}));

describe('AdvancedCursorPagination', () => {
  const mockSetQuery = vi.fn();
  const defaultMockSearchQuery = {
    value: {
      totalItems: '100',
      page: '1',
      limit: '10',
      previousLimit: '10'
    },
    setQuery: mockSetQuery
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useSearchQuery as Mock).mockReturnValue(defaultMockSearchQuery);
  });

  it('renders pagination with correct buttons', () => {
    render(<AdvancedCursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    expect(screen.getByLabelText('First Page')).toBeInTheDocument();
    expect(screen.getByLabelText('Previous Page')).toBeInTheDocument();
    expect(screen.getByLabelText('Next Page')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Page')).toBeInTheDocument();
    expect(screen.getByText(/transactions per page/i)).toBeInTheDocument();
    expect(screen.getByText(/back to top/i)).toBeInTheDocument();
  });

  it.skip('displays annotation correctly when only one item', () => {
    (useSearchQuery as Mock).mockReturnValue({
      ...defaultMockSearchQuery,
      value: { ...defaultMockSearchQuery.value, totalItems: '1' }
    });

    render(<AdvancedCursorPagination cursors={{ total_items: 1, page_size: 10 }} totalCount={1} annotation="transactions" />);

    expect(screen.getByText('transactions')).toBeInTheDocument(); // singular form
  });

  it('disables previous and first page buttons when no previous cursor', () => {
    render(<AdvancedCursorPagination cursors={{ next_cursor: 2, total_items: 100, page_size: 10 }} totalCount={10} />);

    const firstButton = screen.getByLabelText('First Page');
    const prevButton = screen.getByLabelText('Previous Page');
    expect(firstButton).toBeDisabled();
    expect(prevButton).toBeDisabled();
  });

  it('disables next and last page buttons when no next cursor', () => {
    render(<AdvancedCursorPagination cursors={{ previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const nextButton = screen.getByLabelText('Next Page');
    const lastButton = screen.getByLabelText('Last Page');
    expect(nextButton).toBeDisabled();
    expect(lastButton).toBeDisabled();
  });

  it('enables all pagination buttons when both cursors are available', () => {
    render(<AdvancedCursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const firstButton = screen.getByLabelText('First Page');
    const prevButton = screen.getByLabelText('Previous Page');
    const nextButton = screen.getByLabelText('Next Page');
    const lastButton = screen.getByLabelText('Last Page');

    expect(firstButton).not.toBeDisabled();
    expect(prevButton).not.toBeDisabled();
    expect(nextButton).not.toBeDisabled();
    expect(lastButton).not.toBeDisabled();
  });

  it('displays tooltip when not on first page', () => {
    (useSearchQuery as Mock).mockReturnValue({
      ...defaultMockSearchQuery,
      value: { ...defaultMockSearchQuery.value, page: '2' }
    });

    render(<AdvancedCursorPagination cursors={{ next_cursor: 3, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    expect(screen.getByText(/You can only choose the number of items to display from the first page./i)).toBeInTheDocument();
  });

  it('disables limit field when not on first page', () => {
    (useSearchQuery as Mock).mockReturnValue({
      ...defaultMockSearchQuery,
      value: { ...defaultMockSearchQuery.value, page: '2' }
    });

    render(<AdvancedCursorPagination cursors={{ next_cursor: 3, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const limitField = screen.getByTestId('pagination_limit');
    expect(limitField).toBeDisabled();
  });

  it('calls setQuery with correct params on next button click', async () => {
    render(<AdvancedCursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const nextButton = screen.getByLabelText('Next Page');
    await userEvent.click(nextButton);

    expect(mockSetQuery).toHaveBeenCalledWith({
      pageAction: 'next',
      pageCursorValue: '2'
    });
  });

  it('calls setQuery with correct params on previous button click', async () => {
    render(<AdvancedCursorPagination cursors={{ next_cursor: 3, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const prevButton = screen.getByLabelText('Previous Page');
    await userEvent.click(prevButton);

    expect(mockSetQuery).toHaveBeenCalledWith({
      pageAction: 'previous',
      pageCursorValue: '1'
    });
  });

  it('calls setQuery with correct params on first page button click', async () => {
    render(<AdvancedCursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const firstButton = screen.getByLabelText('First Page');
    await userEvent.click(firstButton);

    expect(mockSetQuery).toHaveBeenCalledWith({
      pageAction: 'start',
      pageCursorValue: undefined,
      limit: '10'
    });
  });

  it('calls setQuery with correct params on last page button click', async () => {
    render(<AdvancedCursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const lastButton = screen.getByLabelText('Last Page');
    await userEvent.click(lastButton);

    expect(mockSetQuery).toHaveBeenCalledWith({
      pageAction: 'end',
      pageCursorValue: undefined,
      limit: '10',
      previousLimit: '10'
    });
  });

  it('calls setQuery with correct params when limit is changed', async () => {
    render(<AdvancedCursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const limitSelect = screen.getByTestId('pagination_limit');
    await userEvent.selectOptions(limitSelect, '25');

    expect(mockSetQuery).toHaveBeenCalledWith({
      limit: '25',
      pageAction: 'start',
      pageCursorValue: '',
      page: '1',
      previousLimit: '25'
    });
  });

  it('renders empty section when no items', () => {
    (useSearchQuery as Mock).mockReturnValue({
      ...defaultMockSearchQuery,
      value: { ...defaultMockSearchQuery.value, totalItems: '0' }
    });

    render(<AdvancedCursorPagination cursors={{ total_items: 0, page_size: 10 }} totalCount={0} />);

    expect(screen.queryByText(/back to top/i)).not.toBeInTheDocument();
    expect(screen.getByTestId('pagination-component')).toBeInTheDocument();
  });

  it('applies disabled class when disabled prop is true', () => {
    render(
      <AdvancedCursorPagination
        cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }}
        totalCount={10}
        disabled={true}
      />
    );

    const paginationContainer = screen.getByTestId('pagination-component');
    expect(paginationContainer).toHaveClass('disabled');
  });

  it('shows tooltip when disabled and totalItems > pageSize', () => {
    render(
      <AdvancedCursorPagination
        cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }}
        totalCount={20}
        disabled={true}
      />
    );

    expect(screen.getByText(/This action is disabled because you are carrying out a bulk action on this page./i)).toBeInTheDocument();
  });
});
