import React from 'react';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import dayjs from 'dayjs';
import { vi } from 'vitest';

import { CustomDateRangeCalendar } from '../CustomDateRangeCalendar';

const mockClose = vi.fn();
const mockSetSelectedDateRange = vi.fn();

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useLocation: vi.fn().mockReturnValue({ pathname: '/dashboard/pay-ins' })
  };
});

describe('CustomDateRangeCalendar Component', () => {
  test('renders component with default props', () => {
    render(
      <CustomDateRangeCalendar
        visible={true}
        close={mockClose}
        selectedDate={{ startDate: '2025-02-17', endDate: '2025-02-18' }}
        setSelectedDateRange={mockSetSelectedDateRange}
      />
    );

    expect(screen.getByText('Custom Date Range')).toBeInTheDocument();
    expect(screen.getByLabelText('startDate')).toBeInTheDocument();
    expect(screen.getByLabelText('endDate')).toBeInTheDocument();
  });

  test('changes start date when selecting from calendar', async () => {
    render(
      <CustomDateRangeCalendar
        visible={true}
        close={mockClose}
        selectedDate={{ startDate: '2025-02-18', endDate: '2025-03-18' }}
        setSelectedDateRange={mockSetSelectedDateRange}
      />
    );

    const startCalendar = screen.getByTestId('start-calendar');
    expect(startCalendar).toBeInTheDocument();

    const calendarContainer = within(startCalendar);
    const newDate = '2025-02-20';
    expect(await screen.findByText(/February 2025/)).toBeInTheDocument();

    const dateButton = calendarContainer.getByLabelText('February 20, 2025');
    await userEvent.click(dateButton);

    expect(await screen.findAllByText(/February 2025/)).toHaveLength(1);

    expect(screen.getByLabelText('startDate')).toHaveValue(dayjs(newDate).format('ddd, D MMM YYYY'));
  });

  test('calls setSelectedDateRange and closes on confirm', async () => {
    render(
      <CustomDateRangeCalendar
        visible={true}
        close={mockClose}
        selectedDate={{ startDate: '2025-02-17', endDate: '2025-02-18' }}
        setSelectedDateRange={mockSetSelectedDateRange}
      />
    );

    const setDateButton = screen.getByText(/Set Date/i);
    await userEvent.click(setDateButton);

    expect(mockClose).toHaveBeenCalled();
    expect(mockSetSelectedDateRange).toHaveBeenCalledWith(
      expect.objectContaining({
        startDate: '2025-02-17',
        endDate: '2025-02-18'
      })
    );
  });

  test('renders with time range when showTimeRange is true', () => {
    render(
      <CustomDateRangeCalendar
        visible={true}
        close={mockClose}
        selectedDate={{
          startDate: '',
          endDate: ''
        }}
        setSelectedDateRange={mockSetSelectedDateRange}
        showTimeRange={true}
        showMaxEndDate={true}
      />
    );

    expect(screen.getByText('Date & Time Range')).toBeInTheDocument();
    expect(screen.getByText('From (Start Date & Time)')).toBeInTheDocument();
    expect(screen.getByText('To (End Date & Time)')).toBeInTheDocument();
    expect(screen.getByTestId('custom-time-range')).toBeInTheDocument();
  });

  test('display timerange when tableType is pay-in', () => {
    render(
      <CustomDateRangeCalendar
        visible={true}
        close={mockClose}
        selectedDate={{
          startDate: '2025-02-18',
          endDate: '2025-02-18'
        }}
        setSelectedDateRange={mockSetSelectedDateRange}
        showTimeRange={true}
        showMaxEndDate={true}
        tableType="pay-in"
      />
    );

    expect(screen.getByText('From (Start Date & Time)')).toBeInTheDocument();
    expect(screen.getByText('To (End Date & Time)')).toBeInTheDocument();
  });
});

test('does not display timerange when tableType is not pay-in', () => {
  render(
    <CustomDateRangeCalendar
      visible={true}
      close={mockClose}
      selectedDate={{
        startDate: '2025-02-18',
        endDate: '2025-02-18'
      }}
      setSelectedDateRange={mockSetSelectedDateRange}
      showTimeRange={false}
      showMaxEndDate={true}
    />
  );

  expect(screen.queryByText('From (Start Date & Time)')).not.toBeInTheDocument();
  expect(screen.queryByText('To (End Date & Time)')).not.toBeInTheDocument();
});
