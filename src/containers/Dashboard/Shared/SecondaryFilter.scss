@import '+styles/kpy-custom/variables';

.secondary-filter--container {
  display: flex;
  width: 80%;

  &.--full-width {
    width: 96%;

    @media (max-width: 1230px) {
      width: 94%;
    }
  }
  &.--settlement-table-search {
    width: 50%;
    margin-right: 7px;
  }
}

//secondary search
.secondary-search {
  position: relative;
  width: 80%;
  &--inputs {
    display: flex;
    width: 100%;
    div {
      margin-right: 0 !important;

      input {
        cursor: pointer;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;

        &:read-only {
          background-color: #fff !important;
        }
      }
      i {
        width: 1.05rem;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 0.625rem;
        left: unset !important;
        font-size: 0.7rem;
        font-weight: 900;
      }
    }
    & > input {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  &--separator {
    width: 100%;
    background-color: #dde2ec;
    height: 1px;
  }

  &--option {
    position: absolute;
    background-color: white;
    box-shadow: 0 6px 32px 0 #5f697729;
    width: 300px;
    padding: 20px 0;
    border-radius: 10px;
    z-index: 1000;
    top: 45px;

    @media (min-width: $breakpoint-desktop) {
      padding: 0.3rem !important;
      width: 350px;
    }

    ul {
      list-style-type: none;
      padding-left: 2px;

      li {
        padding: 2px 11px;
        cursor: pointer;
        font-weight: 400;
        font-size: 13px;
        color: #414f5f;
        margin-bottom: 5px;

        span {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
    }
    &-tab {
      max-height: 35px;
      padding: 4px;
      border-radius: 6px;
      margin-inline: 10px;
      background-color: #f1f6fa;
      overflow-x: scroll;
      margin-block: 10px;
      margin-top: 5px;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }

      div {
        display: flex;
        width: max-content;
        column-gap: 5px;
      }
    }
    &-tabItem {
      align-items: center;
      display: flex;
      padding: 2px 8px;
      max-height: 28px;
      border-radius: 4px;
      transition: hover 0.5s ease-in-out;

      p {
        margin: 0;
        font-size: 13px;
      }

      &:hover {
        background-color: #fff;
        box-shadow: 0 3px 7px 0 #3e4b5b52;
      }

      &--active {
        background: #fff;
        box-shadow: 0px 3px 7px 0px #3e4b5b52;
        padding: 2px 8px;
        font-weight: 600;
        border-radius: 4px;

        p {
          color: #2376f3;
          margin: 0;
          font-size: 13px;
        }
      }
    }
  }
  #search.form-control {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 99%;
  }
}

//secondary-date-filter
.full-width-datefilter {
  width: 100% !important;
}
.settlement-table-margin-datefilter {
  margin-right: 70px;
}
.secondary-datefilter {
  display: flex;
  width: 30%;
  position: relative;

  & > .form-group.--search-container {
    input {
      padding-right: 1.8rem;
    }
    i {
      width: 1.05rem;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 0.625rem;
      left: unset !important;
      font-size: 0.7rem;
      font-weight: 900;
    }
  }

  input {
    cursor: pointer;

    &:read-only {
      background-color: #fff !important;
    }
  }

  &--option {
    z-index: 1000;
    position: absolute;
    background-color: white;
    top: 40px;
    box-shadow: 0 5px 7px 0 #7e8eb11a;
    width: 190px;
    padding: 10px 0 0 0px;
    border-radius: 10px;

    @media (min-width: $breakpoint-desktop) {
      padding: 0.3rem !important;
    }

    ul {
      list-style-type: none;
      padding-left: 0;

      li {
        padding: 5px 10px;
        color: #414f5f;
        font-size: 13px;
        font-weight: 400;
        cursor: pointer;
        transition: hover 0.5s ease-in-out;

        &:hover {
          background-color: #2376f3;
          color: white;
        }
      }
    }
  }

  &--custom {
    display: flex;
    flex-direction: column;
    row-gap: 30px;

    @media (min-width: $breakpoint-desktop-l) {
      flex-direction: row;
      column-gap: 30px;
    }

    section {
      display: flex;
      flex-direction: column;
      flex: 1;
      row-gap: 10px;
    }

    &-calendar {
      border: none !important;

      &-tile {
        color: #414f5f !important;
      }
    }
  }
}

.react-calendar__tile--now {
  background: #2376f374 !important;
  color: #fff !important;
}

.react-calendar__tile--active {
  background: #006edc !important;
  color: #fff !important;
}
.timer-section {
  margin-top: 30px;
  margin-bottom: 30px;
}
.settlement-table-search-height {
  height: auto !important;
  margin-top: 2px !important;
}
.settlement-merchant-search-height {
    height: 38px !important;
    margin-top: 1px !important;
}
