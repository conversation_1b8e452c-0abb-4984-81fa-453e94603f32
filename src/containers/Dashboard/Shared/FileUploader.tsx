import React, { InputHTMLAttributes, useRef, useState } from 'react';

import PdfIcon from '+assets/img/dashboard/pdf-file-icon.svg';
import UploadIcon from '+assets/img/dashboard/round-upload.svg';
import CancelIcon from '+assets/img/dashboard/upload-cancel.svg';

import './fileUploader.scss';

type Props = {
  text?: string;
  maxFileSizeMb?: number;
  infoText?: string;
  fileName?: string;
  uploaded?: boolean;
  uploading?: boolean;
  clearUploads?: () => unknown;
  uploadedIcon?: string;
} & InputHTMLAttributes<HTMLInputElement>;

export default function FileUploader({
  text = 'Browse file to upload',
  maxFileSizeMb = 20,
  infoText = `Max file size ${maxFileSizeMb}MB`,
  uploaded = false,
  uploading = false,
  onChange = () => null,
  clearUploads = () => null,
  fileName = '',
  ...otherInputProps
}: Props) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string>('');

  const handleDrag = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const validateFileSize = (file: File) => {
    const maxBytes = maxFileSizeMb * 1024 * 1024;
    return file.size <= maxBytes;
  };

  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    setErrorMsg('');
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (!validateFileSize(file)) {
        setErrorMsg(`File size exceeds the maximum allowed size of ${maxFileSizeMb}MB.`);
        return;
      }
      if (inputRef.current) {
        const event = {
          ...e,
          target: { files: e.dataTransfer.files }
        };
        // @ts-ignore
        onChange(event);
      }
      e.dataTransfer.clearData();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setErrorMsg('');
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (!validateFileSize(file)) {
        setErrorMsg(`File size exceeds the maximum allowed size of ${maxFileSizeMb}MB.`);
        return;
      }
    }
    onChange(e);
  };

  const handleLabelClick = (e: React.MouseEvent<HTMLLabelElement>) => {
    if (!uploaded && inputRef.current) {
      inputRef.current.click();
    }
  };

  return (
    <div className={`file-input-wrapper ${uploaded && 'uploaded'}`}>
      <label
        className={`file-input${dragActive ? ' drag-active' : ''}`}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
        htmlFor="file-upload"
        onClick={handleLabelClick}
        style={{ cursor: uploaded ? 'default' : 'pointer' }}
      >
        {uploaded ? (
          <>
            <img src={otherInputProps?.uploadedIcon ?? PdfIcon} className="icon" title="file" />
            <span className="file-name">{fileName}</span>
            <img
              src={CancelIcon}
              className="icon cancel-icon"
              title="cancel uploads"
              onClick={e => {
                e.preventDefault?.();
                clearUploads();
              }}
            />
          </>
        ) : (
          <>
            <img src={UploadIcon} className="icon" title="upload" />
            <span>{dragActive ? 'Drop file here...' : text}</span>
            <input
              id="file-upload"
              ref={inputRef}
              type="file"
              style={{ display: 'none' }}
              onChange={handleInputChange}
              {...otherInputProps}
            />
          </>
        )}
      </label>
      <div className={`bottom-info-row ${uploaded && !uploading && 'hidden'}`}>
        <p className="info-text">{infoText}</p>
        {uploading && <p className="info-text">Uploading...</p>}
      </div>
      {errorMsg && <div className="file-error-msg">{errorMsg}</div>}
    </div>
  );
}
