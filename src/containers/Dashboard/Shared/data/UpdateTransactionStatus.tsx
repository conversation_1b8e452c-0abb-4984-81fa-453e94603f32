import React from 'react';
import { useMutation, useQueryClient } from 'react-query';
import Select, { components, GroupBase, OptionProps, SingleValueProps } from 'react-select';
import { useFormik } from 'formik';

import { useFeedbackHandler, useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import { advanceCleanInput, capitalize, logError } from '+utils';
import { IUpdateTransactionForm,FormModeType,FormStageType,formStage,transactionStatusPayloadType } from '+types';
import Icon from '../Icons';

import './payInsUpdateTransactionStatus.scss';

const api = new APIRequest();

const { Option, SingleValue } = components;

const statuses = [
  { label: 'Failed', value: 'failed', icon: <Icon name="failed" /> },
  { label: 'Success', value: 'success', icon: <Icon name="success" /> }
];
const iconOption: React.FC<OptionProps<any>> = props => {
  const { data } = props;

  return (
    <Option {...props}>
      {data.icon}
      {data.label}
    </Option>
  );
};
export const maxNumOfCheckBoxToBeSelected = 20;
const statusValue: React.FC<SingleValueProps<(typeof statuses)[0], false, GroupBase<(typeof statuses)[0]>>> = props => {
  const { data } = props;

  return (
    <SingleValue {...props}>
      {data.icon}
      {data.label}
    </SingleValue>
  );
};

const initialValues = {
  reason: '',
  newStatus: 'failed',
  checkbox: false
};

const formFields: Record<FormModeType, FormModeType> = {
  newStatus: 'newStatus',
  reason: 'reason',
  checkbox: 'checkbox'
};

const transactionMode = {
  payout_transaction_table: 'payout-transaction-table',
  payins_transaction_table: 'payins-transaction-table'
} as const;

export const UpdateTransactionStatus: React.FC<
  IUpdateTransactionForm & {
    stage: FormStageType;
    formValues: Record<string, string | boolean>;
    setState: (s: FormStageType | Record<string, string | boolean | Record<string, string | boolean>>) => void;
    type: string;
  }
> = React.memo(({ currentStatus = 'processing', selectedRows, onCloseModal, clearSelection, setState, stage, formValues, type }) => {
  const { feedbackInit } = useFeedbackHandler();
  const searchQuery = useSearchQuery();
  const queryClient = useQueryClient();
  const { isValid, dirty, values, handleBlur, setFieldValue, submitForm, resetForm } = useFormik({
    initialValues,
    validateOnBlur: true,
    validateOnMount: true,
    validate: values => {
      const errors: Record<string, string> = {};
      const { reason, checkbox } = values;
      if (!reason || reason.length < 5) errors.reason = 'Please select a reason';
      if (!checkbox) errors.checkbox = 'Please click the checkbox';
      return errors;
    },
    onSubmit: () => {}
  });
  const { mutateAsync: updateTransactionStatusMutation, isLoading } = useMutation(
    (payload: transactionStatusPayloadType) => api.sendBulkAction(payload),
    {
      retry: false,
      onSuccess: () => {
        setState({ stage: formStage.stage_3 });
      },
      onError: (error: { response: { data: { message: string } } }) => {
        logError(error);
        feedbackInit({
          message: error?.response?.data?.message || 'An error occurred while updating the transaction status',
          type: 'danger'
        });
        onCloseModal();
      }
    }
  );
  const updateTransactionStatus = React.useCallback(
    async (data: transactionStatusPayloadType) => {
      try {
        await updateTransactionStatusMutation(data);
      } catch (error) {
        logError(error);
      }
    },
    [updateTransactionStatusMutation]
  );
  const onSecondButtonClick = () => {
    if (stage === formStage.stage_1) {
      setState({ stage: formStage.stage_2, values: { status: values.newStatus, reason: values.reason } });
    } else if (stage === formStage.stage_2) {
      const payLoad: transactionStatusPayloadType =
        type === transactionMode.payout_transaction_table
          ? {
              reason: formValues.reason as string,
              type: formValues.status === 'failed' ? 'payout_decline' : 'payout_approve',
              references: selectedRows,
              currency: searchQuery.value.currency ?? 'NGN'
            }
          : {
              reason: formValues.reason as string,
              type: formValues.status === 'failed' ? 'payin_decline' : 'payin_approve',
              references: selectedRows,
              currency: searchQuery.value.currency ?? 'NGN'
            };
      updateTransactionStatus(payLoad);
    }
  };

  const onSuccessModal = () => {
    clearSelection();
    if (type === transactionMode.payout_transaction_table) {
      queryClient.invalidateQueries(['PAYOUT_TRANSACTIONS']);
    } else {
      queryClient.invalidateQueries(['PAY_IN_TRANSACTIONS']);
    }
    onCloseModal();
  };

  return (
    <div className="modal-content" role="dialog" aria-modal="true" aria-labelledby="modal-title" aria-describedby="transaction-description">
      {(stage === formStage.stage_1 || stage === formStage.stage_2) && (
        <h4 id="modal-title" className="onboarding-title">
          Update Transaction Status
        </h4>
      )}

      {stage === formStage.stage_1 && (
        <p className="space-text" data-testid="transaction-description">
          Updating these status(es) will reflect on all places where the transaction(s) are stored
        </p>
      )}

      {stage === formStage.stage_2 && (
        <p className="space-text" data-testid="transaction-description">
          {`You are about to update the status of these transaction(s) from `}
          <strong>Processing</strong>
          {` to `}
          <strong>{capitalize((formValues.status as string) || '')}</strong>.
        </p>
      )}

      {selectedRows && selectedRows.length <= maxNumOfCheckBoxToBeSelected ? (
        <>
          {stage === formStage.stage_1 && (
            <>
              <label htmlFor="current-status">
                <span className="dark">Current Status</span>
              </label>
              <input type="text" id="current-status" className="form-control" value={capitalize(currentStatus)} disabled />

              <label htmlFor="new-status-select">
                <span className="dark">New Status</span>
              </label>
              <div className="select-action">
                <Select
                  inputId="new-status-select"
                  aria-label="New Status"
                  isSearchable={false}
                  name="newStatus"
                  defaultValue={statuses[0]}
                  options={statuses}
                  components={{ Option: iconOption, SingleValue: statusValue }}
                  onChange={e => {
                    setFieldValue('newStatus', e.value);
                  }}
                  onBlur={handleBlur}
                />
              </div>

              <label htmlFor="update-reason">
                <span className="dark">Reason for update</span>
              </label>
              <input
                type="text"
                id="update-reason"
                className="form-control"
                name="reason"
                onBlur={handleBlur}
                onChange={e => {
                  const inputValue = advanceCleanInput(e.target.value);
                  setFieldValue('reason', inputValue);
                }}
              />

              <span className="error" role="alert" aria-live="assertive">
                Reason for update must be at least 5 characters
              </span>

              <div className="checkbox-container">
                <input
                  type="checkbox"
                  id="approve"
                  name="checkbox"
                  onBlur={handleBlur}
                  onChange={e => setFieldValue('checkbox', e.target.checked)}
                />
                <label htmlFor="approve">Yes, I understand the implications of this action</label>
              </div>
            </>
          )}

          {stage === formStage.stage_2 && (
            <div className="banner-component-stage-two">
              <div className="icon-div-pay-out">
                <Icon name="warningOrange" />
              </div>
              <div className="banner-text text-warning-container">
                <p data-testid="warning-tag" role="alert" aria-live="polite">
                  {`Important: Updating these status(es) will make the transaction(s) ${
                    values && values.newStatus === initialValues.newStatus ? 'fail' : 'successful'
                  } and merchant will not be credited. Are you sure you want to continue ?`}
                </p>
              </div>
            </div>
          )}

          {stage === formStage.stage_3 && (
            <div className="stage-three-wrapper">
              <Icon name="complete" />
              <h5 className="form-header">Status Updated</h5>
              <p>You have successfully updated these transaction status(es).</p>
              <h5 className="dimissed-text" onClick={onSuccessModal} aria-hidden="true" style={{ cursor: 'pointer' }}>
                Dismiss
              </h5>
            </div>
          )}

          {(stage === formStage.stage_2 || stage === formStage.stage_1) && (
            <>
              <hr className="pay-ins-hr" />
              <div className="payins-button-group-right-1">
                <button
                  type="button"
                  className={`btn border-0 font-weight-bold cancel-payins-button ${
                    stage === formStage.stage_2 ? 'stage-two-width-button' : ''
                  }`}
                  onClick={onCloseModal}
                >
                  Cancel
                </button>
                <button
                  disabled={(stage === formStage.stage_1 && !(dirty && isValid)) || (stage === formStage.stage_2 && isLoading)}
                  type="button"
                  className={`btn border-0 font-weight-bold btn-primary action-botton-payins ${
                    stage === formStage.stage_2 ? 'stage-two-width-button' : ''
                  }`}
                  onClick={onSecondButtonClick}
                >
                  {stage === formStage.stage_1 ? (
                    'Continue'
                  ) : isLoading ? (
                    <span className="spinner-border spinner-border-sm" style={{ marginRight: '0.5rem' }} role="status" aria-hidden="true" />
                  ) : (
                    'Yes,Update'
                  )}
                </button>
              </div>
            </>
          )}
        </>
      ) : (
        <div className="banner-component">
          <div className="icon-div-pay-out">
            <Icon name="warningOrange" />
          </div>
          <div className="banner-text">
            <p data-testid="warning-tag" role="alert" aria-live="polite">
              You can only select up to 20 transactions with a processing status for update
            </p>
          </div>
        </div>
      )}
    </div>
  );
});

