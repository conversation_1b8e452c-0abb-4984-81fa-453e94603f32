$font-sizes: (
  h1: 30px,
  h2: 24px,
  h3: 20px,
  h4: 18px,
  subtitle1: 18px,
  subtitle2: 16px,
  subtitle3: 14px,
  subtitle4: 12px
);

$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semi-bold: 600;

.typography {
  margin: 0;
  padding: 0;
  font-family: inherit;
  line-height: 1.5;
  color: #414f5f;

  &--gutterBottom {
    margin-bottom: 0.75em;
  }
}

@function string-contains($string, $substring) {
  @return str-index($string, $substring) !=null;
}

@each $variant, $size in $font-sizes {
  .typography--#{$variant} {
    font-size: $size;

    @if $variant == 'h1' {
      font-weight: $font-weight-semi-bold;
      line-height: 1.2;
    } @else if $variant == 'h2' {
      font-weight: $font-weight-semi-bold;
      line-height: 1.25;
    } @else if $variant == 'h3' {
      font-weight: $font-weight-semi-bold;
      line-height: 1.3;
    } @else if $variant == 'h4' {
      font-weight: $font-weight-semi-bold;
      line-height: 1.4;
    } @else if string-contains($variant, 'subtitle') {
      font-weight: $font-weight-medium;
      line-height: 1.5;
      color: #a9afbc;
      max-width: 65ch;
    }
  }
}
