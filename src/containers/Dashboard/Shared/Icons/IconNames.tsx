import { SVGAttributes } from 'react';

export type TIcon = SVGAttributes<SVGElement>;
export type TRenderSvg = (args: TIcon) => JSX.Element;

export const iconNames = [
  'cancel',
  'twoPerson',
  'check',
  'caretRight',
  'lock',
  'disable',
  'editPen',
  'chevronLeft',
  'chevronRight',
  'cross',
  'validForm',
  'emptyForm',
  'invalidForm',
  'warningTriangle',
  'eye',
  'picture',
  'office',
  'caution',
  'unknown',
  'dash',
  'invalid',
  'caretUp',
  'caretDown',
  'tipsIcon',
  'complete',
  'errorInfoIcon',
  'processIcon',
  'upwardTrend',
  'fileIcon',
  'calendarIcon',
  'settings',
  'loading',
  'success',
  'failed',
  'info',
  'infoSolid',
  'warningOrange',
  'close',
  'checkRounded',
  'infoRounded',
  'merchantIcon',
  'file',
  'download',
  'pending',
  'circledCheck',
  'circledClose',
  'setting',
  'disabled',
  'arrowUpRight',
  'circledInfo',
  'circledQuestion',
  'save',
  'lightBulb',
  'caretDownSolid',
  'caretUpSolid',
  'doubleCaretLeft',
  'doubleCaretRight',
  'caretLeft',
  'warningThreeStroke',
  'briefCasePurple',
  'arrowDownSvg',
  'infoQuestionMark',
  'arrowRight',
  'medal',
  'circledSuccessWithLightGreenBg',
  'outlineCopy',
  'outlineDownload',
  'trashIcon',
  'lowRisk',
  'midRisk',
  'arrowDown',
  'avatar',
  'receipt',
  'circledCaution',
  'editPen2',
  'clock',
  'lockedPadlock',
  'unlockedPadlock',
  'envelope',
  'merchantIconCircled',
  'hamburgerArrowUp',
  'downloadInside'
] as const;

export type TIconNames = (typeof iconNames)[number];
