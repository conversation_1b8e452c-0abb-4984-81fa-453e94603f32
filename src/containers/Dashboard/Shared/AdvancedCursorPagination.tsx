import React, { useEffect, useLayoutEffect } from 'react';

import { useSearchQuery } from '+hooks';

import ToolTip from './Tooltip';

import './index.scss';

interface ICursorPagination {
  showPaginationDescription?: boolean;
  showLimit?: boolean;
  annotation?: string;
  scrollToTop?: () => void;
  disabled?: boolean;
  cursors: { next_cursor?: number; previous_cursor?: number; total_items: number | null; page_size: number };
  totalCount: number;
  totalItemsCount?: number;
}

let previousTab: string;
export default function AdvancedCursorPagination({
  showLimit = true,
  annotation = 'transactions',
  scrollToTop,
  disabled,
  cursors,
  totalCount,
  totalItemsCount = 0
}: ICursorPagination) {
  const searchQuery = useSearchQuery<{
    endingBefore: string;
    totalItems: string;
    page: string;
    limit: string;
    tab: string;
    currency: string;
    activeTab: string;
    pageAction: string;
    pageCursorValue: string;
    previousLimit: string;
  }>();
  const totalItems = Number(searchQuery.value.totalItems ?? 0);
  const page = Number(searchQuery.value.page ?? 1);
  const pageSize = Number(cursors.page_size ?? 10);
  const currentPageSize = Number(searchQuery.value.limit ?? pageSize);
  const tab = searchQuery.value.tab ?? searchQuery.value.activeTab ?? searchQuery.value.currency ?? '';
  const pagingTotalItems = Number(totalItemsCount ?? 0);
  const previousCursor = cursors?.previous_cursor;
  const nextCursor = cursors?.next_cursor;
  const noNextCursor = !nextCursor;
  const limit = totalItems % currentPageSize;
  const previouslimit = Number(searchQuery.value.previousLimit ?? 0);

  useLayoutEffect(() => {
    if (pagingTotalItems && pagingTotalItems > 0 && pagingTotalItems !== totalItems) {
      searchQuery.setQuery({ totalItems: String(pagingTotalItems) });
    }
  }, [pagingTotalItems, totalItems]);

  useEffect(() => {
    if (previousTab && tab !== previousTab) {
      searchQuery.setQuery({ pageAction: '', endingBefore: '', totalItems: '' });
    }
    previousTab = tab;
  }, [tab]);

  useEffect(() => {
    if (totalItems === 0) return;
  }, [totalItems, previouslimit]);

  const paginate = (action: 'start' | 'previous' | 'next' | 'end', value?: number) => {
    if (action === 'next' && nextCursor) {
      searchQuery.setQuery({
        pageAction: action,
        pageCursorValue: (value && String(value)) || undefined
      });
    } else if (action === 'previous' && previousCursor && noNextCursor) {
      searchQuery.setQuery({
        pageAction: action,
        pageCursorValue: (value && String(value)) || undefined,
        limit: String(previouslimit)
      });
    } else if (action === 'previous' && previousCursor) {
      searchQuery.setQuery({
        pageAction: action,
        pageCursorValue: (value && String(value)) || undefined
      });
    } else if (action === 'start') {
      searchQuery.setQuery({
        pageAction: action,
        pageCursorValue: (value && String(value)) || undefined,
        limit: String(previouslimit)
      });
    } else if (action === 'end') {
      searchQuery.setQuery({
        pageAction: action,
        pageCursorValue: (value && String(value)) || undefined,
        limit: String(limit || currentPageSize),
        previousLimit: String(previouslimit || currentPageSize)
      });
    }
  };

  const renderPagination = () => {
    return (
      <div className="paging-row">
        <button
          disabled={!previousCursor}
          onClick={() => paginate('start')}
          type="button"
          className={`pagination-button ${disabled || !previousCursor ? 'disabled' : ''}`}
          aria-label="First Page"
        >
          &laquo;
        </button>

        <button
          disabled={!previousCursor}
          onClick={() => paginate('previous', previousCursor)}
          type="button"
          className={`pagination-button ${disabled || !previousCursor ? 'disabled' : ''}`}
          aria-label="Previous Page"
        >
          &lsaquo;
        </button>

        <button
          disabled={!nextCursor}
          onClick={() => paginate('next', nextCursor)}
          type="button"
          className={`pagination-button ${disabled || noNextCursor ? 'disabled' : ''}`}
          aria-label="Next Page"
        >
          &rsaquo;
        </button>
        <button
          type="button"
          aria-label="Last Page"
          disabled={!nextCursor}
          className={`pagination-button ${!nextCursor ? 'disabled' : ''}`}
          onClick={() => paginate('end')}
        >
          &raquo;
        </button>
      </div>
    );
  };

  const scrollToTopSection = () => {
    if (totalCount < 10 || (totalCount <= 5 && document.body.clientWidth < 1024)) return null;
    const defaultScroll = () => window.scroll(0, 0);
    return (
      <div>
        <button disabled={disabled} className="btn btn-sm pagination-back-to-top" type="button" onClick={scrollToTop || defaultScroll}>
          <span>Back to top</span>
          <i className="os-icon os-icon-arrow-up6" />
        </button>
      </div>
    );
  };

  const displayLimit = () => {
    return (
      showLimit && (
        <label>
          Show
          <select
            name="dataTable1_length"
            onChange={e =>
              searchQuery.setQuery({
                limit: String(e.target.value),
                pageAction: 'start',
                pageCursorValue: '',
                page: '1',
                previousLimit: String(e.target.value)
              })
            }
            value={previouslimit || '10'}
            aria-controls="dataTable1_length"
            className={`form-control form-control-sm ${annotation === 'users' ? 'default-menu' : ''} ${
              disabled || page !== 1 ? 'disabled' : ''
            }`}
            disabled={disabled || page !== 1}
            data-testid="pagination_limit"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          {annotation} per page
        </label>
      )
    );
  };

  return (
    <>
      {totalItems !== 0 ? (
        <ToolTip
          hasFullWidth
          centered
          disabled={!disabled || totalItems <= pageSize}
          type="bulk_actions_pagination"
          message={<p>This action is disabled because you are carrying out a bulk action on this page.</p>}
        >
          <section
            className={`pagination-container ${disabled ? 'disabled' : ''} `}
            hidden={totalItems === 0}
            data-testid="pagination-component"
          >
            {totalItems <= pageSize ? (
              <>
                <div className="dataTables_length">
                  <span className="pagination-pages">
                    <strong>{totalItems}</strong> {annotation}
                  </span>
                </div>
                {scrollToTopSection()}
              </>
            ) : (
              <>
                <div className="dataTables_length" id="dataTable1_length">
                  {page === 1 ? (
                    displayLimit()
                  ) : (
                    <ToolTip
                      type="limit"
                      hasFullWidth
                      message={<p>You can only choose the number of items to display from the first page.</p>}
                    >
                      {displayLimit()}
                    </ToolTip>
                  )}
                </div>

                {scrollToTopSection()}

                {renderPagination()}
              </>
            )}
          </section>
        </ToolTip>
      ) : (
        <section className={`pagination-container ${disabled ? 'disabled' : ''} `} data-testid="pagination-component">
          <>
            <div className="dataTables_length" id="dataTable1_length">
              {page === 1 ? (
                displayLimit()
              ) : (
                <ToolTip type="limit" hasFullWidth message={<p>You can only choose the number of items to display from the first page.</p>}>
                  {displayLimit()}
                </ToolTip>
              )}
            </div>

            {scrollToTopSection()}

            {renderPagination()}
          </>
        </section>
      )}
    </>
  );
}
