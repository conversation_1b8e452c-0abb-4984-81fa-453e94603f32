import React from 'react';

import { IBatchSearch, IDateFilterProps } from '+types';

import DateFilter from './DateFilter';
import SecondarySearch from './SecondarySearch';

import './SecondaryFilter.scss';

const SecondaryFilter = ({
  selectedDate,
  onDateChange,
  type,
  handleSearch,
  state,
  batchSearch
}: IDateFilterProps & {
  type: 'pay-in' | 'pay-outs' | 'paused-payments' | 'audit-logs' | 'settlements';
  handleSearch: (value: Record<string, unknown>) => void;
  state: Record<string, unknown>;
  batchSearch?: IBatchSearch;
}) => {
  const fullWidth = ['merchant-team-members'].includes(type);
  const isSettlements = type === 'settlements';
  return (
    <div className={`secondary-filter--container ${fullWidth ? '--full-width' : ''} ${isSettlements ? '--settlement-table-search' : ''}`}>
      {!['merchant-team-members', 'settlements'].includes(type) && (
        <DateFilter tableType={type} selectedDate={selectedDate} onDateChange={onDateChange} isCleared={state.clear as boolean} />
      )}
      <SecondarySearch type={type} handleSearch={handleSearch} state={state} batchSearch={batchSearch} />
    </div>
  );
};

export default SecondaryFilter;
