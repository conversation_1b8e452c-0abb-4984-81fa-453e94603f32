.file-input-wrapper {
  .file-input {
    background-color: #f1f6fa;
    padding: 15px;
    border: 2px dashed #dde2ec;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition:
      background 0.2s,
      border-color 0.2s;
    &.drag-active {
      background-color: #e6f0ff;
      border-color: #2376f3;
      box-shadow: 0 0 0 2px #2376f333;
      * {
        color: #2376f3;
      }
    }
    * {
      color: #9a9a9a;
      font-weight: 400;
    }

    .icon {
      width: 20px;
      margin-right: 10px;
    }
    .file-name {
      color: #2376f3;
      text-decoration: underline;
      flex: 1;
    }
    .cancel-icon {
      width: 16px;
      cursor: pointer;
    }
  }
  &.uploaded .file-input {
    transition: background 200ms ease-in;
    justify-content: flex-start;
    background-color: #e4fff1;
    cursor: default;
  }
  .info-text {
    color: #414f5f;
    opacity: 0.7;
  }
  .bottom-info-row {
    display: flex;
    justify-content: space-between;
    transition: all 300ms ease-in-out;
    max-height: 200px;
    overflow: hidden;

    &.hidden {
      max-height: 0;
    }
  }
  .file-error-msg {
    color: #d32f2f;
    margin-top: 8px;
    font-size: 0.95em;
  }
}
