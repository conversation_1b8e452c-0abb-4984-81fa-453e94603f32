@import '+styles/kpy-custom/variables';

// Pagination Component
.pagination-button {
  height: 30px;
  background-color: rgb(225, 238, 252);
  text-decoration: none;
  padding: 3px 8px;
  border-radius: 3px;
  margin-right: 5px;
  border: none;
  font-size: 13px;
  color: rgb(35, 118, 243);
  margin-top: 1rem;
  transition: background-color 0.3s;

  &.--first {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
  }

  &.--last {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
  }

  &.active,
  &:hover {
    background-color: rgb(35, 118, 243);
    color: white;
  }

  &.disabled {
    color: #a9afbc;
    background-color: #f1f6fa;
    cursor: not-allowed;
  }
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  .pagination-back-to-top {
    background: none;
    border: none;
    color: #2376f3;
    font-weight: 500;
    padding-top: 5px;
    padding-bottom: 5px;
    display: flex;
    align-items: center;
    background-color: #e2eefc;
    border-radius: 8px;
    margin: 1.25rem 0 0.5rem;
  }

  @media (min-width: 600px) {
    flex-direction: row;

    .pagination-back-to-top {
      margin: 1.25rem 1rem 0;
    }
  }

  .paging-row {
    display: flex;

    .pagination-pages {
      margin-top: 1rem;
      font-size: 13.5px;
      display: flex;
      align-items: center;
      margin-right: 10px;

      & > strong {
        margin: 0 3px;
      }
    }

    @media (min-width: 600px) {
      justify-content: flex-end;
    }
  }

  .dataTables_length {
    display: flex;
    align-items: center;
    margin-top: 1rem;

    select {
      display: inline-block;
      width: 40px !important;
      margin: 0px 5px;
      vertical-align: middle;
      background-color: #e0e7f0;
      border: 2px solid #e0e7f0;
      appearance: menulist;
      padding: 1px !important;

      &.disabled {
        cursor: not-allowed;
      }
    }

    label {
      font-size: 0.85rem;
      margin-top: 0;
      margin-bottom: 0;
    }

    .divider-sm {
      height: 20px;
      margin: 0 10px 0 20px;
    }

    .pagination-pages {
      margin-top: 0;
      font-size: 13.5px;
    }

    span + .pagination-pages {
      margin-left: 10px;
    }

    .annotation {
      color: rgba(90, 99, 126, 0.49);
      font-size: 0.8rem;
      font-weight: 400;
      margin-left: -0.6rem;
      text-transform: lowercase;
    }
  }

  &.disabled {
    .pagination-button {
      &.active,
      &:hover {
        background-color: #a9afbc;
        color: white;
      }
    }

    .pagination-back-to-top,
    & .pagination-button {
      color: #a9afbc;
      background-color: #f1f6fa;
    }

    .goto-container {
      color: #a9afbc;

      button {
        background-color: #a9afbc;
        color: white;
      }

      input {
        border-color: #a9afbc;
      }
    }
  }
}

.goto-container {
  font-size: 14px;
  height: 30px;
  border-radius: 3px;
  color: rgb(35, 118, 243);
  padding: 5px 0 0 0;

  span {
    font-weight: 600;
  }

  input {
    text-align: center;
    padding: 0;
    border-radius: 0;
    margin: 0 10px;
    width: 30px;
    font-size: 13px;
    background: none;
    border-radius: 5px;
    border: 1.5px solid rgb(35, 118, 243);
  }

  input[type='number'] {
    -moz-appearance: textfield;
    -webkit-appearance: textfield;

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }

  button {
    border-radius: 3px;
    font-size: 13px;
    background-color: rgb(35, 118, 243);
    color: white;
    border: none;
  }

  @media screen and (max-width: 600px) {
    display: none;
  }
}

// FeedBackHandler Component
.feedback {
  position: relative;
  font-size: 0.85rem;
  padding: 0.75rem 1.25rem;
  margin: 1rem 1.6rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  font-weight: 300;

  p:last-child {
    margin-bottom: 0px;
  }

  &.danger {
    color: #86252f;
    background-color: #f8d7da;
    border-color: #f5c6cb;
  }

  &.success {
    color: #256b5c;
    background-color: #daf5ef;
    border-color: #ccf1e9;
  }

  .btn-action {
    background: none;
    border: none;
  }

  &.full-width {
    margin: 0px;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    font-weight: 300;
    width: auto;
    left: 0;
    right: 0;
    border: none;
    border-radius: 0rem;
    text-align: center;
    transition: top 300ms cubic-bezier(0.17, 0.04, 0.03, 0.94);

    &.success {
      color: #fff;
      background-color: #24b314;
    }

    &.danger {
      color: #fff;
      background-color: #f32345;
      display: flex;
      column-gap: 10px;
      justify-content: center;
      flex-direction: column;

      & > button.close {
        position: absolute;
        right: 1rem;
      }
    }

    &.warning {
      color: #24292f;
      background-color: #d4a72c;
    }

    button,
    button > span {
      color: #fff;
      top: 8px !important;
    }
  }

  // @media (min-width: 768px) and (max-width: 1024px) {
  //   &.full-width:not(.component-level) {
  //     width: calc(100% - 70px);
  //     left: 70px;
  //   }
  // }
}

// LoadingPlaceHolder
@mixin gradient-animation {
  background-size: 400px;
  animation: shine-lines 1s ease-in-out;
  animation-iteration-count: infinite;
}

.loader-row {
  opacity: 1;
  padding: 1rem;
  display: flex;
  border-radius: 5px;
  justify-content: space-between;
  margin-bottom: 10px;
  animation: aniVertical 2.5s ease;
  animation-iteration-count: infinite;
  animation-fill-mode: forwards;
  box-shadow: 0 1px 12px 0 rgba(113, 113, 113, 0.05);

  &:nth-child(2) {
    animation-delay: 0.5s;
  }

  &:nth-child(3) {
    animation-delay: 1s;
  }

  .loader-content {
    padding: 5px;
    border-radius: 5px;
    background-image: linear-gradient(100deg, rgb(241, 240, 240) 0, rgba(224, 222, 222, 0.8) 60px, rgb(241, 240, 240) 80px);

    @include gradient-animation;
  }
}

.textbox-loader-container {
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  & + & {
    margin-top: 30px;
  }

  @media (min-width: $breakpoint-tablet) {
    flex-wrap: wrap;
    flex-direction: row;
  }

  .textbox-loader {
    margin-bottom: 15px;
  }

  .loader-content {
    padding: 6px;
    border-radius: 5px;
    margin-bottom: 10px;
    background-image: linear-gradient(90deg, rgba(229, 229, 229, 0.8) 0, #f4f4f4 80px, rgba(229, 229, 229, 0.8) 80px);

    @include gradient-animation;
  }
}

@keyframes aniVertical {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes shine-lines {
  0% {
    background-position: -100px;
  }

  40%,
  100% {
    background-position: 140px;
  }
}

@media (max-width: $breakpoint-desktop) {
  .element-search-content.filter-body > div,
  .element-search-content button.filter__button {
    width: 100% !important;
  }

  .element-search-content.filter-body div.react-datepicker-popper {
    transform: translate3d(15px, 807px, 0px);
  }

  .filter-section .filter-body .form-group.filter-object-ssm,
  .filter-section .filter-body .form-group.filter-object-xl,
  .filter-section .filter-body .form-group.filter-object-sm {
    max-width: 100% !important;
  }
}

// Filter Component
.filter-section {
  // display: none;

  .filter-box {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 15px;
    margin-bottom: 20px;
  }

  @media (min-width: $breakpoint-desktop) {
    display: block;
  }

  .filter-heading {
    margin-bottom: 1.5rem;
    font-weight: 500;
    letter-spacing: -0.1px;
  }

  .filter-body {
    display: flex;
    flex-wrap: wrap;
    width: 103%;

    .form-group {
      margin-bottom: 0.4rem;
      margin-right: 15px;
      width: 100%;
    }

    .filter-object label .label-note {
      color: rgba(62, 75, 91, 0.4);
    }

    .filter-object .filter-check {
      display: flex;
      flex-wrap: wrap;

      .filter-check-item {
        margin-right: 1rem;
        margin-bottom: 0.3rem;
        padding: 0.5rem 0.7rem;
        border-radius: 5px;
        font-weight: 300;
        color: #000;
      }
    }

    .form-group.filter-object-md {
      max-width: 25%;
      min-width: 20%;
    }

    .form-group.filter-object-sm {
      max-width: 25%;
      min-width: 15%;
    }

    .form-group.filter-object-ssm {
      max-width: 15%;
      min-width: 10%;
    }

    .form-group.filter-object-lg {
      max-width: 30%;
      min-width: 25%;
    }

    .form-group.filter-object-xl {
      max-width: 40%;
      min-width: 35%;
    }

    .form-group.filter-object-xxl {
      max-width: 55%;
      min-width: 40%;
    }

    .form-group.--no-max-width {
      max-width: initial;
    }

    .filter-options {
      margin-bottom: 0.5rem;
      align-self: flex-end !important;

      * {
        padding: 0.5rem 0.75rem;
        font-size: 16px;
      }

      .btn-secondary {
        background-color: #292b2c;
        border-color: #292b2c;
        margin-right: 0.5rem;
      }

      .btn-success {
        background-color: #24b314;
        border-color: #24b314;
      }
    }
  }
}

// Search Component
.search__container {
  margin-right: 15px;
  margin-left: 15px;
  margin-bottom: 10px;
  display: flex;
  position: relative;

  @media (min-width: $breakpoint-desktop) {
    margin-bottom: 0;
  }

  input {
    border: 2px solid #dde2ec;
    padding: 0.7rem 0.7rem 0.7rem 1.75rem;
    height: 2.25rem !important;
    border-right: none;
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
    font-size: 0.75rem;
    min-width: 13.5rem;

    &::-webkit-search-cancel-button,
    &::-webkit-search-decoration {
      -webkit-appearance: auto;
    }

    &::placeholder {
      font-weight: 400;
      color: #3e4b5b;
      font-size: 0.75rem;
    }
  }

  .search__icon {
    width: 0.85rem;
    position: absolute;
    left: 0.625rem;
    top: 50%;
    transform: translateY(-50%);
  }

  .search__button {
    border: none;
    background: #3e4b5b;
    width: 3rem;
    height: 2.25rem;
    padding-bottom: 2px;
    border-radius: 0 4px 4px 0;

    img {
      width: 1.125rem;
    }

    &:hover img {
      transform: scale(1.1);
    }
  }
}

// Styles for the SecondaryDetails component:
.sec-details-title {
  font-weight: 600;
  font-size: 1.1rem;
  letter-spacing: -0.02em;
  color: #4e555b;
  margin-bottom: 1.2rem;
}

.secondary-details__comp {
  display: flex;
  flex-direction: column;
  padding-bottom: 1rem;

  @media (min-width: $breakpoint-desktop) {
    flex-direction: row;
  }

  ul {
    display: block;
    padding: 0 !important;

    &:first-of-type {
      @media (min-width: $breakpoint-desktop) {
        width: 46%;
        border-bottom: unset;
        padding-right: 2rem !important;
      }

      &::after {
        content: '';
        display: block;
        margin-right: 1rem;
      }
    }

    &:nth-of-type(2) {
      @media (min-width: $breakpoint-desktop) {
        padding-left: 2rem !important;
        border-left: 1px solid rgba(0, 0, 0, 0.08);
        width: 50%;
      }
    }
  }

  .sec-details-list {
    display: flex;
    flex-direction: row;
    min-width: 300px;
    margin-bottom: 12px;
    font-size: 0.94rem;

    @media (min-width: $breakpoint-tablet) {
      flex-direction: row;
      margin-bottom: 8px;
    }

    .sec-details-key {
      color: #aabdce;
      width: 200px;
    }

    .sec-details-val {
      font-weight: 300;
      color: #3e4b5b;

      .smaller {
        font-size: 0.88rem;
      }

      .lighter {
        color: #aabdce;
      }
    }
  }
}

.sec-details-hr {
  border-color: rgba(0, 0, 0, 0.05);
  margin: 0 -1rem;

  @media (min-width: $breakpoint-desktop) {
    margin: 1rem -2rem;
  }

  &.short {
    margin: 1rem 0 0;
  }
}

// Styles for the CurrencyPicker component:
.currency-picker__comp {
  padding: 0.5rem 0;

  > div {
    float: right;
  }

  @media (min-width: $breakpoint-desktop-sm) {
    padding: 0.5rem 0;

    > div {
      float: none;
    }
  }

  &.__with-border {
    border-bottom: 1px solid #dee2e6;
  }
}

.copy-button {
  border: none;
  background: inherit;
  padding: 0;
  margin-left: 5px;

  #copy-icon {
    margin: -4px 0 0 0;
    width: 30px;
    filter: none;
  }
}

// Toast Component
.toast__main {
  position: relative;
  min-height: 90px;
  margin-bottom: 1rem;

  p {
    margin: 0;
    color: grey;
  }

  .toast__container {
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 5px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    width: 100%;

    @media (min-width: $breakpoint-tablet) {
      width: 350px;
    }

    @media (min-width: $breakpoint-desktop) {
      width: 400px;
    }

    &.info {
      background-color: #fff;
      border-color: rgba(0, 0, 0, 0.1);
    }

    &.success {
      background-color: #daf5ef;
      border-color: #ccf1e9;
    }

    &.danger {
      background-color: #f8d7da;
      border-color: #f5c6cb;
    }

    &.warning {
      background-color: #fbe4a0;
      border-color: #fbe4a0;
    }
  }

  .toast__header {
    display: flex;
    justify-content: space-between;
    padding: 0.2rem 0 0.2rem 0.7rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    .header-text {
      font-weight: 500;
      font-size: 16px;
      align-self: center;
      color: #6c757d;
    }
  }

  .btn {
    span {
      font-weight: bold;
      font-size: 20px;
      color: #6c757d;
    }
  }

  .toast__body {
    padding: 0.8rem 0.7rem;
  }
}

// Tooltip component
.text-tooltip-w {
  width: 0.875rem;
  display: inline-block;
  position: relative;
  margin-left: 0.5rem;
  transform: translateY(2px);

  &.no-transform {
    transform: translateY(0);
  }

  &.visible {
    z-index: 12;
  }

  img.text-tooltip--image {
    margin: 0;
    display: block;
    width: 1rem;
    height: 1rem;
  }

  div.text-tooltip--content {
    visibility: hidden;
    opacity: 0;
    position: absolute;
    z-index: 10;
    transition:
      opacity 0.3s ease-in,
      visibility 0.3s ease-in;
    padding: 0.875rem 1.25rem;
    transform: translateY(-50%);
    left: -2rem;
    top: calc(100% + 3.5rem);
    width: 220px;
    background: #292b2c;
    border-radius: 0.5rem;
    color: #ffffff;

    &::after {
      position: absolute;
      width: 0;
      height: 0;
      content: '';
      border-style: solid;
      border-width: 9px;
      border-color: #292b2c transparent transparent #292b2c;
      transform: translateY(-50%) rotate(45deg);
      top: 7px;
      left: 1.875rem;
    }

    &.visible {
      visibility: visible;
      opacity: 1;
    }

    p {
      margin: 0;
      color: white;
      font-size: 0.75rem;
    }

    @media (min-width: 768px) {
      width: 280px;
      left: calc(100% + 0.875rem);
      top: 50%;
      transform: translateY(-50%);

      &::after {
        transform: translateY(-50%) rotate(315deg);
        top: 50%;
        left: -2px;
      }
    }

    &.centered {
      left: 0;
      top: -50%;
      transform: translate(0, 50%);

      &::after {
        transform: translateX(-50%) rotate(225deg);
        top: auto;
        left: 50%;
        bottom: -2px;
      }

      @media (min-width: 768px) {
        left: 50%;
        top: -100%;
        transform: translate(-50%, 0);
      }
    }
  }
}

// Styles for the Accordion component:
.accordion__comp {
  summary {
    list-style: none;
    display: flex;

    section {
      margin: 0 20px 0 0;
      padding: 0 !important;
      background-color: inherit !important;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0.5rem;

      p {
        margin: 0;
      }
    }

    &::-webkit-details-marker {
      display: none !important;
    }
  }

  &[open] {
    summary ~ * {
      animation: sweep 0.5s ease-in-out;
    }

    .toggler-arrow {
      transform: rotate(180deg) !important;
    }
  }

  @keyframes sweep {
    0% {
      opacity: 0;
      margin-top: -5px;
    }

    100% {
      opacity: 1;
      margin-top: 0px;
    }
  }
}

.accordion__comp--checkbox {
  width: 100%;

  section {
    opacity: 0;
    visibility: hidden;
    max-height: 0;
    transition:
      visibility 0.5s ease-in-out,
      opacity 0.5s ease-in-out,
      max-height 0.35s ease-in-out 0.1s;
  }

  section.content-visible {
    opacity: 1;
    visibility: visible;
    max-height: 500px;
  }

  > section > div {
    padding: 0;
  }

  &-title label,
  &-title label button {
    font-weight: 500;
    color: #3e4b5b;
  }
}

// Custom Checkbox / Radio
.checkbox__custom,
.radio__custom {
  display: grid;
  grid-template-columns: 1em auto;
  gap: 0.4em;
  align-items: baseline;

  &.disabled button {
    color: #a9afbc;
    cursor: auto;
  }

  button {
    background: none;
    color: inherit;
    border: none;
    font-size: inherit;
    box-shadow: none;
    cursor: pointer;
    padding: 0;
    width: max-content;
    text-align: left;
    font-size: 12px;
    width: 100%;
    width: fit-content;
  }

  input {
    appearance: none;
    background-color: #fff;
    margin: 0;
    font: inherit;
    color: #a9afbc;
    border: 1px solid #a9afbc;
    cursor: pointer;
    transform: translateY(0.15em);
    display: grid;
    place-content: center;

    &::before {
      content: '';
      width: 14px;
      height: 14px;
      transform: scale(0);
      transition: 120ms transform ease-in-out;
      background-size: 8px;
      background-repeat: no-repeat;
      background-position: center;
      background-color: #2376f3;
    }

    &.checked::before {
      transform: scale(1);
    }

    &:not(.checked):focus {
      outline: 1px solid #2376f3;
      outline-offset: 1px;
    }

    &.disabled {
      cursor: auto;

      &::before {
        background-color: #a9afbc;
      }
    }
  }
}

.checkbox__custom {
  align-items: baseline;

  input {
    border-radius: 2px;
    width: 13px;
    height: 13px;

    &::before {
      background-image: var(--check-image);
      border-radius: 2px;
    }

    &.checked {
      border: none;
    }
  }
}

.radio__custom {
  align-items: flex-start;

  input {
    border-radius: 50%;
    width: 14px;
    height: 14px;

    &::before {
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }

    &.checked {
      border-color: #2376f3;
    }
  }
}

// Grey Sections

.grey-section {
  .__title {
    margin-bottom: 1.125rem;
    color: #636c72;
    font-weight: 500;
    font-size: 0.92rem;
  }

  &__subsection {
    background: #fbfcfd;
    border: 1px solid #d3e4fd;
    border-radius: 10px;
    margin-bottom: 2.5rem;
  }

  &__subsection-group {
    padding: 1.5rem 1.625rem;

    + .grey-section__subsection-group {
      border-top: 1px solid #d3e4fd;
    }
  }

  &__subsection-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-direction: column;
    font-size: 0.92rem;
    gap: 2rem;

    p {
      margin: 0;
      font-size: 0.92rem;
    }

    > div:first-child p:first-child {
      font-weight: 500;
      margin-bottom: 0.325rem;
      color: #414f5f;
    }

    > div p:last-child {
      max-width: 400px;
    }

    button {
      color: #2376f3;
      font-size: 0.875rem;
      font-weight: 500;
      padding: 0;
    }

    @media screen and (min-width: 600px) {
      flex-direction: row;
      align-items: center;
    }
  }
}

.arrow-btn {
  cursor: pointer;
  display: inline-block;

  .caret {
    transform: rotate(0deg);
    color: #94a7b7;
  }

  .open-detail {
    transform: rotate(90deg);
  }
}

// Load last query Button
.loadquery {
  position: relative;

  &_marker {
    width: 7px;
    border: 1px solid #dde2ec;
    height: 7px;
    background: white;
    transform: rotate(45deg);
    position: absolute;
    top: 27px;
    right: 15px;
    z-index: 99999;
    border-right: none;
    border-bottom: 0px;
  }

  &_container {
    position: absolute;
    top: 30px;
    right: 5px;
    width: 190px;
    padding: 10px;
    margin: 0px;
    background: white;
    z-index: 9999;
    border: 1px solid #dde2ec;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    font-weight: 800;

    &-item {
      width: 100%;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }
}

// Styles for ViewAndDownload Component

.download_button {
  border: none;
  background-color: transparent;
  color: #2376f3;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    text-decoration: none;
  }

  img {
    margin-left: 15px;
  }
}

// Uploader

.upload_container {
  align-items: center;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.bulk-action-section {
  display: flex;
  margin-bottom: 1.6rem;
  gap: 0.3rem 1rem;

  button {
    background: #eaf2fe;
    font-style: normal;
  }

  .approveText-p {
    max-width: 19.5rem;
    margin-bottom: 0rem;
  }

  .approve-btn {
    background: #2376f3;
    color: #fff;
    padding: 0.75rem 1.5rem;
    text-transform: capitalize;

    &:hover {
      color: #fff;
    }
  }
}

.bulktable-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem 0.65rem;
  background: #f1f6fa;

  p {
    font-size: 0.9rem;
    color: #a9afbc;
    font-weight: 500;

    & img {
      width: 1.2rem;
      margin-right: 0.66rem;

      &.rotate {
        animation: rotate 1s linear infinite;

        @keyframes rotate {
          0% {
            transform: rotate(0deg);
          }

          100% {
            transform: rotate(360deg);
          }
        }
      }
    }

    & a {
      margin-left: 0.76rem;
      text-decoration: none;
      color: #292b2c;
      font-weight: 600;
    }
  }

  .bulktable-p-completed {
    color: #ffffff;
  }

  &.is-completed {
    background: #24b314;

    p {
      color: #ffffff;
    }
  }
}

.field-error,
.form-error {
  font-size: 0.85rem;
  color: #ff5661;

  &:empty {
    display: none;
  }
}

.default-payment-channel {
  button {
    color: #2376f3;
  }
}
.check-all-boxes {
  min-width: 40;
}

.select-product-type {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 120%;
  margin: 0 0 -0.1rem 0 !important;
}

.tooltip-portal-container > .text-tooltip--content {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  transition: opacity 0.2s ease-in-out;
  padding: 0.75rem 1rem;
  width: max-content;
  max-width: 280px;
  background-color: #292b2c;
  color: white;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  line-height: 1.4;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tooltip-portal-container > .text-tooltip--content.visible {
  visibility: visible;
  opacity: 1;
}

.tooltip-portal-container > .text-tooltip--content::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px;
}

.tooltip-portal-container > .text-tooltip--content[data-position='top']::after {
  border-color: #292b2c transparent transparent transparent;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.tooltip-portal-container > .text-tooltip--content[data-position='bottom']::after {
  border-color: transparent transparent #292b2c transparent;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.tooltip-portal-container > .text-tooltip--content[data-position='left']::after {
  border-color: transparent transparent transparent #292b2c;
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
}

.tooltip-portal-container > .text-tooltip--content[data-position='right']::after {
  border-color: transparent #292b2c transparent transparent;
  top: 50%;
  right: 100%;
  transform: translateY(-50%);
}

.paused-payments-filter-body {
  display: flex;
  justify-content: flex-end;
}

.paused-payments-filter-body > *:nth-child(1) {
  margin: 0;
  flex: 1;
  max-width: 100%;
  box-sizing: border-box;
}

.paused-payments-filter-body > *:last-child {
  margin: 0;
  flex: 0 0 auto;
  max-width: none;
}

.secondary-filter-wrapper {
  flex-grow: 1;
}

@media (min-width: 1024px) {
  .secondary-filter-wrapper {
    margin-right: -23%;
  }
}
.batch-viewer-text{
    color: #2376f3;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
}
.table-with-batch-search{
  margin-top: 2rem !important;
}

