$badge-types: (
  'success': (bg: #e4fff1,
    fg: #24B314,
    label: 'Transaction successful'
  ),
  'processing': (bg: #FFF8E1,
    fg: #FA9500,
    label: 'Transaction in progress...'
  ),
  'requires_auth': (bg: transparent,
    fg: rgba(0, 0, 0, 0.5),
    label: 'Transaction requires authorization'
  ),
  'require_auth': (bg: transparent,
    fg: rgba(0, 0, 0, 0.5),
    label: 'Transaction requires authorization'
  ),
  'failed': (bg: #FFD2DA,
    fg: #F32345,
    label: 'Transaction failed'
  ),
  'expired': (bg: #DDE2EC,
    fg: #94A7B7,
    label: 'Transaction expired'
  ),
  'rejected': (bg: #DDE2EC,
    fg: #94A7B7,
    label: 'Transaction rejected'
  ),
  'abandoned': (bg: #DDE2EC,
    fg: #94A7B7,
    label: 'Transaction abandoned'
  ),
  'won': (bg: #E4FFF1,
    fg: #24B314,
    label: 'Won'
  ),
  'accepted': (bg: #E4FFF1,
    fg: #24B314,
    label: 'Accepted'
  ),
  'pending': (bg: #FFF8E1,
    fg: #FA9500,
    label: 'Pending'
  ),
  'card_pending': (bg: #FFF8E1,
    fg: #915200,
    label: 'Pending'
  ),
  'card_active': (bg: #E4FFF1,
    fg: #24B314,
    label: 'Active'
  ),
  'lost': (bg: #FFD2DA,
    fg: #F32345,
    label: 'Chargeback Lost'
  ),
  'declined': (bg: #FFD2DA,
    fg: #F32345,
    label: 'Chargeback Declined'
  ),
  'partial': (bg: #E4FFF1,
    fg: #24B314,
    label: 'Partial'
  ),
  'auto_accepted': (bg: #FFD2DA,
    fg: #F32345,
    label: 'Accepted (Auto)'
  ),
  'enabled': (bg: #e4fff1,
    fg: #24B314,
    label: 'Enabled'
  ),
  'disableds': (bg: #F1F6FA,
    fg: #414F5F,
    label: 'Disabled'
  ),
  'disabled': (bg: #FFD2DA,
    fg: #F32345,
    label: 'Disabled'
  ),
  'disabled_access': (bg: #F1F6FA,
    fg: #414F5F,
    label: 'Disabled'
  ),
  'reversed': (bg: #DDE2EC,
    fg: #94A7B7,
    label: 'Reversed'
  ),
  'void_authorization': (bg: #F1F6FA,
    fg: #414F5F,
    label: 'Voided (Auth)'
  ),
  'void_capture': (bg: #F1F6FA,
    fg: #414F5F,
    label: 'Voided (Capture)'
  ),
  'pre_authorized': (bg: #EBEDFF,
    fg: #6474FF,
    label: 'Pre-Authorized'
  ),
  'paused': (bg: #F3F4F8,
    fg: #414F5F,
    label: 'Transaction On Hold'
  ),
  'flagged_success': (bg: #DDE2EC,
    fg: #94A7B7,
    label: 'Flagged Success'
  )
);
