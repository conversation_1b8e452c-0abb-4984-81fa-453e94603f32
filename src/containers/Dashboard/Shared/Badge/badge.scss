@import 'status-config';

.badge-comp {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  padding: 0.5rem 1rem;
  border-radius: 0.5em;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
}

@each $status, $config in $badge-types {
  .badge-comp--#{$status} {
    background-color: map-get($config, bg);
    color: map-get($config, fg);
    --badge-name: "#{map-get($config, label)}";

    &.show-name::before {
      content: var(--badge-name);
    }
  }
}
