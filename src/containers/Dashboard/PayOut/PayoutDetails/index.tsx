import React, { useEffect, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useLocation, useParams } from 'react-router-dom';

import Modal from '+containers/Dashboard/Shared/Modal';
import { useFeedbackHandler, useSetUserAccess, useTransactionStatus } from '+hooks';
import APIRequest from '+services/api-services';
import { IModalProps, IPayOutDetails, TTransactionDetails } from '+types';
import { formatAmount, history, isAllowed, isObjectNotEmpty, logError, switchTrxnMessage } from '+utils';

import FailedPayoutReasons from '../../Shared/FailedPayoutReasonsModal';
import ReversalsInfoModal from '../../Shared/ReversalsInfoModal';
import TransactionBreakdownModal from '../../Shared/TransactionBreakdownModal';
import TransactionStatusModal from '../../Shared/TransactionStatus';
import TransactionDetails from '../../TransactionDetailsNew';
import { ReversePayoutModal } from '../components/PayoutReversals';
import {
  generateMoreDetailFrom,
  generateProcessingActions,
  generateRecipientInfoFrom,
  generateRemittanceInfoFrom,
  generateSummaryFrom,
  getPayoutActionButtons,
  summaryGenerators
} from './payoutDetailsHelpers';

const api = new APIRequest();
const pageSource = {
  webhookPage: 'web-hook'
};

type ReversalActionType = 'approve' | 'reject' | '';

const PayOutDetails = () => {
  const { id } = useParams<Record<string, string>>();
  const { feedbackInit } = useFeedbackHandler();

  const userAccess = useSetUserAccess() as { [key: string]: boolean };
  const hasAccess = isAllowed(userAccess, ['payout_details.view']);
  const hasPayoutReversalAccess = isAllowed(userAccess, ['payout_reversals.create']);

  const { search } = useLocation();
  const queryParams = new URLSearchParams(search);
  const origin = queryParams.get('origin');

  const [modal, setModal] = useState('');
  const [isLearnMoreModalOpen, setIsLearnMoreModalOpen] = useState(false);

  const [reversalAction, setReversalAction] = useState<ReversalActionType>('');

  useEffect(() => {
    if (userAccess && !hasAccess) {
      history.push('/dashboard/access-denied', null);
    }
  }, [userAccess]);

  const selectAPI = () => {
    if (origin && origin === pageSource.webhookPage) {
      return api.getSingleTransactionFromWebhookServices('payouts', id, 'reference');
    }
    return api.getSingleTransaction('payouts', id);
  };

  const { mutateAsync: processReversal } = useMutation(
    ({ reference, action }: { reference: string; action: string }) => api.processPayoutReversal({ reference, action }),
    {
      onError: (error: any) => {
        feedbackInit({
          message: error?.response?.data?.message || 'Unable to process reversal',
          type: 'danger',
          componentLevel: true
        });
      },
      onSuccess: () => {
        refetchTransaction();
      }
    }
  );

  const {
    data,
    isLoading,
    refetch: refetchTransaction
  } = useQuery(`PAYOUT_DETAILS_${id}`, selectAPI, {
    onError: e => {
      logError(e);
      feedbackInit({
        message: `There has been an error fetching the details for the payout: ${id.toUpperCase()}.`,
        type: 'danger'
      });
      history.goBack();
    },
    enabled: !!hasAccess
  });

  const {
    amount,
    amount_charged: amountCharged,
    payout_reversal: payoutReversal,
    amount_paid: netAmount,
    currency,
    fee,
    vat,
    payment,
    meta
  } = (data || {}) as TTransactionDetails;

  const [isReasonForFailureModalOpen, setIsReasonForFailureModalOpen] = useState(false);
  const { state, updateTransactionStatusModalState, handleProcessingLoader } = useTransactionStatus();
  const [showReversalInitiateModal, setShowReversalInitiateModal] = useState(false);
  const [selectedReversalReference, setSelectedReversalReference] = useState<string>('');
  const merchantName = payment?.account?.name;

  const triggerReasonForFailureModal = () => {
    setIsReasonForFailureModalOpen(true);
  };

  const triggerReversalInitiateModal = () => {
    setShowReversalInitiateModal(true);
  };

  const summaries = generateSummaryFrom(data as IPayOutDetails['data']);
  const actionButtons = getPayoutActionButtons({ data, triggerReversalInitiateModal });
  const moreDetails = generateMoreDetailFrom({ data, triggerReasonForFailureModal, state, updateTransactionStatusModalState, userAccess });
  const recipientInfo = generateRecipientInfoFrom(data as IPayOutDetails['data']);
  const remittanceInfo = generateRemittanceInfoFrom(data as IPayOutDetails['data']);
  const actionGenerators = generateProcessingActions({
    hasCreateAccess: isAllowed(userAccess, ['payout_reversals.create']),
    hasProcessAccess: isAllowed(userAccess, ['payout_reversals.process']),
    onApproveReversal: reference => {
      setSelectedReversalReference(reference);
      setReversalAction('approve');
    },
    onDeclineReversal: reference => {
      setSelectedReversalReference(reference);
      setReversalAction('reject');
    }
  });

  const disputes = {
    refunds: [],
    chargebacks: [],
    reversals: payoutReversal
      ? [
          {
            reference: '',
            amount: '',
            status: '',
            payment_reversal_payouts: [],
            ...payoutReversal
          }
        ]
      : []
  };

  const payoutModalOptions: Record<ReversalActionType, Partial<IModalProps>> = {
    approve: {
      heading: 'Approve Reversal',
      content: (
        <>
          You are about to approve a payout reversal of{' '}
          <b>
            {currency} {formatAmount(amount)}
          </b>
          {merchantName && (
            <>
              {' '}
              for <b>{merchantName}</b>
            </>
          )}
          .
        </>
      ),
      firstButtonText: 'Cancel',
      secondButtonAction: () => processReversal({ reference: selectedReversalReference, action: 'approved' }),
      secondButtonText: 'Yes, Approve',
      completedHeading: 'Reversal Approved',
      completedDescription: 'Transaction reversal has been approved'
    },
    reject: {
      heading: 'Decline Reversal',
      content: (
        <>
          You are about to decline a payout reversal of{' '}
          <b>
            {currency} {formatAmount(amount)}
          </b>
          {merchantName && (
            <>
              {' '}
              for <b>{merchantName}</b>
            </>
          )}
          .
        </>
      ),
      firstButtonText: 'Cancel',
      secondButtonAction: () => processReversal({ reference: selectedReversalReference, action: 'declined' }),
      secondButtonText: 'Yes, Decline',
      completedHeading: 'Reversal Declined',
      completedDescription: 'Transaction reversal has been declined',
      secondButtonColor: '#F32345'
    },
    '': {}
  };

  return (
    <section style={{ padding: '40px' }}>
      <TransactionDetails>
        <TransactionDetails.Header
          heading={formatAmount(data?.amount as number)}
          currency={data?.currency}
          statusLabels={[
            {
              status: switchTrxnMessage[data?.status as IPayOutDetails['status']]?.name,
              statusBg: switchTrxnMessage[data?.status as IPayOutDetails['status']]?.backgroundColor,
              statusColor: switchTrxnMessage[data?.status as IPayOutDetails['status']]?.color
            }
          ]}
          isLoading={isLoading}
          summaries={summaries}
          actionButtons={hasPayoutReversalAccess && data?.status === 'success' ? actionButtons : []}
        />
        <TransactionDetails.Section
          isLoading={isLoading}
          heading="More Transaction Details"
          summaries={moreDetails}
          showLink
          handleLinkClick={() => setModal('transaction_breakdown')}
        />
        {data?.remittance_data && (
          <TransactionDetails.Section isLoading={isLoading} heading="Remittance Information" summaries={remittanceInfo} />
        )}
        <TransactionDetails.Section isLoading={isLoading} heading="Recipient's Information" summaries={recipientInfo} />
        <TransactionDetails.Section
          isLoading={isLoading}
          heading="Refunds, Reversals & Chargebacks"
          showLearnMoreBtn={true}
          openLearnMoreModal={() => setIsLearnMoreModalOpen(true)}
        >
          <TransactionDetails.Disputes
            tabs={['reversals']}
            disputesGenerators={isObjectNotEmpty(payoutReversal) ? disputes : null}
            summaryGenerators={summaryGenerators}
            actionGenerator={actionGenerators}
            childrenGenerators={{}}
            isPayOut
          />
        </TransactionDetails.Section>
      </TransactionDetails>
      {showReversalInitiateModal && (
        <ReversePayoutModal transaction={data} onReversalInitiated={refetchTransaction} close={() => setShowReversalInitiateModal(false)} />
      )}
      {isLearnMoreModalOpen && <ReversalsInfoModal close={() => setIsLearnMoreModalOpen(false)} visible={isLearnMoreModalOpen} />}
      {isReasonForFailureModalOpen && (
        <FailedPayoutReasons
          close={() => setIsReasonForFailureModalOpen(false)}
          visible={isReasonForFailureModalOpen}
          transactions={data}
        />
      )}
      {state.openTransactionStatusModal && (
        <TransactionStatusModal
          activeTransaction={state.activeTransaction}
          updateModalState={updateTransactionStatusModalState}
          triggerProcessingLoader={handleProcessingLoader}
          transactionType={'payout'}
        />
      )}
      {modal === 'transaction_breakdown' && (
        <TransactionBreakdownModal
          transactionData={{
            currency,
            amount_charged: formatAmount(amountCharged),
            amount_paid: formatAmount(amount),
            fees: formatAmount(+fee + (+vat || 0)),
            ...(meta?.additional_fees?.stamp_duty && { stamp_duty_fee: meta?.additional_fees?.stamp_duty }),
            ...(payment?.sentinal_transaction
              ? {
                  tax: formatAmount(Number(payment.sentinal_transaction.vat) + Number(payment.sentinal_transaction.processing_fee)),
                  net_amount: formatAmount(
                    +netAmount - Number(payment.sentinal_transaction.vat) - Number(payment.sentinal_transaction.processing_fee)
                  )
                }
              : { net_amount: netAmount })
          }}
          close={() => setModal('')}
        />
      )}
      {reversalAction ? <Modal size="md" {...payoutModalOptions[reversalAction]} close={() => setReversalAction('')} /> : null}
    </section>
  );
};

export default PayOutDetails;
