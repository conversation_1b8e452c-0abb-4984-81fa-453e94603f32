import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import BulkTransactionList from '../BulkTransactionList';

const MockedBulkTransactionList = () => {
  return (
    <MockIndexWithRoute route="/:id" initialEntries={['/KPY-BPO-2024013117543PZ80411375']}>
      <BulkTransactionList />
    </MockIndexWithRoute>
  );
};

describe('BulkTransactionList', () => {
  test('Bulk payout component is accessible', async () => {
    const { container } = render(<MockedBulkTransactionList />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('renders the payout summary section when bulk status is draft', async () => {
    const { getByText } = render(<MockedBulkTransactionList />);
    await waitFor(() => expect(getByText('Bulk Payout Summary')).toBeInTheDocument());
    await waitFor(() => expect(getByText(/bulk status/i)).toBeInTheDocument());
    await waitFor(() => expect(getByText(/draft/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getAllByText('NGN')).toHaveLength(2));
    await waitFor(() => expect(screen.getByText('2,000.00')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Merchant Name/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Ugo Demo/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Total Bulk Amount (incl. fees)')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/2,053.75/i)).toBeInTheDocument());
  });

  test('renders the table section when bulk status is complete', async () => {
    render(<MockedBulkTransactionList />);
    await waitFor(() => expect(screen.getAllByText('NGN')).toHaveLength(2));
    await waitFor(() => expect(screen.getByText('2,000.00')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Test Account/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('KPY-DD-EDllQljG40PGC8u')).toBeInTheDocument());
  });
});
