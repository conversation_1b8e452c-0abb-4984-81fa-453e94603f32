import React, { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useShallow } from 'zustand/react/shallow';

import useFeedbackHandler from '+hooks/useFeedbackHandler';
import APIRequest from '+services/api-services';
import Modal from '+shared/Modal';
import Typography from '+shared/Typography';
import useReconciliationStore from '+store/reconciliationStore';
import { ReconciliationDataType } from '+types';
import { capitalize, capitalizeFirst, history, logError } from '+utils';

import ReconcileColumnSection from '../components/ReconcileColumnSection';
import ReportProfileCard from '../components/ReportProfileCard';
import useColumnMapping from '../hooks/useColumnMapping';

import './index.scss';

const apiRequest = new APIRequest();

const ReconcileReport = () => {
  const queryClient = useQueryClient();
  const { feedbackInit } = useFeedbackHandler();
  const { autoMatchColumns } = useReconciliationStore(
    useShallow(state => ({
      autoMatchColumns: state.autoMatchColumns
    }))
  );

  const clearStartReconciliationData = useReconciliationStore(state => state.clearStartReconciliationData);
  const setAutoMatchColumns = useReconciliationStore(state => state.setAutoMatchColumns);

  const [internalReportOptions, setInternalReportOptions] = useState<string[]>([]);
  const [processorReportOptions, setProcessorReportOptions] = useState<string[]>([]);
  const [displayPreview, setDisplayPreview] = useState(false);

  const [isCancelled, setIsCancelled] = useState(false);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);

  const {
    handleMappings,
    handleAddNewColumn,
    handleOptionChange,
    handleDelete,
    removingItems,
    updateKeyMappings,
    startReconciliationData,
    primaryKeyMappings,
    comparisonKeyMappings,
    referenceKeyMappings
  } = useColumnMapping();

  useEffect(() => {
    if (!startReconciliationData.processor) {
      history.push('/dashboard/reconciliation/start');
    }
  }, []);

  useEffect(() => {
    if (internalReportOptions.length > 0 || processorReportOptions.length > 0) return;
    setInternalReportOptions(primaryKeyMappings.map(mapping => mapping.internal_report));
    setProcessorReportOptions(primaryKeyMappings.map(mapping => mapping.processor_report));
  }, [primaryKeyMappings]);

  useEffect(() => {
    if (!autoMatchColumns || primaryKeyMappings.length === 0) return;

    const performAutoMatch = () => {
      comparisonKeyMappings.forEach(mapping => {
        let shouldUpdate = false;
        const updateValue: any = {};

        if (mapping.processor_report && !mapping.internal_report) {
          const matchingPrimary = primaryKeyMappings.find(primary => primary.processor_report === mapping.processor_report);
          if (matchingPrimary?.internal_report) {
            updateValue.internal_report = matchingPrimary.internal_report;
            shouldUpdate = true;
          }
        }

        if (mapping.internal_report && !mapping.processor_report) {
          const matchingPrimary = primaryKeyMappings.find(primary => primary.internal_report === mapping.internal_report);
          if (matchingPrimary?.processor_report) {
            updateValue.processor_report = matchingPrimary.processor_report;
            shouldUpdate = true;
          }
        }

        if (shouldUpdate) {
          updateKeyMappings(
            {
              id: mapping.id as string,
              value: updateValue
            },
            'comparisonKeyMappings'
          );
        }
      });

      referenceKeyMappings.forEach(mapping => {
        let shouldUpdate = false;
        const updateValue: any = {};

        if (mapping.processor_report && !mapping.internal_report) {
          const matchingPrimary = primaryKeyMappings.find(primary => primary.processor_report === mapping.processor_report);
          if (matchingPrimary?.internal_report) {
            updateValue.internal_report = matchingPrimary.internal_report;
            shouldUpdate = true;
          }
        }

        if (mapping.internal_report && !mapping.processor_report) {
          const matchingPrimary = primaryKeyMappings.find(primary => primary.internal_report === mapping.internal_report);
          if (matchingPrimary?.processor_report) {
            updateValue.processor_report = matchingPrimary.processor_report;
            shouldUpdate = true;
          }
        }

        if (shouldUpdate) {
          updateKeyMappings(
            {
              id: mapping.id as string,
              value: updateValue
            },
            'referenceKeyMappings'
          );
        }
      });
    };

    const timeoutId = setTimeout(performAutoMatch, 0);
    return () => clearTimeout(timeoutId);
  }, [autoMatchColumns, primaryKeyMappings, comparisonKeyMappings, referenceKeyMappings, updateKeyMappings]);

  const { isLoading } = useQuery(
    [`RECONCILIATION_PROCESSOR_CONFIG_${startReconciliationData.processor}_${startReconciliationData.payment_type}`],
    () => apiRequest.getSettlementReconciliationProcessorConfig(startReconciliationData.processor, startReconciliationData.payment_type),

    {
      refetchOnMount: 'always',
      onSuccess(data) {
        handleMappings(data);
      },
      enabled: !!startReconciliationData.processor && !!startReconciliationData.payment_type
    }
  );

  const createReconciliation = useMutation((value: ReconciliationDataType) => apiRequest.createSettlementReconciliation(value), {
    onSuccess: () => {
      queryClient.invalidateQueries('RECONCILIATIONS');
      feedbackInit({
        message: 'Reconciliation created successfully',
        type: 'success'
      });
      clearStartReconciliationData();
      setIsSuccessModalVisible(true);
    },
    onError: (error: { response: { data: { data: { amount: { message: string } }; message: string } } }) => {
      logError(error.response.data);
      feedbackInit({
        message: error.response.data?.message
          ? `${capitalizeFirst(error.response.data?.message)}`
          : 'There has been an error creating this reconciliation',
        type: 'danger'
      });
    }
  });

  const disableStartReconciliationButton = () => {
    if (comparisonKeyMappings.length === 0) return true;
    if (createReconciliation.isLoading) return true;
    return comparisonKeyMappings.some(mapping => !mapping.internal_report || !mapping.processor_report);
  };
  const handleStartReconciliation = () => {
    createReconciliation.mutate({
      ...startReconciliationData,
      field_mapping: {
        processor: startReconciliationData.processor,
        payment_type: startReconciliationData.payment_type,
        primary_key_mappings: primaryKeyMappings.map(item => ({ ...item, color: undefined, id: undefined, existing: undefined })),
        comparison_key_mappings: comparisonKeyMappings.map(item => ({ ...item, id: undefined, color: undefined, existing: undefined })),
        reference_key_mapping: {
          internal_report: referenceKeyMappings[0]?.internal_report,
          processor_report: referenceKeyMappings[0]?.processor_report
        }
      }
    });
  };

  const handlePreviewDisplay = () => {
    if (processorReportOptions.length === 0) return;
    setDisplayPreview(!displayPreview);
  };

  const handleCancel = () => {
    clearStartReconciliationData();
  };

  const handleGoBack = () => {
    setIsCancelled(true);
  };
  const confirmCancel = () => {
    history.push('/dashboard/reconciliation/start');
  };

  const handleAutoMatchColumns = () => {
    setAutoMatchColumns();
  };

  const handleDismissSuccess = () => {
    setIsSuccessModalVisible(false);
    history.push('/dashboard/reconciliation');
  };

  return (
    <section className="recon-report">
      <section className="recon-report__heading">
        <div className="row mb-3">
          <div className="">
            <button type="button" className="btn btn-link" onClick={handleGoBack}>
              <i className="os-icon os-icon-arrow-left7" />
              <span style={{ fontWeight: 500 }}>Go Back</span>
            </button>
          </div>
        </div>
        <Typography variant="h2" className="recon-report__heading--title">
          Reconcile reports
        </Typography>
        <Typography variant="subtitle4" className="recon-report__heading--description">
          To start the reconciliation process, map each column from the processor's report to their respective columns on Kora's internal
          report and examine the discrepancies.
        </Typography>
      </section>
      <section className="recon-report__content">
        <div className="recon-report__content--card">
          <div className="recon-report__content--card__left">
            <ReportProfileCard
              processor={startReconciliationData.processor}
              numberOfColumns={processorReportOptions.length}
              label={capitalize(startReconciliationData.processor?.split('')[0])}
              displayPreview={handlePreviewDisplay}
              hidePreview={processorReportOptions.length === 0}
            />
          </div>
          <div className="recon-report__content--card__right">
            <ReportProfileCard hidePreview numberOfColumns={internalReportOptions.length} label={'K'} />
          </div>
        </div>

        <ReconcileColumnSection
          comparisonKeyMappings={comparisonKeyMappings}
          primaryKeyMappings={primaryKeyMappings}
          handleOptionChange={(value, field, id, type) => handleOptionChange(value, field, id, type)}
          handleDelete={id => handleDelete(id, 'comparisonKeyMappings')}
          handleAddNewColumn={() => handleAddNewColumn('comparisonKeyMappings')}
          disableStartReconciliationButton={disableStartReconciliationButton}
          handleStartReconciliation={handleStartReconciliation}
          handleCancel={handleCancel}
          handleAutoMatchColumns={handleAutoMatchColumns}
          autoMatchColumns={autoMatchColumns}
          removingItems={removingItems}
          processorReportOptions={processorReportOptions}
          displayPreview={displayPreview}
          handlePreviewDisplay={handlePreviewDisplay}
          createReconciliation={createReconciliation}
          isLoading={isLoading}
          referenceKeyMappings={referenceKeyMappings}
        />
      </section>
      <Modal
        visible={isCancelled}
        description={
          <p>
            Please confirm that you want to cancel this reconciliation. Any progress you have made will be lost. This action cannot be
            undone.
          </p>
        }
        heading="Go back?"
        close={() => setIsCancelled(false)}
        firstButtonText="Back"
        firstButtonAction={() => setIsCancelled(false)}
        secondButtonText="Yes, Cancel"
        secondButtonAction={() => {
          setIsCancelled(false);
          confirmCancel();
        }}
        size="sm"
        secondButtonColor="red"
      />
      <Modal
        secondaryCompletedModal
        visible={isSuccessModalVisible}
        description="Success!"
        completedDescription="Your reconciliation request was successful. The link to download the report will be sent to your work email."
        completedActionText="Dismiss"
        completedAction={handleDismissSuccess}
        secondButtonActionIsTerminal
        size="sm"
        stage="complete"
        close={handleDismissSuccess}
      />
    </section>
  );
};

export default ReconcileReport;
