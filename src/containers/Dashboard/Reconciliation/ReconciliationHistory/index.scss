@import '+styles/kpy-custom/_custom';
@import '+styles/kpy-custom/variables';

.recon-container {
  font-family: 'Averta PE', sans-serif;
  padding: 2rem 3rem;
  &__heading {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    &--left {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      max-width: 680px;

      &--title {
        font-weight: 600;
        font-size: 28px;
      }

      &__description {
        max-width: 75ch;
        margin-top: 1rem;
        line-height: 1.5;
      }
    }

    &--right {
      display: flex;
      align-items: center;
      flex-direction: column;
      row-gap: 1rem;

      .column-menu-wrapper {
        margin-top: 0.5rem;

        .hover-menu__list {
          & > li {
            background: #fff;

            button{
              color: #000;
            }
          }
        }
      }
    }
  }

  &__content {
    margin-top: 5rem;
    background-color: #f9fbfd;
    height: 50vh;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
  }

  &__table {
    margin-top: 3rem;
    &--info {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1rem;
      background-color: #ebedff;
      border-radius: 5px;
      margin-block: 2rem;
      p {
        font-size: 0.8rem;
        color: #3e4b5b;
        font-weight: 600;
        margin-bottom: 0;
      }
    }
  }
}
