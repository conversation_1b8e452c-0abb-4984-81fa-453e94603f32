import React, { useEffect } from 'react';
import { useMutation, useQuery } from 'react-query';

import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceHolder';
import { useFeedbackHandler, useReducerState } from '+hooks';
import APIRequest from '+services/api-services';
import Modal from '+shared/Modal';
import Typography from '+shared/Typography';
import { IManageColumnFormData, ProcessorFormMappingType } from '+types';
import { capitalize, logError } from '+utils';

import ReconcileColumnProcessor from '../components/ReconcileColumnProcessor';
import ReconciliationOptionRowCard from '../components/ReconciliationOptionRowCard';
import { validateColumn } from '../helpers/reconcileReportHelper';
import useColumnMapping from '../hooks/useColumnMapping';

import './index.scss';

const apiRequest = new APIRequest();
const AddColumns = () => {
  const { feedbackInit } = useFeedbackHandler();

  const {
    handleDelete,
    handleAddNewColumn,
    handleOptionChange,
    removingItems,
    primaryKeyMappings,
    comparisonKeyMappings,
    referenceKeyMappings,
    statusKeyMappings,
    handleMappings,
    handleCancel,
    setStartReconciliationData,
    startReconciliationData,
    setDefaultData,
    handleCloseSuccessModal
  } = useColumnMapping();

  const [state, setState] = useReducerState<ProcessorFormMappingType & { displaySuccessModal: boolean }>({
    processor: startReconciliationData.processor,
    payment_type: startReconciliationData.payment_type,
    displaySuccessModal: false
  });

  useEffect(() => {
    const hasExistingData =
      primaryKeyMappings.length > 0 || comparisonKeyMappings.length > 0 || referenceKeyMappings.length > 0 || statusKeyMappings.length > 0;

    if (!state.processor || !state.payment_type) {
      if (!hasExistingData) {
        setDefaultData();
      }
    }
  }, []);

  const shouldShowLoading = !!state.processor && !!state.payment_type;

  const shouldShowDefaultFields = !state.processor || !state.payment_type;

  const { data, isLoading, isSuccess, isError } = useQuery(
    [`RECONCILIATION_PROCESSOR_CONFIG_${state.payment_type}_${state.processor}`],
    () => apiRequest.getSettlementReconciliationProcessorConfig(state.processor, state.payment_type),
    {
      refetchOnMount: 'always',
      staleTime: 0,
      onSuccess(queryData) {
        handleMappings(queryData);
      },
      enabled: shouldShowLoading
    }
  );

  useEffect(() => {
    setStartReconciliationData({ ...startReconciliationData, processor: state.processor, payment_type: state.payment_type });

    if (shouldShowDefaultFields) {
      const hasExistingData =
        primaryKeyMappings.length > 0 ||
        comparisonKeyMappings.length > 0 ||
        referenceKeyMappings.length > 0 ||
        statusKeyMappings.length > 0;
      if (!hasExistingData) {
        setDefaultData();
      }
      return;
    }

    if (!isSuccess && !isError) return;

    const noMappingsPresent =
      primaryKeyMappings.length === 0 &&
      comparisonKeyMappings.length === 0 &&
      referenceKeyMappings.length === 0 &&
      statusKeyMappings.length === 0;

    if (noMappingsPresent && (!data || (data?.data?.length ?? 0) === 0)) {
      setDefaultData();
    }
  }, [
    state.processor,
    state.payment_type,
    isSuccess,
    isError,
    data,
    primaryKeyMappings.length,
    comparisonKeyMappings.length,
    referenceKeyMappings.length,
    statusKeyMappings.length,
    shouldShowDefaultFields
  ]);

  const handleSetState = (newState: Partial<typeof state>) => {
    setState({ ...state, ...newState });
  };
  const createSettlementConfigMutation = useMutation((value: IManageColumnFormData) => apiRequest.createOrUpdateProcessorConfig(value), {
    onSuccess: () => {
      setState({ ...state, displaySuccessModal: true });
    },
    onError: (error: { response: { data: { data: { amount: { message: string } }; message: string } } }) => {
      logError(error);

      feedbackInit({
        message: capitalize(
          `${error?.response?.data?.data?.amount?.message ?? error?.response?.data?.message}` ||
            'There has been an error creating this settlement config'
        ),
        type: 'danger'
      });
    }
  });

  const handleAddColumns = () => {
    const statusEntries = statusKeyMappings.filter(item => {
      if (!item.internal_report || !item.processor_report) return false;
      if (item.internal_report === 'pending' && item.processor_report === 'pending') return false;
      return true;
    });
    const statusValues = Array.from(new Set(statusEntries.map(item => item.internal_report!.trim()).filter(Boolean)));

    const sanitize = (arr: typeof primaryKeyMappings) =>
      arr.map(({ internal_report, processor_report }) => ({ internal_report, processor_report }));

    const newData = {
      processor: state.processor,
      payment_type: state.payment_type,
      primary_key_mappings: sanitize(primaryKeyMappings),
      comparison_key_mappings: sanitize(comparisonKeyMappings),
      reference_key_mapping: sanitize(referenceKeyMappings)[0],
      metadata: {
        statusMap: (() => {
          const map = statusEntries.reduce(
            (acc, item) => {
              acc[item.internal_report as string] = item.processor_report as string;
              return acc;
            },
            {} as Record<string, string>
          );
          return Object.keys(map).length ? map : undefined;
        })(),
        status: statusValues.length ? [...statusValues, ...data?.data[0]?.metadata?.status] : data?.data[0]?.metadata?.status
      }
    } satisfies IManageColumnFormData;
    createSettlementConfigMutation.mutate(newData);
  };

  return (
    <section className="element-box manage-columns">
      <div className="row manage-columns__back">
        <button type="button" className="btn btn-link" onClick={handleCancel}>
          <i className="os-icon os-icon-arrow-left7" />
          <span style={{ fontWeight: 500 }}>Back</span>
        </button>
      </div>
      <div className="manage-columns__title">
        <Typography variant="h4" className="mb-20">
          Add Reconciliation Columns
        </Typography>
        <Typography variant="subtitle4">
          Add columns to selected processor and payment type. They will be available for selection during reconciliation
        </Typography>
      </div>
      <section className="manage-columns__content">
        <ReconcileColumnProcessor state={state} setState={handleSetState} />

        {shouldShowLoading &&
        isLoading &&
        primaryKeyMappings.length === 0 &&
        comparisonKeyMappings.length === 0 &&
        referenceKeyMappings.length === 0 &&
        statusKeyMappings.length === 0 ? (
          <LoadingPlaceholder type="text" content={2} rows={2} section={2} />
        ) : (
          <>
            <ReconciliationOptionRowCard
              mappings={primaryKeyMappings}
              handleAddNewColumn={() => handleAddNewColumn('primaryKeyMappings')}
              handleDelete={(id: string) => handleDelete(id, 'primaryKeyMappings')}
              handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'primaryKeyMappings')}
              label="Primary Mapping"
              isLoading={false}
              removingItems={removingItems}
              message="Primary Mapping connects columns containing the same data, even if their column header names differ between the Internal and Processor reports."
              fieldType="text"
              mode="add"
            />

            <ReconciliationOptionRowCard
              mappings={referenceKeyMappings}
              optionKeyMappings={primaryKeyMappings}
              handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'referenceKeyMappings')}
              label="Reference Mapping"
              isLoading={false}
              message="Reference Mapping uses a unique identifier common to both reports to match data between Kora and the Processor."
              mode="add"
            />

            <ReconciliationOptionRowCard
              mappings={comparisonKeyMappings}
              optionKeyMappings={primaryKeyMappings}
              handleAddNewColumn={() => handleAddNewColumn('comparisonKeyMappings')}
              handleDelete={(id: string) => handleDelete(id, 'comparisonKeyMappings')}
              handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'comparisonKeyMappings')}
              label="Comparison Mapping"
              isLoading={false}
              removingItems={removingItems}
              message="Comparison Mapping compares similar columns during the reconciliation process to identify matches or discrepancies."
              mode="add"
            />

            <ReconciliationOptionRowCard
              mappings={statusKeyMappings}
              handleAddNewColumn={() => handleAddNewColumn('statusKeyMappings')}
              handleDelete={(id: string) => handleDelete(id, 'statusKeyMappings')}
              handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'statusKeyMappings')}
              label="Status Mapping (optional)"
              isLoading={false}
              removingItems={removingItems}
              message="Status Mapping links different status terms that have the same meaning across the Internal and Processor reports."
              fieldType="text"
              controlFieldDisplay
              mode="add"
            />
          </>
        )}
      </section>
      <section className="manage-columns__action">
        <button className="btn text-danger" onClick={handleCancel}>
          Cancel
        </button>
        <button
          className="btn btn-primary"
          onClick={handleAddColumns}
          disabled={
            createSettlementConfigMutation.isLoading ||
            validateColumn(state.processor, primaryKeyMappings, referenceKeyMappings, comparisonKeyMappings, statusKeyMappings)
          }
        >
          {createSettlementConfigMutation.isLoading ? 'Adding...' : 'Add Columns'}
        </button>
      </section>
      <Modal
        visible={state.displaySuccessModal}
        close={handleCloseSuccessModal}
        secondaryCompletedModal
        completedModalSize="md"
        stage="complete"
        completedDescription={`New columns have been added to ${capitalize(state.processor)} processor as Primary Keys, Reference Keys, Comparison Keys and Status Mappings`}
        completedActionText="Dismiss"
      />
    </section>
  );
};

export default AddColumns;
