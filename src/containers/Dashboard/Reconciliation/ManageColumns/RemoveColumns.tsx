import React from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useDebounce } from 'react-use';



import Icon from '+containers/Dashboard/Shared/Icons';
import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import EmptyStateComponent from '+shared/EmptyState';
import LoadingPlaceholder from '+shared/LoadingPlaceHolder';
import Modal from '+shared/Modal';
import Typography from '+shared/Typography';
import { IManageColumnFormData, KeyMappingType, MappingType } from '+types';
import { capitalize, logError } from '+utils';



import ReconcileColumnProcessor from '../components/ReconcileColumnProcessor';
import ReconciliationOptionRowCard from '../components/ReconciliationOptionRowCard';
import { validateColumn } from '../helpers/reconcileReportHelper';
import useColumnMapping from '../hooks/useColumnMapping';






import './index.scss';





const apiRequest = new APIRequest();
const RemoveColumns = () => {
  const queryClient = useQueryClient();
  const { feedbackInit } = useFeedbackHandler();
  const [consent, setConsent] = React.useState(false);

  const {
    handleMappings,
    handleOptionChange,
    primaryKeyMappings,
    comparisonKeyMappings,
    referenceKeyMappings,
    handleCancel,
    setStartReconciliationData,
    startReconciliationData,
    handleBulkDelete,
    bulkDeleteIds,
    statusKeyMappings,
    state,
    setState
  } = useColumnMapping();

  useDebounce(
    () => {
      setStartReconciliationData({ ...startReconciliationData, processor: state.processor });
    },
    500,
    [state.processor]
  );
  useDebounce(
    () => {
      setStartReconciliationData({ ...startReconciliationData, payment_type: state.payment_type });
    },
    500,
    [state.payment_type]
  );

  const {
    data: dataValue,
    isLoading,
    refetch
  } = useQuery(
    [`RECONCILIATION_PROCESSOR_CONFIG_${state.payment_type}_${state.processor}`],
    () => apiRequest.getSettlementReconciliationProcessorConfig(state.processor, state.payment_type),
    {
      refetchOnMount: 'always',
      staleTime: 0,
      onSuccess(data) {
        handleMappings(data);
      },
      enabled: !!state.processor && !!state.payment_type
    }
  );

  const handleSetState = (newState: Partial<typeof state>) => {
    setState({ ...state, ...newState });
  };

  const createSettlementConfigMutation = useMutation((value: IManageColumnFormData) => apiRequest.createOrUpdateProcessorConfig(value), {
    onSuccess: () => {
      setState({ ...state, displaySuccessModal: true });
    },
    onError: (error: { response: { data: { data: { amount: { message: string } }; message: string } } }) => {
      logError(error);

      feedbackInit({
        message: capitalize(
          `${error?.response?.data?.data?.amount?.message ?? error?.response?.data?.message}` ||
            'There has been an error creating this settlement config'
        ),
        type: 'danger'
      });
    }
  });

  const removeSettlementConfigMutation = useMutation((value: IManageColumnFormData) => apiRequest.createOrUpdateProcessorConfig(value), {
    onSuccess: () => {
      setState({ ...state, displaySuccessModal: true });
    },
    onError: (error: { response: { data: { data: { amount: { message: string } }; message: string } } }) => {
      logError(error);

      feedbackInit({
        message: capitalize(
          `${error?.response?.data?.data?.amount?.message ?? error?.response?.data?.message}` || 'There has been an error removing columns'
        ),
        type: 'danger'
      });
    }
  });

  const handleRemoveColumnsModal = () => {
    setState({ ...state, displaySuccessModal: true });
  };

  const findEquivalentItems = (selectedItem: KeyMappingType[0], targetMappings: KeyMappingType) => {
    return targetMappings.filter(
      item => item.processor_report === selectedItem.processor_report && item.internal_report === selectedItem.internal_report
    );
  };

  const handleCascadingBulkDelete = (ids: string[], mappingType: MappingType) => {
    if (mappingType === 'primaryKeyMappings') {
      const currentlySelected = bulkDeleteIds.primaryKeyMappings;
      const newlySelected = ids.filter(id => !currentlySelected.includes(id));
      const newlyDeselected = currentlySelected.filter(id => !ids.includes(id));

      let cascadingComparisonIds = [...bulkDeleteIds.comparisonKeyMappings];
      let cascadingReferenceIds = [...bulkDeleteIds.referenceKeyMappings];
      let cascadingStatusIds = [...bulkDeleteIds.statusKeyMappings];

      newlySelected.forEach(primaryId => {
        const primaryItem = primaryKeyMappings.find(item => item.id === primaryId);
        if (primaryItem) {
          const equivalentComparison = findEquivalentItems(primaryItem, comparisonKeyMappings);
          const equivalentReference = findEquivalentItems(primaryItem, referenceKeyMappings);
          const equivalentStatus = findEquivalentItems(primaryItem, statusKeyMappings);

          cascadingComparisonIds.push(...equivalentComparison.map(item => item.id || ''));
          cascadingReferenceIds.push(...equivalentReference.map(item => item.id || ''));
          cascadingStatusIds.push(...equivalentStatus.map(item => item.id || ''));
        }
      });

      newlyDeselected.forEach(primaryId => {
        const primaryItem = primaryKeyMappings.find(item => item.id === primaryId);
        if (primaryItem) {
          const equivalentComparison = findEquivalentItems(primaryItem, comparisonKeyMappings);
          const equivalentReference = findEquivalentItems(primaryItem, referenceKeyMappings);
          const equivalentStatus = findEquivalentItems(primaryItem, statusKeyMappings);

          const comparisonIdsToRemove = equivalentComparison.map(item => item.id || '');
          const referenceIdsToRemove = equivalentReference.map(item => item.id || '');
          const statusIdsToRemove = equivalentStatus.map(item => item.id || '');

          cascadingComparisonIds = cascadingComparisonIds.filter(id => !comparisonIdsToRemove.includes(id));
          cascadingReferenceIds = cascadingReferenceIds.filter(id => !referenceIdsToRemove.includes(id));
          cascadingStatusIds = cascadingStatusIds.filter(id => !statusIdsToRemove.includes(id));
        }
      });

      cascadingComparisonIds = [...new Set(cascadingComparisonIds)];
      cascadingReferenceIds = [...new Set(cascadingReferenceIds)];
      cascadingStatusIds = [...new Set(cascadingStatusIds)];

      handleBulkDelete(ids, 'primaryKeyMappings');
      handleBulkDelete(cascadingComparisonIds, 'comparisonKeyMappings');
      handleBulkDelete(cascadingReferenceIds, 'referenceKeyMappings');
      handleBulkDelete(cascadingStatusIds, 'statusKeyMappings');
    } else {
      handleBulkDelete(ids, mappingType);
    }
  };

  const isPrimaryEquivalentSelected = (item: KeyMappingType[0]) => {
    return bulkDeleteIds.primaryKeyMappings.some(primaryId => {
      const primaryItem = primaryKeyMappings.find(p => p.id === primaryId);
      if (!primaryItem) return false;
      return primaryItem.processor_report === item.processor_report && primaryItem.internal_report === item.internal_report;
    });
  };

  const validateRemoveColumns = () => {
    // Check if no items are selected for removal
    const hasSelections =
      bulkDeleteIds.primaryKeyMappings.length > 0 ||
      bulkDeleteIds.comparisonKeyMappings.length > 0 ||
      bulkDeleteIds.referenceKeyMappings.length > 0 ||
      bulkDeleteIds.statusKeyMappings.length > 0;

    if (!hasSelections) return true; // Disable button when no selections

    const remainingPrimary = primaryKeyMappings.filter(item => !bulkDeleteIds.primaryKeyMappings.includes(item.id || ''));
    const remainingComparison = comparisonKeyMappings.filter(item => !bulkDeleteIds.comparisonKeyMappings.includes(item.id || ''));
    const remainingReference = referenceKeyMappings.filter(item => !bulkDeleteIds.referenceKeyMappings.includes(item.id || ''));

    if (remainingPrimary.length === 0 || remainingComparison.length === 0 || remainingReference.length === 0) return true;

    const unenforcedEquivalentExists = bulkDeleteIds.primaryKeyMappings.some(primaryId => {
      const primaryItem = primaryKeyMappings.find(item => item.id === primaryId);
      if (!primaryItem) return false;
      const compEquivalents = findEquivalentItems(primaryItem, comparisonKeyMappings).filter(
        eq => !bulkDeleteIds.comparisonKeyMappings.includes(eq.id || '')
      );
      const refEquivalents = findEquivalentItems(primaryItem, referenceKeyMappings).filter(
        eq => !bulkDeleteIds.referenceKeyMappings.includes(eq.id || '')
      );
      const statusEquivalents = findEquivalentItems(primaryItem, statusKeyMappings).filter(
        eq => !bulkDeleteIds.statusKeyMappings.includes(eq.id || '')
      );
      return compEquivalents.length > 0 || refEquivalents.length > 0 || statusEquivalents.length > 0;
    });

    if (unenforcedEquivalentExists) return true;

    return false;
  };

  const isLastItemInSection = (mappingType: 'primaryKeyMappings' | 'comparisonKeyMappings' | 'referenceKeyMappings', itemId: string) => {
    let mappings;
    if (mappingType === 'primaryKeyMappings') {
      mappings = primaryKeyMappings;
    } else if (mappingType === 'comparisonKeyMappings') {
      mappings = comparisonKeyMappings;
    } else {
      mappings = referenceKeyMappings;
    }

    const bulkIds = bulkDeleteIds[mappingType];

    if (mappingType === 'primaryKeyMappings') {
      const unselectedItems = mappings.filter(item => !bulkIds.includes(item.id || '') || item.id === itemId);
      return unselectedItems.length === 1 && unselectedItems[0].id === itemId;
    }

    let effectiveSelectedIds = [...bulkIds];

    bulkDeleteIds.primaryKeyMappings.forEach(primaryId => {
      const primaryItem = primaryKeyMappings.find(item => item.id === primaryId);
      if (primaryItem) {
        const equivalentItems = findEquivalentItems(primaryItem, mappings);
        effectiveSelectedIds.push(...equivalentItems.map(item => item.id || ''));
      }
    });

    effectiveSelectedIds = [...new Set(effectiveSelectedIds)];

    const unselectedItems = mappings.filter(item => !effectiveSelectedIds.includes(item.id || '') || item.id === itemId);
    return unselectedItems.length === 1 && unselectedItems[0].id === itemId;
  };

  const handleRemoveColumns = async () => {
    const filterAndValidate = (mappings: KeyMappingType, bulkIds: string[]) =>
      mappings
        .filter(item => !bulkIds.includes(item.id || ''))
        .filter(item => item.internal_report?.trim() && item.processor_report?.trim())
        .map(item => ({
          internal_report: item.internal_report,
          processor_report: item.processor_report
        }));

    const filteredPrimary = filterAndValidate(primaryKeyMappings, bulkDeleteIds.primaryKeyMappings);
    const filteredComparison = filterAndValidate(comparisonKeyMappings, bulkDeleteIds.comparisonKeyMappings);
    const filteredReference = filterAndValidate(referenceKeyMappings, bulkDeleteIds.referenceKeyMappings);
    const filteredStatus = filterAndValidate(statusKeyMappings, bulkDeleteIds.statusKeyMappings);

    const statusMap = filteredStatus.reduce(
      (acc, item) => {
        acc[item.internal_report] = item.processor_report;
        return acc;
      },
      {} as Record<string, string>
    );
console.log('Filtered Primary:', filteredStatus);
    const data: IManageColumnFormData = {
      processor: state.processor,
      payment_type: state.payment_type,
      primary_key_mappings: filteredPrimary,
      comparison_key_mappings: filteredComparison,
      reference_key_mapping: filteredReference[0],
      metadata: {
        statusMap: Object.keys(statusMap).length ? statusMap : undefined,
        status: filteredStatus.length ? filteredStatus.map(item => item.internal_report) : dataValue?.data[0]?.metadata?.status
      }
    };
    console.log('Data to be sent for removal:', data);
    // await removeSettlementConfigMutation.mutateAsync(data);
  };

  const isEmpty =
    primaryKeyMappings.length === 0 &&
    comparisonKeyMappings.length === 0 &&
    referenceKeyMappings.length === 0 &&
    statusKeyMappings.length === 0;
  const renderContent = () => {
    if (isLoading) {
      return <LoadingPlaceholder type="text" content={2} rows={2} section={2} />;
    }

    if (isEmpty) {
      return (
        <EmptyStateComponent heading="No Config Details" message="Select a processor and payment type to view configuration details." />
      );
    }

    return (
      <>
        <ReconciliationOptionRowCard
          mappings={primaryKeyMappings}
          handleBulkDelete={(id: string[]) => handleCascadingBulkDelete(id, 'primaryKeyMappings')}
          handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'primaryKeyMappings')}
          label="Primary Mapping"
          isLoading={false}
          removingItems={new Set([...bulkDeleteIds.primaryKeyMappings])}
          message="Primary Mapping connects columns containing the same data, even if their column header names differ between the Internal and Processor reports."
          fieldType="text"
          mode="remove"
          isItemDisabled={itemId => isLastItemInSection('primaryKeyMappings', itemId)}
        />
        <ReconciliationOptionRowCard
          mappings={referenceKeyMappings}
          optionKeyMappings={primaryKeyMappings}
          handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'referenceKeyMappings')}
          label="Reference Mapping"
          isLoading={false}
          removingItems={new Set([...bulkDeleteIds.referenceKeyMappings])}
          message="Reference Mapping uses a unique identifier common to both reports to match data between Kora and the Processor."
          mode="remove"
          handleBulkDelete={(id: string[]) => handleCascadingBulkDelete(id, 'referenceKeyMappings')}
          isItemDisabled={itemId => {
            const base = isLastItemInSection('referenceKeyMappings', itemId);
            const item = referenceKeyMappings.find(i => i.id === itemId);
            return base || (item ? isPrimaryEquivalentSelected(item) : false);
          }}
        />
        <ReconciliationOptionRowCard
          mappings={comparisonKeyMappings}
          optionKeyMappings={primaryKeyMappings}
          handleBulkDelete={(id: string[]) => handleCascadingBulkDelete(id, 'comparisonKeyMappings')}
          handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'comparisonKeyMappings')}
          label="Comparison Mapping"
          isLoading={false}
          removingItems={new Set([...bulkDeleteIds.comparisonKeyMappings])}
          message="Comparison Mapping compares similar columns during the reconciliation process to identify matches or discrepancies."
          mode="remove"
          isItemDisabled={itemId => {
            const base = isLastItemInSection('comparisonKeyMappings', itemId);
            const item = comparisonKeyMappings.find(i => i.id === itemId);
            return base || (item ? isPrimaryEquivalentSelected(item) : false);
          }}
        />
        <ReconciliationOptionRowCard
          mappings={statusKeyMappings}
          handleBulkDelete={(id: string[]) => handleCascadingBulkDelete(id, 'statusKeyMappings')}
          handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'statusKeyMappings')}
          label="Status Mapping (optional)"
          isLoading={false}
          removingItems={new Set([...bulkDeleteIds.statusKeyMappings])}
          message="Status Mapping links different status terms that have the same meaning across the Internal and Processor reports."
          fieldType="text"
          mode="remove"
          controlFieldDisplay={statusKeyMappings.length === 0}
          isItemDisabled={itemId => {
            const item = statusKeyMappings.find(i => i.id === itemId);
            return item ? isPrimaryEquivalentSelected(item) : false;
          }}
        />
      </>
    );
  };

  return (
    <section className="element-box manage-columns">
      <div className="row manage-columns__back">
        <button type="button" className="btn btn-link" onClick={handleCancel}>
          <i className="os-icon os-icon-arrow-left7" />
          <span style={{ fontWeight: 500 }}>Back</span>
        </button>
      </div>
      <div className="manage-columns__title">
        <Typography variant="h4" className="mb-20">
          Remove Reconciliation Columns
        </Typography>
        <Typography variant="subtitle4">
          Remove columns from the selected processor and payment type. These columns will no longer be available for selection during
          reconciliation
        </Typography>
      </div>
      <section className="manage-columns__content">
        <ReconcileColumnProcessor state={state} setState={handleSetState} />
        {renderContent()}
      </section>
      {!isEmpty && (
        <section className="manage-columns__action">
          <button className="btn text-danger" onClick={handleCancel}>
            Cancel
          </button>
          <button
            className="btn btn-danger"
            onClick={handleRemoveColumnsModal}
            disabled={
              createSettlementConfigMutation.isLoading ||
              validateColumn(state.processor, primaryKeyMappings, referenceKeyMappings, comparisonKeyMappings, statusKeyMappings) ||
              validateRemoveColumns()
            }
          >
            {'Remove Selected Columns'}
          </button>
        </section>
      )}
      <Modal
        visible={state.displaySuccessModal}
        close={() => {
          (setState({ ...state, displaySuccessModal: false }), refetch());
        }}
        heading="Are you sure?"
        description={`Are you sure you want to remove the selected columns from the ${capitalize(state.processor)} processor?`}
        secondButtonText="Yes, Remove"
        secondaryCompletedModal
        completedModalSize="md"
        secondButtonAction={async () => {
          await handleRemoveColumns();
        }}
        secondButtonDisable={!consent}
        size="mdBase"
        secondButtonColor="red"
        content={
          <div className="manage-columns--remove-consent">
            <div className="info-wrapper">
              <Icon name="infoRounded" fill="#F32345" height={16} width={16} />
              <p className="disable">
                <span>Warning:</span> This means that the column pair will not be available for reconciliation.
              </p>
            </div>
            <label className="prod-consent-wrapper">
              <input checked={consent} type="checkbox" onChange={() => setConsent(!consent)} data-testid="consent-checkbox" />
              <span>Yes, I understand the implications of this action</span>
            </label>
          </div>
        }
        completedDescription="Selected column pair have been removed."
        completedActionText="Dismiss"
      />
    </section>
  );
};

export default RemoveColumns;
