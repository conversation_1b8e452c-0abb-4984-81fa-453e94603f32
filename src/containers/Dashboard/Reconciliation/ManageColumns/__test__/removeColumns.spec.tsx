import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { beforeEach, describe, expect, it } from 'vitest';

import { mockProcessorConfigResponseForRemoval, mockRemoveColumnsStoreState } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';
import useReconciliationStore from '+store/reconciliationStore';

import RemoveColumns from '../RemoveColumns';

const MockedRemoveColumns = () => (
  <MockIndex>
    <RemoveColumns />
  </MockIndex>
);

describe('RemoveColumns', () => {
  beforeEach(() => {
    useReconciliationStore.setState({
      startReconciliationData: mockRemoveColumnsStoreState,
      primaryKeyMappings: [],
      comparisonKeyMappings: [],
      referenceKeyMappings: [],
      statusKeyMappings: [],
      bulkDeleteIds: { primaryKeyMappings: [], comparisonKeyMappings: [], referenceKeyMappings: [], statusKeyMappings: [] }
    });

    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');
        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(mockProcessorConfigResponseForRemoval, { status: 200 });
        }
        return HttpResponse.json({ status: true, data: [] }, { status: 200 });
      })
    );
  });

  it('loads config and renders sections including Status Mapping', async () => {
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Remove Reconciliation Columns')).toBeInTheDocument();
    });

    expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    expect(screen.getByText('Reference Mapping')).toBeInTheDocument();
    expect(screen.getByText('Comparison Mapping')).toBeInTheDocument();
    expect(screen.getByText('Status Mapping (optional)')).toBeInTheDocument();
  });

  it('has no major accessibility violations on initial load (excluding known label issues for legacy inputs)', async () => {
    const { container } = render(<MockedRemoveColumns />);
    await waitFor(() => {
      expect(screen.getByText('Remove Reconciliation Columns')).toBeInTheDocument();
    });

    const results = await axe(container, {
      rules: {
        label: { enabled: false },
        'label-title-only': { enabled: false }
      }
    });
    expect(results).toHaveNoViolations();
  });

  it('cascades bulk selections from Primary to related sections and persists across refresh', async () => {
    const user = userEvent.setup();
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      expect(state.primaryKeyMappings.length).toBeGreaterThan(0);
    });

    const checkboxes = screen.getAllByRole('checkbox').filter(cb => cb.getAttribute('data-testid') !== 'consent-checkbox');
    expect(checkboxes.length).toBeGreaterThan(0);

    await user.click(checkboxes[0]);
    if (checkboxes.length > 1) {
      await user.click(checkboxes[1]);
    }

    const state1 = useReconciliationStore.getState();
    expect(state1.bulkDeleteIds.primaryKeyMappings.length).toBeGreaterThan(0);

    expect(state1.bulkDeleteIds.comparisonKeyMappings.length).toBeGreaterThanOrEqual(0);
    expect(state1.bulkDeleteIds.referenceKeyMappings.length).toBeGreaterThanOrEqual(0);
    expect(state1.bulkDeleteIds.statusKeyMappings.length).toBeGreaterThanOrEqual(0);

    render(<MockedRemoveColumns />);

    const state2 = useReconciliationStore.getState();
    expect(state2.bulkDeleteIds.primaryKeyMappings.length).toBe(state1.bulkDeleteIds.primaryKeyMappings.length);
  });

  it('disables Remove Selected Columns button when removal would leave an empty section', async () => {
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      expect(state.primaryKeyMappings.length).toBeGreaterThan(0);
    });

    const removeButton = screen.getByRole('button', { name: 'Remove Selected Columns' });
    expect(removeButton).toBeDisabled();
  });

  it('auto-selects and disables equivalent comparison/reference/status mappings when a primary is selected (user flow)', async () => {
    const user = userEvent.setup();
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    const state = useReconciliationStore.getState();
    const initialPrimaryLength = state.primaryKeyMappings.length;
    expect(initialPrimaryLength).toBeGreaterThan(0);

    const checkboxes = screen.getAllByRole('checkbox').filter(cb => cb.getAttribute('data-testid') !== 'consent-checkbox');
    const primaryCheckbox = checkboxes[0];
    await user.click(primaryCheckbox);

    const updated = useReconciliationStore.getState();
    expect(updated.bulkDeleteIds.primaryKeyMappings.length).toBe(1);

    const selectedPrimary = state.primaryKeyMappings.find(p => p.id === updated.bulkDeleteIds.primaryKeyMappings[0]);
    if (selectedPrimary) {
      const compEquivalents = updated.comparisonKeyMappings.filter(
        c => c.processor_report === selectedPrimary.processor_report && c.internal_report === selectedPrimary.internal_report
      );
      compEquivalents.forEach(eq => {
        expect(updated.bulkDeleteIds.comparisonKeyMappings).toContain(eq.id);
      });
    }

    const disabledChecked = checkboxes.filter(
      cb => cb !== primaryCheckbox && (cb as HTMLInputElement).checked && (cb as HTMLInputElement).disabled
    );
    expect(disabledChecked.length).toBeGreaterThanOrEqual(0);
  });

  it('keeps remove button disabled when deletion would remove the only reference mapping (user flow)', async () => {
    const user = userEvent.setup();
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    const checkboxes = screen.getAllByRole('checkbox').filter(cb => cb.getAttribute('data-testid') !== 'consent-checkbox');
    await user.click(checkboxes[0]);

    const removeBtn = screen.getByRole('button', { name: 'Remove Selected Columns' });
    expect(removeBtn).toBeDisabled();
  });

  it('requires consent before enabling confirm removal (user flow)', async () => {
    const user = userEvent.setup();
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    const checkboxes = screen.getAllByRole('checkbox').filter(cb => cb.getAttribute('data-testid') !== 'consent-checkbox');
    await user.click(checkboxes[1]);

    const removeBtn = screen.getByRole('button', { name: 'Remove Selected Columns' });
    await waitFor(() => expect(removeBtn).not.toBeDisabled());

    await user.click(removeBtn);
    const confirmBtn = screen.getByTestId('second-button');
    expect(confirmBtn).toBeDisabled();
    await user.click(screen.getByTestId('consent-checkbox'));
    await waitFor(() => expect(screen.getByTestId('second-button')).not.toBeDisabled());
  });

  it('sends expected payload including metadata.status and metadata.statusMap when confirming removal (user flow)', async () => {
    const user = userEvent.setup();

    let capturedBody: any = null;
    server.use(
      http.post('/admin/settlement-reconciliations/processor-configs', async ({ request }) => {
        capturedBody = await request.json();
        return HttpResponse.json({ status: true }, { status: 200 });
      })
    );

    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    const checkboxes = screen.getAllByRole('checkbox').filter(cb => cb.getAttribute('data-testid') !== 'consent-checkbox');
    await user.click(checkboxes[1]);

    const removeBtn = screen.getByRole('button', { name: 'Remove Selected Columns' });
    await waitFor(() => expect(removeBtn).not.toBeDisabled());
    await user.click(removeBtn);

    await user.click(await screen.findByTestId('consent-checkbox'));
    await user.click(screen.getByTestId('second-button'));

    await waitFor(() => {
      expect(capturedBody).toBeTruthy();
    });

    expect(capturedBody).toEqual(
      expect.objectContaining({
        processor: 'korapay',
        payment_type: 'payin',
        primary_key_mappings: expect.any(Array),
        comparison_key_mappings: expect.any(Array),
        reference_key_mapping: expect.objectContaining({ processor_report: 'txn_id', internal_report: 'transaction_id' }),
        metadata: expect.objectContaining({
          status: expect.arrayContaining(['success']),
          statusMap: expect.objectContaining({ success: 'completed' })
        })
      })
    );
  });
});
