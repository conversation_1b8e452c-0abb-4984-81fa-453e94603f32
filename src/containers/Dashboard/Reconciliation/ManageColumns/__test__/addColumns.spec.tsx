import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  mockCreateConfigSuccessResponse,
  mockEmptyProcessorConfigResponse,
  mockProcessorConfigResponse,
  mockStartReconciliationData
} from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';
import useReconciliationStore from '+store/reconciliationStore';

import AddColumns from '../AddColumns';

const MockedAddColumns = () => {
  return (
    <MockIndex>
      <AddColumns />
    </MockIndex>
  );
};

const mockHistoryPush = vi.fn();
const mockHistoryGoBack = vi.fn();

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useHistory: () => ({
      push: mockHistoryPush,
      goBack: mockHistoryGoBack
    })
  };
});

describe('AddColumns', () => {
  beforeEach(() => {
    mockHistoryPush.mockClear();
    mockHistoryGoBack.mockClear();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: [],
      comparisonKeyMappings: [],
      referenceKeyMappings: [],
      statusKeyMappings: []
    });

    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');

        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(mockProcessorConfigResponse, { status: 200 });
        }
        return HttpResponse.json(mockEmptyProcessorConfigResponse, { status: 200 });
      }),
      http.post('/admin/settlement-reconciliations/processor-configs', () => {
        return HttpResponse.json(mockCreateConfigSuccessResponse, { status: 201 });
      })
    );
  });

  it('should render the component correctly', async () => {
    render(<MockedAddColumns />);

    await waitFor(() => {
      expect(screen.getByText('Add Reconciliation Columns')).toBeInTheDocument();
    });

    expect(screen.getByText('Add Reconciliation Columns')).toBeInTheDocument();
  });

  it('should render the component with correct title and description', async () => {
    render(<MockedAddColumns />);

    await waitFor(() => {
      expect(screen.getByText('Add Reconciliation Columns')).toBeInTheDocument();
      expect(
        screen.getByText('Add columns to selected processor and payment type. They will be available for selection during reconciliation')
      ).toBeInTheDocument();
    });
  });

  it('should display loading placeholder when data is loading', () => {
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', () => {
        return new Promise(() => {});
      })
    );

    render(<MockedAddColumns />);

    expect(screen.getByTestId('loading-placeholder')).toBeInTheDocument();
  });

  it('should render all mapping sections when data is loaded', async () => {
    render(<MockedAddColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
      expect(screen.getByText('Reference Mapping')).toBeInTheDocument();
      expect(screen.getByText('Comparison Mapping')).toBeInTheDocument();
      expect(screen.getByText('Status Mapping (optional)')).toBeInTheDocument();
    });

    expect(screen.getByText(/Primary Mapping connects columns containing the same data/)).toBeInTheDocument();
    expect(screen.getByText(/Reference Mapping uses a unique identifier common to both reports/)).toBeInTheDocument();
    expect(screen.getByText(/Comparison Mapping compares similar columns during the reconciliation process/)).toBeInTheDocument();
    expect(screen.getByText(/Status Mapping links different status terms/)).toBeInTheDocument();
  });

  it('should populate mappings from existing processor config data', async () => {
    render(<MockedAddColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      expect(state.primaryKeyMappings).toHaveLength(2);
      expect(state.comparisonKeyMappings).toHaveLength(2);
      expect(state.referenceKeyMappings).toHaveLength(1);
      expect(state.statusKeyMappings).toHaveLength(2);
    });
  });

  it('should initialize default data when no existing config is found', async () => {
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', () => {
        return HttpResponse.json(mockEmptyProcessorConfigResponse, { status: 200 });
      })
    );

    render(<MockedAddColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      expect(state.primaryKeyMappings).toHaveLength(1);
      expect(state.comparisonKeyMappings).toHaveLength(1);
      expect(state.referenceKeyMappings).toHaveLength(1);
      expect(state.statusKeyMappings).toHaveLength(1);
    });
  });

  it('should handle back button click', async () => {
    render(<MockedAddColumns />);

    await waitFor(() => {
      expect(screen.getByText('Back')).toBeInTheDocument();
    });

    const backButton = screen.getByRole('button', { name: /back/i });
    await userEvent.click(backButton);

    expect(mockHistoryGoBack).toHaveBeenCalledTimes(1);
  });

  it('should handle cancel button click', async () => {
    render(<MockedAddColumns />);

    await waitFor(() => {
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await userEvent.click(cancelButton);

    expect(mockHistoryGoBack).toHaveBeenCalledTimes(1);
  });

  it('should disable Add Columns button when validation fails', async () => {
    useReconciliationStore.setState({
      startReconciliationData: { ...mockStartReconciliationData, processor: '' },
      primaryKeyMappings: [],
      comparisonKeyMappings: [],
      referenceKeyMappings: [],
      statusKeyMappings: []
    });

    render(<MockedAddColumns />);

    await waitFor(() => {
      const addButton = screen.getByRole('button', { name: /add columns/i });
      expect(addButton).toBeDisabled();
    });
  });

  it('should enable Add Columns button when validation passes', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: [{ id: '1', processor_report: 'txn_id', internal_report: 'transaction_id', color: '#FF5733' }],
      comparisonKeyMappings: [{ id: '2', processor_report: 'status', internal_report: 'txn_status' }],
      referenceKeyMappings: [{ id: '3', processor_report: 'txn_id', internal_report: 'transaction_id' }],
      statusKeyMappings: [{ id: '4', processor_report: 'completed', internal_report: 'success', color: '#33FF57' }]
    });

    render(<MockedAddColumns />);

    await waitFor(() => {
      const addButton = screen.getByRole('button', { name: /add columns/i });
      expect(addButton).not.toBeDisabled();
    });
  });

  it('should successfully add columns and show success modal', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: [{ id: '1', processor_report: 'txn_id', internal_report: 'transaction_id', color: '#FF5733' }],
      comparisonKeyMappings: [{ id: '2', processor_report: 'status', internal_report: 'txn_status' }],
      referenceKeyMappings: [{ id: '3', processor_report: 'txn_id', internal_report: 'transaction_id' }],
      statusKeyMappings: [{ id: '4', processor_report: 'completed', internal_report: 'success', color: '#33FF57' }]
    });

    render(<MockedAddColumns />);

    await waitFor(() => {
      const addButton = screen.getByRole('button', { name: /add columns/i });
      expect(addButton).not.toBeDisabled();
    });

    const addButton = screen.getByRole('button', { name: /add columns/i });
    await userEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText(/New columns have been added to Korapay processor/)).toBeInTheDocument();
      expect(screen.getByText('Dismiss')).toBeInTheDocument();
    });
  });

  it('should show loading state when adding columns', async () => {
    server.use(
      http.post('/admin/settlement-reconciliations/processor-configs', () => {
        return new Promise(resolve => {
          setTimeout(() => resolve(HttpResponse.json(mockCreateConfigSuccessResponse, { status: 201 })), 1000);
        });
      })
    );

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: [{ id: '1', processor_report: 'txn_id', internal_report: 'transaction_id', color: '#FF5733' }],
      comparisonKeyMappings: [{ id: '2', processor_report: 'status', internal_report: 'txn_status' }],
      referenceKeyMappings: [{ id: '3', processor_report: 'txn_id', internal_report: 'transaction_id' }],
      statusKeyMappings: [{ id: '4', processor_report: 'completed', internal_report: 'success', color: '#33FF57' }]
    });

    render(<MockedAddColumns />);

    const addButton = screen.getByRole('button', { name: /add columns/i });
    await userEvent.click(addButton);

    expect(screen.getByRole('button', { name: /adding.../i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /adding.../i })).toBeDisabled();
  });

  it('should handle API error when adding columns', async () => {
    const mockErrorResponse = {
      response: {
        data: {
          message: 'Failed to create processor configuration'
        }
      }
    };

    server.use(
      http.post('/admin/settlement-reconciliations/processor-configs', () => {
        return HttpResponse.json(mockErrorResponse, { status: 400 });
      })
    );

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: [{ id: '1', processor_report: 'txn_id', internal_report: 'transaction_id', color: '#FF5733' }],
      comparisonKeyMappings: [{ id: '2', processor_report: 'status', internal_report: 'txn_status' }],
      referenceKeyMappings: [{ id: '3', processor_report: 'txn_id', internal_report: 'transaction_id' }],
      statusKeyMappings: [{ id: '4', processor_report: 'completed', internal_report: 'success', color: '#33FF57' }]
    });

    render(<MockedAddColumns />);

    const addButton = screen.getByRole('button', { name: /add columns/i });
    await userEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /add columns/i })).not.toBeDisabled();
    });
  });

  it('should close success modal and navigate to reconciliation page', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: [{ id: '1', processor_report: 'txn_id', internal_report: 'transaction_id', color: '#FF5733' }],
      comparisonKeyMappings: [{ id: '2', processor_report: 'status', internal_report: 'txn_status' }],
      referenceKeyMappings: [{ id: '3', processor_report: 'txn_id', internal_report: 'transaction_id' }],
      statusKeyMappings: [{ id: '4', processor_report: 'completed', internal_report: 'success', color: '#33FF57' }]
    });

    render(<MockedAddColumns />);

    const addButton = screen.getByRole('button', { name: /add columns/i });
    await userEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Dismiss')).toBeInTheDocument();
    });

    const dismissButton = screen.getByText('Dismiss');
    await userEvent.click(dismissButton);

    expect(mockHistoryPush).toHaveBeenCalledWith('/dashboard/reconciliation');
  });

  describe('Metadata Handling', () => {
    it('should handle status mappings with metadata correctly', async () => {
      useReconciliationStore.setState({
        startReconciliationData: mockStartReconciliationData,
        primaryKeyMappings: [{ id: '1', processor_report: 'txn_id', internal_report: 'transaction_id', color: '#FF5733' }],
        comparisonKeyMappings: [{ id: '2', processor_report: 'status', internal_report: 'txn_status' }],
        referenceKeyMappings: [{ id: '3', processor_report: 'txn_id', internal_report: 'transaction_id' }],
        statusKeyMappings: [
          { id: '4', processor_report: 'completed', internal_report: 'success', color: '#33FF57' },
          { id: '5', processor_report: 'failed', internal_report: 'failed', color: '#FF3357' }
        ]
      });

      let capturedRequestData: any;
      server.use(
        http.post('/admin/settlement-reconciliations/processor-configs', async ({ request }) => {
          capturedRequestData = await request.json();
          return HttpResponse.json(mockCreateConfigSuccessResponse, { status: 201 });
        })
      );

      render(<MockedAddColumns />);

      const addButton = screen.getByRole('button', { name: /add columns/i });
      await userEvent.click(addButton);

      await waitFor(() => {
        expect(capturedRequestData).toBeDefined();
        expect(capturedRequestData.metadata.statusMap).toEqual({
          success: 'completed',
          failed: 'failed'
        });
        expect(capturedRequestData.metadata.status).toEqual(['success', 'failed']);
      });
    });

    it('should handle empty status mappings correctly', async () => {
      server.use(
        http.get('/admin/settlement-reconciliations/processor-configs', () => {
          return HttpResponse.json(mockEmptyProcessorConfigResponse, { status: 200 });
        })
      );

      let capturedRequestData: any;
      server.use(
        http.post('/admin/settlement-reconciliations/processor-configs', async ({ request }) => {
          capturedRequestData = await request.json();
          return HttpResponse.json(mockCreateConfigSuccessResponse, { status: 201 });
        })
      );

      render(<MockedAddColumns />);

      await waitFor(() => {
        expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
      });

      useReconciliationStore.setState({
        startReconciliationData: mockStartReconciliationData,
        primaryKeyMappings: [{ id: '1', processor_report: 'txn_id', internal_report: 'transaction_id', color: '#FF5733' }],
        comparisonKeyMappings: [{ id: '2', processor_report: 'status', internal_report: 'txn_status' }],
        referenceKeyMappings: [{ id: '3', processor_report: 'txn_id', internal_report: 'transaction_id' }],
        statusKeyMappings: []
      });

      await waitFor(() => {
        const addButton = screen.getByRole('button', { name: /add columns/i });
        expect(addButton).not.toBeDisabled();
      });

      const addButton = screen.getByRole('button', { name: /add columns/i });
      await userEvent.click(addButton);

      await waitFor(() => {
        expect(capturedRequestData).toBeDefined();
        expect(capturedRequestData.metadata.statusMap).toBeUndefined();
        expect(capturedRequestData.metadata.status).toBeUndefined();
      });
    });

    it('should handle multiple status mappings correctly', async () => {
      useReconciliationStore.setState({
        startReconciliationData: mockStartReconciliationData,
        primaryKeyMappings: [{ id: '1', processor_report: 'txn_id', internal_report: 'transaction_id', color: '#FF5733' }],
        comparisonKeyMappings: [{ id: '2', processor_report: 'status', internal_report: 'txn_status' }],
        referenceKeyMappings: [{ id: '3', processor_report: 'txn_id', internal_report: 'transaction_id' }],
        statusKeyMappings: [
          { id: '4', processor_report: 'completed', internal_report: 'success', color: '#33FF57' },
          { id: '5', processor_report: 'failed', internal_report: 'failed', color: '#FF3357' },
          { id: '6', processor_report: 'pending', internal_report: 'pending', color: '#FFA500' }
        ]
      });

      let capturedRequestData: any;
      server.use(
        http.post('/admin/settlement-reconciliations/processor-configs', async ({ request }) => {
          capturedRequestData = await request.json();
          return HttpResponse.json(mockCreateConfigSuccessResponse, { status: 201 });
        })
      );

      render(<MockedAddColumns />);

      await waitFor(() => {
        const addButton = screen.getByRole('button', { name: /add columns/i });
        expect(addButton).not.toBeDisabled();
      });

      const addButton = screen.getByRole('button', { name: /add columns/i });
      await userEvent.click(addButton);

      await waitFor(() => {
        expect(capturedRequestData).toBeDefined();
        expect(capturedRequestData.metadata.statusMap).toEqual({
          success: 'completed',
          failed: 'failed'
        });
        expect(capturedRequestData.metadata.status).toEqual(['success', 'failed']);
      });
    });
  });

  describe('Edge Cases', () => {
    it('has no major accessibility violations on load (with known label rules disabled)', async () => {
      const { container } = render(<MockedAddColumns />);
      await waitFor(() => {
        expect(screen.getByText('Add Reconciliation Columns')).toBeInTheDocument();
      });
      const results = await axe(container, {
        rules: {
          label: { enabled: false },
          'label-title-only': { enabled: false }
        }
      });
      expect(results).toHaveNoViolations();
    });
    it('hides delete icon for existing mappings but shows for newly added ones', async () => {
      render(<MockedAddColumns />);

      await waitFor(() => {
        expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
      });
      const initialTrashButtons = screen.queryAllByLabelText(/delete mapping/i);
      const addPrimaryBtn = screen.getAllByRole('button', { name: /add new column/i })[0];
      await userEvent.click(addPrimaryBtn);
      const afterAddTrashButtons = screen.queryAllByLabelText(/delete mapping/i);
      expect(afterAddTrashButtons.length).toBeGreaterThan(initialTrashButtons.length);
    });

    it('sanitizes payload removing existing/id/color fields before submission', async () => {
      let capturedRequestData: any;
      server.use(
        http.post('/admin/settlement-reconciliations/processor-configs', async ({ request }) => {
          capturedRequestData = await request.json();
          return HttpResponse.json(mockCreateConfigSuccessResponse, { status: 201 });
        })
      );

      render(<MockedAddColumns />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /add columns/i })).toBeInTheDocument();
      });

      const addButtons = screen.getAllByRole('button', { name: /add new column/i });
      const comparisonAddBtn = addButtons.find(btn => btn.textContent?.toLowerCase().includes('add new column')) as HTMLElement;
      await userEvent.click(comparisonAddBtn);

      const addColumnsBtn = screen.getByRole('button', { name: /add columns/i });
      await userEvent.click(addColumnsBtn);

      await waitFor(() => {
        expect(capturedRequestData).toBeDefined();
      });

      const validateMappingShape = (m: { internal_report: string; processor_report: string }) => {
        const keys = Object.keys(m).sort();
        expect(keys).toEqual(['internal_report', 'processor_report']);
      };
      capturedRequestData.primary_key_mappings.forEach(validateMappingShape);
      capturedRequestData.comparison_key_mappings.forEach(validateMappingShape);
      validateMappingShape(capturedRequestData.reference_key_mapping);
    });
    it('should handle processor and payment type changes', async () => {
      render(<MockedAddColumns />);

      await waitFor(() => {
        expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
      });

      await waitFor(() => {
        const state = useReconciliationStore.getState();
        expect(state.startReconciliationData.processor).toBe('korapay');
        expect(state.startReconciliationData.payment_type).toBe('payin');
      });
    });

    it('should handle API query with different processor and payment type', async () => {
      useReconciliationStore.setState({
        startReconciliationData: {
          ...mockStartReconciliationData,
          processor: 'flutterwave',
          payment_type: 'payout'
        },
        primaryKeyMappings: [],
        comparisonKeyMappings: [],
        referenceKeyMappings: [],
        statusKeyMappings: []
      });

      server.use(
        http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
          const url = new URL(request.url);
          const processor = url.searchParams.get('processor');
          const paymentType = url.searchParams.get('payment_type');

          expect(processor).toBe('flutterwave');
          expect(paymentType).toBe('payout');

          return HttpResponse.json(mockEmptyProcessorConfigResponse, { status: 200 });
        })
      );

      render(<MockedAddColumns />);

      await waitFor(() => {
        expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
      });
    });

    it('should handle network errors gracefully', async () => {
      server.use(
        http.get('/admin/settlement-reconciliations/processor-configs', () => {
          return HttpResponse.json({ message: 'Network error' }, { status: 500 });
        })
      );

      render(<MockedAddColumns />);

      await waitFor(() => {
        expect(screen.getByText('Add Reconciliation Columns')).toBeInTheDocument();
      });

      await waitFor(() => {
        const state = useReconciliationStore.getState();
        expect(state.primaryKeyMappings).toHaveLength(1);
        expect(state.comparisonKeyMappings).toHaveLength(1);
        expect(state.referenceKeyMappings).toHaveLength(1);
        expect(state.statusKeyMappings).toHaveLength(1);
      });
    });
  });
});
