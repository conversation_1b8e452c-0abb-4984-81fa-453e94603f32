@import '+styles/kpy-custom/_custom';
@import '+styles/kpy-custom/variables';

.manage-columns {
  font-family: 'Averta PE', sans-serif;
  padding: 0rem 3rem;

  &__back {
    margin-bottom: 1rem;
  }

  &__title {
    margin-bottom: 4rem;
    & > h4 {
      margin-bottom: 0.5rem;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    row-gap: 2rem;
    margin-block: 2rem 0;

    &--processor {
      & > div {
        background: #f9fbfd;
        border-radius: 8px;
        padding: 20px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        column-gap: 20px;
      }

      & > p {
        color: #414f5f;
        font-weight: 500;
        font-size: 13px;
        margin-bottom: 0.3rem;
      }
    }
  }
  &--card-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    p {
      color: #414f5f;
      font-weight: 500;
      font-size: 13px;
      margin-bottom: 0;
      margin-left: 0.5rem;
    }
    .label-tooltip {
      margin-top: -0.5rem;
    }
  }

  &__action {
    border-top: 1px solid #d5d8db;
    display: flex;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    gap: 1rem;
  }

  .border-dotted {
    border: 2px dashed #dde2ec;
    display: flex;
    align-items: center;
  }

  &--remove-consent {
    display: flex;
    flex-direction: column;

    .info-wrapper {
      display: flex;
      svg {
        margin-top: 0.8rem;
      }
      .disable {
        color: #f32345;
      }
    }

    p {
      font-weight: 400;
      font-style: italic;
      font-family: 'Averta PE ', sans-serif;
      font-size: 14px;
      max-width: 22rem;

      span {
        font-weight: 700;
      }
    }

    .prod-consent-wrapper {
      display: flex;
      align-items: center;
      column-gap: 0.5rem;

      span {
        color: #3e4b5b;
        font-weight: 500;
      }
    }
  }
}
