import { act, renderHook } from '@testing-library/react';
import createHookWrapper from '+mock/reactQueryHookWrapper';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import useReconciliationStore from '+store/reconciliationStore';

import useColumnMapping from '../useColumnMapping';

describe('useColumnMapping', () => {
  beforeEach(() => {
    useReconciliationStore.setState({
      startReconciliationData: {
        processor: 'korapay',
        payment_type: 'payin',
        report_start_date: '2024-01-01',
        report_end_date: '2024-01-31',
        processor_file_id: '123',
        processor_file_details: { key: 'k' },
        field_mapping: { processor: 'korapay', payment_type: 'payin', primary_key_mappings: [], comparison_key_mappings: [] }
      },
      primaryKeyMappings: [],
      comparisonKeyMappings: [],
      referenceKeyMappings: [],
      statusKeyMappings: [],
      bulkDeleteIds: {
        primaryKeyMappings: [],
        comparisonKeyMappings: [],
        referenceKeyMappings: [],
        statusKeyMappings: []
      }
    } as any);
  });

  it('returns hook functions and state from store', () => {
    const { result } = renderHook(() => useColumnMapping(), {
      wrapper: createHookWrapper()
    });

    expect(typeof result.current.handleMappings).toBe('function');
    expect(typeof result.current.handleAddNewColumn).toBe('function');
    expect(typeof result.current.handleOptionChange).toBe('function');
    expect(typeof result.current.handleDelete).toBe('function');
    expect(typeof result.current.handleBulkDelete).toBe('function');
    expect(typeof result.current.setDefaultData).toBe('function');
    expect(Array.isArray(result.current.primaryKeyMappings)).toBe(true);
    expect(Array.isArray(result.current.comparisonKeyMappings)).toBe(true);
    expect(Array.isArray(result.current.referenceKeyMappings)).toBe(true);
    expect(Array.isArray(result.current.statusKeyMappings)).toBe(true);
  });

  it('sets default data when called directly', () => {
    const { result } = renderHook(() => useColumnMapping(), {
      wrapper: createHookWrapper()
    });

    act(() => {
      result.current.setDefaultData();
    });

    const state = useReconciliationStore.getState();
    expect(state.primaryKeyMappings.length).toBe(1);
    expect(state.comparisonKeyMappings.length).toBe(1);
    expect(state.referenceKeyMappings.length).toBe(1);
    expect(state.statusKeyMappings.length).toBe(1);
    expect(state.primaryKeyMappings[0].processor_report).toBe('');
    expect(state.primaryKeyMappings[0].internal_report).toBe('');
    expect(state.primaryKeyMappings[0].id).toBeTruthy();
  });

  it('adds a new comparison mapping with empty fields and generated id', () => {
    const { result } = renderHook(() => useColumnMapping(), {
      wrapper: createHookWrapper()
    });

    const initialCount = result.current.comparisonKeyMappings.length;

    act(() => {
      result.current.handleAddNewColumn('comparisonKeyMappings');
    });

    expect(result.current.comparisonKeyMappings.length).toBe(initialCount + 1);

    const newMapping = result.current.comparisonKeyMappings[result.current.comparisonKeyMappings.length - 1];
    expect(newMapping).toEqual(
      expect.objectContaining({
        processor_report: '',
        internal_report: '',
        id: expect.any(String)
      })
    );
  });

  it('handles bulk delete ids', () => {
    const { result } = renderHook(() => useColumnMapping(), {
      wrapper: createHookWrapper()
    });

    const testIds = ['id1', 'id2'];

    act(() => {
      result.current.handleBulkDelete(testIds, 'primaryKeyMappings');
    });

    const state = useReconciliationStore.getState();
    expect(state.bulkDeleteIds.primaryKeyMappings).toEqual(testIds);
  });

  it('reverts a comparison mapping to previous valid selection when its primary is deleted', () => {
    vi.useFakeTimers();
    useReconciliationStore.setState({
      primaryKeyMappings: [
        { id: 'p1', processor_report: 'proc_a', internal_report: 'int_a', color: '#111' },
        { id: 'p2', processor_report: 'proc_b', internal_report: 'int_b', color: '#222' }
      ],
      comparisonKeyMappings: [{ id: 'c1', processor_report: 'proc_a', internal_report: 'int_a' }],
      referenceKeyMappings: [{ id: 'r1', processor_report: 'proc_a', internal_report: 'int_a' }],
      statusKeyMappings: []
    });

    const { result } = renderHook(() => useColumnMapping(), { wrapper: createHookWrapper() });

    act(() => {
      result.current.handleOptionChange('proc_b', 'processor_report', 'c1', 'comparisonKeyMappings');
      result.current.handleOptionChange('int_b', 'internal_report', 'c1', 'comparisonKeyMappings');
    });

    act(() => {
      result.current.handleDelete('p2', 'primaryKeyMappings');
      vi.advanceTimersByTime(300);
    });

    const state = useReconciliationStore.getState();
    const comp = state.comparisonKeyMappings.find(c => c.id === 'c1');
    expect(comp?.processor_report).toBe('proc_a');
    expect(comp?.internal_report).toBe('int_a');
    vi.useRealTimers();
  });

  it('deletes a newly added comparison mapping referencing deleted primary when multiple comparison rows exist', () => {
    vi.useFakeTimers();
    useReconciliationStore.setState({
      primaryKeyMappings: [
        { id: 'p1', processor_report: 'pa', internal_report: 'ia', color: '#111' },
        { id: 'p2', processor_report: 'pb', internal_report: 'ib', color: '#222' }
      ],
      comparisonKeyMappings: [
        { id: 'c1', processor_report: 'pa', internal_report: 'ia' },
        { id: 'c2', processor_report: 'pb', internal_report: 'ib' }
      ],
      referenceKeyMappings: [{ id: 'r1', processor_report: 'pa', internal_report: 'ia' }],
      statusKeyMappings: []
    });

    const { result } = renderHook(() => useColumnMapping(), { wrapper: createHookWrapper() });

    act(() => {
      result.current.handleDelete('p2', 'primaryKeyMappings');
      vi.advanceTimersByTime(300);
    });

    const state = useReconciliationStore.getState();
    const comp2 = state.comparisonKeyMappings.find(c => c.id === 'c2');
    expect(comp2).toBeUndefined();
    vi.useRealTimers();
  });

  it('clears (not deletes) sole comparison mapping when its primary is deleted and no history', () => {
    vi.useFakeTimers();
    useReconciliationStore.setState({
      primaryKeyMappings: [
        { id: 'p1', processor_report: 'px', internal_report: 'ix', color: '#111' },
        { id: 'p2', processor_report: 'py', internal_report: 'iy', color: '#222' }
      ],
      comparisonKeyMappings: [{ id: 'c1', processor_report: 'py', internal_report: 'iy' }],
      referenceKeyMappings: [{ id: 'r1', processor_report: 'px', internal_report: 'ix' }],
      statusKeyMappings: []
    } as any);

    const { result } = renderHook(() => useColumnMapping(), { wrapper: createHookWrapper() });

    act(() => {
      result.current.handleDelete('p2', 'primaryKeyMappings');
      vi.advanceTimersByTime(300);
    });

    const state = useReconciliationStore.getState();
    expect(state.comparisonKeyMappings.length).toBe(1);
    const comp = state.comparisonKeyMappings[0];
    expect(comp.processor_report === '' || comp.processor_report === 'px').toBe(true);
    vi.useRealTimers();
  });
});
