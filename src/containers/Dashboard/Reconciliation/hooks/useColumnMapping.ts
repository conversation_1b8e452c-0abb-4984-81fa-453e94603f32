import { useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { useShallow } from 'zustand/react/shallow';



import { useReducerState } from '+hooks';
import useReconciliationStore from '+store/reconciliationStore';
import { IProcessorReportMapping, KeyMappingType, MappingType, ProcessorConfigDataType, ProcessorFormMappingType } from '+types';



import { generateColor } from '../helpers/reconcileReportHelper';





const generateStableId = (mapping: IProcessorReportMapping, type: string, index: number) => {
  const baseContent = `${type}-${mapping.processor_report || 'empty'}-${mapping.internal_report || 'empty'}`;
  const stableContent = btoa(baseContent).replace(/[^a-zA-Z0-9]/g, '');
  return `${stableContent}-${index}`;
};

const validateBulkDeleteIds = (bulkIds: string[], currentMappings: { id?: string }[]): string[] => {
  const validIds = new Set(currentMappings.map(mapping => mapping.id).filter(Boolean));
  return bulkIds.filter(id => validIds.has(id));
};

const useColumnMapping = () => {
  const history = useHistory();
  const [removingItems, setRemovingItems] = useState<Set<string>>(new Set());

  const comparisonHistoryRef = useRef<Record<string, { processor_report?: string; internal_report?: string }[]>>({});
  const referenceHistoryRef = useRef<Record<string, { processor_report?: string; internal_report?: string }[]>>({});

  const setKeyMappings = useReconciliationStore(state => state.setKeyMappings);

  const deleteKeyMapping = useReconciliationStore(state => state.deleteKeyMapping);
  const updateKeyMappings = useReconciliationStore(state => state.updateKeyMappings);
  const clearKeyMappings = useReconciliationStore(state => state.clearKeyMappings);
  const setStartReconciliationData = useReconciliationStore(state => state.setStartReconciliationData);
  const setBulkDeleteIds = useReconciliationStore(state => state.setBulkDeleteIds);

  const {
    startReconciliationData,
    primaryKeyMappings,
    comparisonKeyMappings,
    referenceKeyMappings,
    statusKeyMappings,
    bulkDeleteIds,
    autoMatchColumns
  } = useReconciliationStore(
    useShallow(state => ({
      startReconciliationData: state.startReconciliationData,
      primaryKeyMappings: state.primaryKeyMappings,
      comparisonKeyMappings: state.comparisonKeyMappings,
      referenceKeyMappings: state.referenceKeyMappings,
      statusKeyMappings: state.statusKeyMappings,
      autoMatchColumns: state.autoMatchColumns,
      bulkDeleteIds: state.bulkDeleteIds
    }))
  );

  const [state, setState] = useReducerState<ProcessorFormMappingType & { displaySuccessModal: boolean }>({
    processor: startReconciliationData.processor,
    payment_type: startReconciliationData.payment_type,
    displaySuccessModal: false
  });

  const setDefaultData = () => {
    const defaultPrimary = { processor_report: '', internal_report: '', color: generateColor() };
    const defaultComparison = { processor_report: '', internal_report: '' };
    const defaultReference = { processor_report: '', internal_report: '' };
    const defaultStatus = { processor_report: '', internal_report: '', color: generateColor() };

    setKeyMappings([{ ...defaultPrimary, id: generateStableId(defaultPrimary, 'primary', 0) }], 'primaryKeyMappings');
    setKeyMappings([{ ...defaultComparison, id: generateStableId(defaultComparison, 'comparison', 0) }], 'comparisonKeyMappings');
    setKeyMappings([{ ...defaultReference, id: generateStableId(defaultReference, 'reference', 0) }], 'referenceKeyMappings');
    setKeyMappings([{ ...defaultStatus, id: generateStableId(defaultStatus, 'status', 0) }], 'statusKeyMappings');
  };

  const handleMappings = (data: { data: ProcessorConfigDataType[] }) => {
    clearKeyMappings('comparisonKeyMappings', false);
    clearKeyMappings('referenceKeyMappings', false);
    clearKeyMappings('primaryKeyMappings', false);
    clearKeyMappings('statusKeyMappings', false);

    const actualData = data?.data;
    if (actualData.length > 0) {
      const modifiedPrimaryKeyMappings = actualData[0].primary_key_mappings.map((mapping: IProcessorReportMapping, index: number) => ({
        ...mapping,
        color: generateColor(index),
        id: generateStableId(mapping, 'primary', index),
        existing: true
      }));
      setKeyMappings(modifiedPrimaryKeyMappings, 'primaryKeyMappings');
      setKeyMappings(
        actualData[0].comparison_key_mappings.map((mapping: IProcessorReportMapping, index: number) => ({
          ...mapping,
          id: generateStableId(mapping, 'comparison', index),
          existing: true
        })),
        'comparisonKeyMappings'
      );

      const modifiedReferenceKeyMappings = [
        {
          ...actualData[0].reference_key_mapping,
          color: generateColor(),
          id: generateStableId(actualData[0].reference_key_mapping, 'reference', 0),
          existing: true
        }
      ];

      setKeyMappings(modifiedReferenceKeyMappings, 'referenceKeyMappings');

      if (actualData[0].metadata?.statusMap && Object.keys(actualData[0].metadata.statusMap).length > 0) {
        const statusMap = actualData[0].metadata.statusMap;
        const modifiedStatusKeyMappings = Object.entries(statusMap).map(([internal_report, processor_report], index) => ({
          processor_report,
          internal_report,
          color: generateColor(index),
          id: generateStableId({ processor_report, internal_report }, 'status', index),
          existing: true
        }));
        setKeyMappings(modifiedStatusKeyMappings, 'statusKeyMappings');
      }

      setTimeout(() => {
        const validationData = [
          {
            type: 'primaryKeyMappings' as MappingType,
            currentIds: bulkDeleteIds.primaryKeyMappings,
            mappings: modifiedPrimaryKeyMappings
          },
          {
            type: 'comparisonKeyMappings' as MappingType,
            currentIds: bulkDeleteIds.comparisonKeyMappings,
            mappings: actualData[0].comparison_key_mappings.map((mapping, index) => ({
              id: generateStableId(mapping, 'comparison', index)
            }))
          },
          {
            type: 'referenceKeyMappings' as MappingType,
            currentIds: bulkDeleteIds.referenceKeyMappings,
            mappings: modifiedReferenceKeyMappings
          }
        ];

        if (actualData[0].metadata?.statusMap && Object.keys(actualData[0].metadata.statusMap).length > 0) {
          const statusMap = actualData[0].metadata.statusMap;
          const statusMappings = Object.entries(statusMap).map(([internal_report, processor_report], index) => ({
            id: generateStableId({ processor_report, internal_report }, 'status', index)
          }));

          validationData.push({
            type: 'statusKeyMappings' as MappingType,
            currentIds: bulkDeleteIds.statusKeyMappings,
            mappings: statusMappings
          });
        }

        validationData.forEach(({ type, currentIds, mappings }) => {
          const validIds = validateBulkDeleteIds(currentIds, mappings);
          if (validIds.length !== currentIds.length) {
            setBulkDeleteIds(validIds, type);
          }
        });
      }, 100);
    } else {
      setDefaultData();
    }
  };

  const handleAddNewColumn = (mappingType: MappingType) => {
    const newMapping = { processor_report: '', internal_report: '', color: generateColor() };

    let currentMappings;
    if (mappingType === 'primaryKeyMappings') {
      currentMappings = primaryKeyMappings;
    } else if (mappingType === 'comparisonKeyMappings') {
      currentMappings = comparisonKeyMappings;
    } else if (mappingType === 'referenceKeyMappings') {
      currentMappings = referenceKeyMappings;
    } else {
      currentMappings = statusKeyMappings;
    }
    const newIndex = currentMappings.length;
    setKeyMappings([{ ...newMapping, id: generateStableId(newMapping, mappingType.replace('KeyMappings', ''), newIndex) }], mappingType);
  };

  const handleOptionChange = (
    value: string | number | (string | number)[],
    field: keyof IProcessorReportMapping,
    id: string,
    type: MappingType
  ) => {
    if (type === 'comparisonKeyMappings' || type === 'referenceKeyMappings') {
      const collection = type === 'comparisonKeyMappings' ? comparisonKeyMappings : referenceKeyMappings;
      const target = collection.find(m => m.id === id);
      if (target) {
        const refStore = type === 'comparisonKeyMappings' ? comparisonHistoryRef.current : referenceHistoryRef.current;
        if (!refStore[id]) refStore[id] = [];
        const last = refStore[id][refStore[id].length - 1];
        if (!last || last.processor_report !== target.processor_report || last.internal_report !== target.internal_report) {
          refStore[id].push({ processor_report: target.processor_report, internal_report: target.internal_report });
          if (refStore[id].length > 5) refStore[id].shift();
        }
      }
    }

    updateKeyMappings({ value: { [field]: value }, id }, type);

    if (!autoMatchColumns) return;

    const valueStr = String(value);

    let currentMappings = primaryKeyMappings;
    if (type === 'comparisonKeyMappings') currentMappings = comparisonKeyMappings;
    else if (type === 'referenceKeyMappings') currentMappings = referenceKeyMappings;

    const current = currentMappings.find(m => m.id === id);
    if (!current) return;

    const isProcessorChange = field === 'processor_report';
    const isInternalChange = field === 'internal_report';

    let counterpartUpdate: Partial<IProcessorReportMapping> | null = null;

    if (isProcessorChange) {
      const match = primaryKeyMappings.find(p => p.processor_report === valueStr);
      if (match?.internal_report && current.internal_report !== match.internal_report) {
        counterpartUpdate = { internal_report: match.internal_report };
      }
    } else if (isInternalChange) {
      const match = primaryKeyMappings.find(p => p.internal_report === valueStr);
      if (match?.processor_report && current.processor_report !== match.processor_report) {
        counterpartUpdate = { processor_report: match.processor_report };
      }
    }

    if (counterpartUpdate) {
      updateKeyMappings({ value: counterpartUpdate, id }, type);
    }
  };

  const handleDelete = (id: string, mappingType: MappingType) => {
    setRemovingItems(prev => new Set(prev).add(id));

    setTimeout(() => {
      if (mappingType === 'primaryKeyMappings') {
        const deleted = primaryKeyMappings.find(m => m.id === id);
        if (deleted) {
          const remainingPrimaries = primaryKeyMappings.filter(p => p.id !== id);
          const stillExistingProcessor = (val?: string) => remainingPrimaries.some(p => p.processor_report === val);
          const stillExistingInternal = (val?: string) => remainingPrimaries.some(p => p.internal_report === val);

          const revertIfNeeded = (
            mappings: KeyMappingType,
            historyRef: React.MutableRefObject<Record<string, { processor_report?: string; internal_report?: string }[]>>,
            type: MappingType
          ) => {
            mappings.forEach(m => {
              const matchesDeleted =
                (!!deleted.processor_report && m.processor_report === deleted.processor_report) ||
                (!!deleted.internal_report && m.internal_report === deleted.internal_report);

              if (!matchesDeleted) return;

              const historyStack = historyRef.current[m.id as string] || [];
              for (let i = historyStack.length - 1; i >= 0; i--) {
                const prev = historyStack[i];
                if (stillExistingProcessor(prev.processor_report) && stillExistingInternal(prev.internal_report)) {
                  updateKeyMappings(
                    { value: { processor_report: prev.processor_report, internal_report: prev.internal_report }, id: m.id as string },
                    type
                  );
                  return;
                }
              }
              updateKeyMappings({ value: { processor_report: '', internal_report: '' }, id: m.id as string }, type);
            });
          };

          // Custom logic for comparison mappings:
          // - If mapping references deleted primary, try revert via history
          // - If newly added (no existing flag) and not the only comparison mapping and cannot revert -> delete it
          // - If it is the only comparison mapping and cannot revert -> clear (reset empty) to keep one row
          comparisonKeyMappings.forEach(m => {
            const referencesDeleted =
              (!!deleted.processor_report && m.processor_report === deleted.processor_report) ||
              (!!deleted.internal_report && m.internal_report === deleted.internal_report);
            if (!referencesDeleted) return;

            const historyStack = comparisonHistoryRef.current[m.id as string] || [];
            let reverted = false;
            for (let i = historyStack.length - 1; i >= 0; i--) {
              const prev = historyStack[i];
              if (stillExistingProcessor(prev.processor_report) && stillExistingInternal(prev.internal_report)) {
                updateKeyMappings(
                  { value: { processor_report: prev.processor_report, internal_report: prev.internal_report }, id: m.id as string },
                  'comparisonKeyMappings'
                );
                reverted = true;
                break;
              }
            }

            if (!reverted) {
              const multipleRows = comparisonKeyMappings.length > 1;
              const isExisting = m.existing;
              if (!isExisting && multipleRows) {
                deleteKeyMapping(m.id as string, 'comparisonKeyMappings');
              } else {
                updateKeyMappings({ value: { processor_report: '', internal_report: '' }, id: m.id as string }, 'comparisonKeyMappings');
              }
            }
          });

          revertIfNeeded(referenceKeyMappings, referenceHistoryRef, 'referenceKeyMappings');
        }
      }

      deleteKeyMapping(id, mappingType);

      setRemovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }, 300);
  };
  const handleBulkDelete = (ids: string[], mappingType: MappingType) => {
    setBulkDeleteIds(ids, mappingType);
  };

  const handleCancel = () => {
    history.goBack();
    clearKeyMappings('all');
  };

  const handleCloseSuccessModal = () => {
    setState({ ...state, displaySuccessModal: false });
    clearKeyMappings('all');
    history.push('/dashboard/reconciliation');
  };

  return {
    handleMappings,
    setKeyMappings,
    handleAddNewColumn,
    handleOptionChange,
    handleDelete,
    removingItems,
    updateKeyMappings,
    primaryKeyMappings,
    comparisonKeyMappings,
    referenceKeyMappings,
    statusKeyMappings,
    clearKeyMappings,
    handleCancel,
    setStartReconciliationData,
    startReconciliationData,
    setDefaultData,
    handleBulkDelete,
    bulkDeleteIds,
    handleCloseSuccessModal,
    state,
    setState
  };
};

export default useColumnMapping;
