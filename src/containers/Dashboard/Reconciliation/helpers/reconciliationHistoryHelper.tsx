import React from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import { ReconciliationHistoryType } from '+types';
import { capitalizeRemovedash, downloadFileViaUrl, getDate, getTime, switchStatus } from '+utils';

export const reconciliationHistory = {
  className: '--reconciliation-history-table',
  emptyStateHeading: 'No History yet',
  emptyStateMessage: 'Reconciliation History Not Available',
  fields: (each: ReconciliationHistoryType) => ({
    data: {
      status: (
        <>
          <span className={`status-pill smaller ${switchStatus(each.status)}`} />
          <span>{capitalizeRemovedash(`${each?.status}`)}</span>
        </>
      ),
      reconciled_report: <>{each?.title}</>,
      created_at: (
        <>
          <span>{getDate(each.createdAt)}</span>
          <span className="annotation" style={{ marginLeft: '5px' }}>
            {getTime(each.createdAt)}
          </span>
        </>
      ),
      action: each?.result_details?.presignedUrl ? (
        <span
          role="button"
          tabIndex={0}
          className={'action'}
          data-testid="action"
          onKeyDown={() => handleDownload(each.result_details?.presignedUrl ?? '', each.title ?? '')}
          onClick={() => handleDownload(each.result_details?.presignedUrl ?? '', each.title ?? '')}
          style={{ cursor: 'pointer' }}
        >
          <Icon name="downloadInside" />
          Download
        </span>
      ) : null
    }
  })
};

const handleDownload = (path: string, name: string) => {
  const fileName = name.toLowerCase().endsWith('.xlsx') ? name : `${name}.xlsx`;

  downloadFileViaUrl(path, fileName, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
};
