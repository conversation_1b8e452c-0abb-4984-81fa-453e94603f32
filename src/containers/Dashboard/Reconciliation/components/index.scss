@import '+styles/kpy-custom/_custom';
@import '+styles/kpy-custom/variables';

.report-profile-card {
  display: flex;
  column-gap: 1rem;
  align-items: center;

  &__image {
    background-color: #94a7b7;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;

    & > p {
      margin: 0;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    row-gap: 0.2rem;
    height: 50px;

    &--title {
      margin: 0;
      font-size: 0.9rem;
      font-weight: 600;
    }

    span {
      display: flex;
      align-items: center;
      column-gap: 0.5rem;

      p {
        margin: 0;
        font-size: 0.7rem;
        color: #94a7b7;
        font-weight: 400;
      }

      span {
        height: 5px;
        width: 5px;
        border-radius: 50%;
        background-color: #94a7b7;
      }

      button {
        color: #2376f3;
        font-weight: 400;
        font-size: 0.7rem;
        padding: 0;
      }
    }
  }
}

.reconciliation-option-row {
  .option-group {
    display: grid;
    grid-template-columns: 1fr 0.2fr 1fr 0.05fr;
    column-gap: 0.5rem;
    row-gap: 1rem;
    align-items: baseline;

    &.--delete {
      grid-template-columns: 0.05fr 1fr 0.2fr 1fr;
    }

    span {
      font-weight: 500;
      font-size: 0.9rem;
      color: #94a7b7;
    }
  }

  &__left,
  &__right {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
  }
}
