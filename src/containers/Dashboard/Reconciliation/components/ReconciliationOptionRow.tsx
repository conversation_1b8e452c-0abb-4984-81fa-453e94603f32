import React, { useState } from 'react';
import { useDebounce } from 'react-use';

import Icon from '+shared/Icons';
import ReactSelectDropdown from '+shared/ReactSelectDropdown';
import { ReconciliationOptionRowType } from '+types';

const ReconciliationOptionRow = ({
  options,
  value,
  onChange,
  onDelete,
  fieldType,
  selected,
  onSelect,
  mode,
  disabled
}: ReconciliationOptionRowType & { selected?: boolean; onSelect?: () => void; mode?: 'add' | 'remove'; disabled?: boolean }) => {
  const [state, setState] = useState({ processor_report: value.processor_report, internal_report: value.internal_report });

  useDebounce(
    () => {
      onChange?.(state.processor_report, 'processor_report');
    },
    500,
    [state.processor_report]
  );

  useDebounce(
    () => {
      onChange?.(state.internal_report, 'internal_report');
    },
    500,
    [state.internal_report]
  );
  return (
    <div className="reconciliation-option-row">
      <div className={'option-group' + (onSelect && mode === 'remove' ? ' --delete' : '')}>
        {onSelect && mode === 'remove' && (
          <input type="checkbox" checked={selected} disabled={disabled} onChange={() => !disabled && onSelect?.()} />
        )}
        <div className="option-group__left">
          {fieldType === 'text' ? (
            <div className="form-group">
              <input
                className="form-control"
                id="processor_report"
                type="text"
                name="processor_report"
                value={state.processor_report}
                onChange={value => {
                  setState({ ...state, processor_report: value.target.value });
                }}
              />
            </div>
          ) : (
            <ReactSelectDropdown
              options={options[0]}
              placeholder={`Select option`}
              label=""
              value={value.processor_report}
              onChange={value => onChange(value, 'processor_report')}
            />
          )}
        </div>
        <span>- Match with -</span>
        <div className="option-group__right">
          {fieldType === 'text' ? (
            <div className="form-group">
              <input
                className="form-control"
                id="internal_report"
                type="text"
                name="internal_report"
                value={state.internal_report}
                onChange={value => {
                  setState({ ...state, internal_report: value.target.value });
                }}
              />
            </div>
          ) : (
            <ReactSelectDropdown
              options={options[1]}
              placeholder={`Select option`}
              label=""
              value={value.internal_report}
              onChange={value => onChange(value, 'internal_report')}
            />
          )}
        </div>
        {onDelete && mode !== 'remove' && (
          <div
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                onDelete();
              }
            }}
            tabIndex={0}
            onClick={onDelete}
            role="button"
            aria-label="Delete mapping"
            data-testid={(value as any).id ? `delete-${(value as any).id}` : undefined}
          >
            <Icon name="trashIcon" />
          </div>
        )}
      </div>
    </div>
  );
};

export default ReconciliationOptionRow;
