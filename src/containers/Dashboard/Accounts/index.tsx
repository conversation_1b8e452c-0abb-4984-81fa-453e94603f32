import React from 'react';
import { Link, Route, Switch } from 'react-router-dom';

import { useSetUserAccess } from '+hooks';
import { isAllowed } from '+utils';

import AccessDenial from '../AccessDenial';
import PoolAccounts from './PoolAccounts';
import PoolAccountTransactionDetails from './PoolAccounts/PoolAccountTransactionDetails';
import VirtualBankAccounts from './VirtualAccounts';

import './index.scss';

export function AccountsComponent() {
  const userAccess = useSetUserAccess();
  const canViewVirtualAccounts = isAllowed(userAccess, ['virtual_accounts.view']);
  const canViewPoolAccounts = isAllowed(userAccess, ['pool_account_transactions.view']);

  if (!canViewVirtualAccounts && !canViewPoolAccounts) {
    return <AccessDenial />;
  }

  return (
    <div className="content-i">
      <div className="content-box">
        <main className="accounts">
          <div>
            <h1 className="form-header">Local Accounts for Merchants</h1>
            <p className="form-header__description">
              Equip merchants with the ability to expand globally by configuring virtual accounts. This system allows them to receive
              payments in various currencies from around the world, simplifying their international payment processes.
            </p>
          </div>

          <div className="accounts-options">
            {canViewVirtualAccounts && (
              <div className="accounts-options__item">
                <Link to="/dashboard/accounts/virtual-accounts" className="accounts-options__item-link">
                  Fixed Virtual Accounts
                </Link>
                <p>Manage virtual accounts by merchants and their customers</p>
              </div>
            )}

            {canViewPoolAccounts && (
              <div className="accounts-options__item">
                <Link to="/dashboard/accounts/pool-accounts" className="accounts-options__item-link">
                  Pool Accounts
                </Link>
                <p>Manage pool account references and maintain compliance effortlessly.</p>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

export default function Accounts() {
  return (
    <Switch>
      <Route exact path="/dashboard/accounts" component={AccountsComponent} />
      <Route exact path="/dashboard/accounts/pool-accounts" component={PoolAccounts} />
      <Route exact path="/dashboard/accounts/pool-accounts/:id" component={PoolAccounts} />
      <Route exact path="/dashboard/accounts/pool-accounts/transaction/:id" component={PoolAccountTransactionDetails} />
      <Route path="/dashboard/accounts/virtual-accounts" component={VirtualBankAccounts} />
    </Switch>
  );
}
