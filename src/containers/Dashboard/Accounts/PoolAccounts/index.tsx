import React from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

import Table from '+containers/Dashboard/Shared/Table';
import { useFeedbackHandler, useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import { PoolAccountReferenceType } from '+types';
import { history } from '+utils';

import PoolAccountsHeading from './components/PoolAccountsHeading';
import PoolAccountSummary from './components/PoolAccountSummary';
import { poolAccountsTableData } from './poolAccountsHelpers';

import './index.scss';

const api = new APIRequest();

export default function PoolAccounts() {
  const { feedbackInit } = useFeedbackHandler();
  const searchQuery = useSearchQuery();
  const page = Number(searchQuery.value.page) || 1;
  const limit = Number(searchQuery.value.limit) || 10;
  const activeCurrency = searchQuery.value.currency ?? 'GHS';
  const startDate = searchQuery.value.dateFrom || '';
  const { id: poolReference } = useParams<{ id: string }>();
  const status = searchQuery.value.status || '';
  const keyword = searchQuery.value.keyword || '';

  const { data: references, isFetching: fetchingReferences } = useQuery(
    ['POOL_ACCOUNTS', page, limit, startDate, keyword, activeCurrency, poolReference],
    () =>
      api.getPoolAccounts({
        page,
        limit,
        ...(startDate && { startDate }),
        ...(keyword && { keyword }),
        ...(poolReference ? { keyword: poolReference } : { currency: activeCurrency })
      }),
    {
      onError: () => {
        feedbackInit({
          message: "There has been an error getting this account's references",
          type: 'danger'
        });
      }
    }
  );

  const selectedAccount = references?.data?.find((account: PoolAccountReferenceType) => account.reference === poolReference);

  const { data: transactions, isFetching: fetchingTransactions } = useQuery(
    [`POOL_TRANSACTIONS_${poolReference}`, searchQuery.value],
    () =>
      api.getPoolAccountTransactions({
        ...(page && { page }),
        ...(limit && { limit }),
        ...(status && { status }),
        ...(startDate && { startDate }),
        ...(keyword && { keyword }),
        reference: poolReference
      }),
    {
      enabled: !!poolReference,
      refetchOnMount: 'always',
      onError: () => {
        feedbackInit({
          message: "There has been an error getting this pool account's transactions",
          type: 'danger'
        });
      }
    }
  );

  const data = poolReference ? transactions : references;

  const paging = data?.paging || {};

  const tableData = poolAccountsTableData({ type: poolReference ? 'pool-account-summary' : 'pool-accounts' });

  const tableDataKeys = Object.keys(tableData.fields?.().data ?? {});

  return (
    <div className="content-i pool-accounts">
      <div className="content-box">
        {!poolReference && (
          <button type="button" className="btn btn-link" onClick={() => history.push('/dashboard/accounts')}>
            <i className="os-icon os-icon-arrow-left7" />
            <span style={{ fontSize: '13px', fontWeight: '500' }}>Back to Accounts</span>
          </button>
        )}
        {poolReference ? (
          <PoolAccountSummary isFetching={fetchingReferences} account={selectedAccount} activeCurrency={activeCurrency} />
        ) : (
          <PoolAccountsHeading activeCurrency={activeCurrency} onChangeCurrency={currency => searchQuery.setQuery({ currency }, true)} />
        )}

        <div className="pool-accounts__table">
          <Table
            className={tableData.className || ''}
            type={tableData.type}
            filterName={tableData.filterName || ''}
            loading={fetchingReferences || fetchingTransactions}
            data={data?.data}
            renderFields
            hasPagination
            tableHeadings={tableDataKeys}
            annotation={tableData.annotations}
            current={page}
            limitAction={(c: number | string) => searchQuery.setQuery({ limit: String(c) })}
            rowKey={tableData.rowKey}
            rowURL={tableData.rowURL}
            pageSize={paging?.page_size || 0}
            totalItems={paging?.total_items || 0}
            actionFn={current => searchQuery.setQuery({ page: String(current) })}
            emptyStateHeading={tableData.emptyStateHeading || ''}
            emptyStateMessage={tableData.emptyStateMessage || ''}
            filterShowExport={false}
            filterKeywordPlaceholder="Search Anything..."
          >
            {tableData.fields as any}
          </Table>
        </div>
      </div>
    </div>
  );
}
