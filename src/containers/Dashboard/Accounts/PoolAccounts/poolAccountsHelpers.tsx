import React, { Dispatch } from 'react';
import Calendar from 'react-calendar';

import BasicSelect from '+containers/Dashboard/Shared/BasicSelect';
import Icon from '+containers/Dashboard/Shared/Icons';
import { PoolAccountReferenceType, PoolAccountTransactionType } from '+types';
import { capitalize, capitalizeFirst, capitalizeRemovedash, formatAmount, getDate, getTime, stripNonNumeric, switchStatus } from '+utils';

export const initialState = {
  modalType: '',
  amount: '',
  time: '',
  date: '',
  timeOfDay: '',
  sourceDetails: {} as Record<string, string>,
  details: ['', ''],
  bankReference: '',
  checked: false
};

export const poolAccountsCurrencies = ['GHS', 'KES', 'ZAR', 'XAF', 'XOF', 'EGP'];

export const setTimeOnDate = (date: Date, timeStr: string) => {
  let [time, timeOfDay] = timeStr.toLowerCase().split(' ');
  let [hr, min] = time.indexOf(':') + 1 ? time.split(':').map(Number) : [Number(time), 0];
  date.setHours(timeOfDay === 'pm' ? (hr % 12) + 12 : hr % 12, min, 0, 0);
  return date.toISOString();
};

export const poolAccountsTableData = ({ type }: { type: 'pool-accounts' | 'pool-account-summary' }) => {
  switch (type) {
    case 'pool-accounts':
      return {
        type: 'pool-accounts' as const,
        className: '--pool-accounts',
        rowKey: 'reference',
        rowURL: '/dashboard/accounts/pool-accounts',
        emptyStateHeading: 'No pool accounts yet',
        emptyStateMessage: 'There are no pool accounts yet.',
        annotations: 'Pool Account References',
        filterName: 'Pool Account References',
        fields: (iter?: PoolAccountReferenceType) => ({
          data: {
            'Reference ID': <span style={{ color: '#007bff', fontWeight: '500', textTransform: 'uppercase' }}>{iter?.reference}</span>,
            'Merchant Name': <span>{capitalize(iter?.account_name ?? '')}</span>,
            'Customer Name': <span>{capitalize(iter?.customer_name ?? '')}</span>,

            date_created: (
              <>
                <span>{getDate(iter?.created_at)}</span>
                <span className="annotation" style={{ marginLeft: '5px' }}>
                  {getTime(iter?.created_at)}
                </span>
              </>
            )
          }
        })
      };
    case 'pool-account-summary':
      return {
        type: 'pool-account-summary' as const,
        className: '--pool-account-summary',
        rowURL: '/dashboard/accounts/pool-accounts/transaction',
        rowKey: 'transaction_reference',
        filterName: 'Transactions',
        emptyStateHeading: 'No Transactions Yet',
        emptyStateMessage: 'Pool account transactions for merchants will show here  ',
        fields: (iter?: PoolAccountTransactionType) => ({
          data: {
            status: (
              <>
                <span className={`status-pill smaller ${switchStatus(iter?.status.toLowerCase() ?? '')}`} />
                <span>{capitalize(iter?.status || '')}</span>
              </>
            ),
            'Transaction ID': (
              <span style={{ color: '#007bff', fontWeight: '500', textTransform: 'uppercase' }}>{iter?.transaction_reference}</span>
            ),

            transaction_date: (
              <>
                <span>{getDate(iter?.transaction_date)}</span>
                <span className="annotation" style={{ marginLeft: '5px' }}>
                  {getTime(iter?.transaction_date)}
                </span>
              </>
            ),
            amount: (
              <span>
                <span style={{ fontWeight: '600' }}>{formatAmount(iter?.amount_paid ?? '')} </span>
                {iter?.currency || 'N/A'}
              </span>
            )
          }
        })
      };

    default:
      return {};
  }
};

export const poolAccountModalData = ({
  state = initialState,
  setState = () => null,
  addTransactionFn = () => Promise.resolve()
}: {
  state?: typeof initialState;
  setState?: Dispatch<Partial<typeof initialState>>;
  addTransactionFn?: () => Promise<void>;
}) => {
  const timeIsInvalid = Number(state.time.slice(0, 2)) > 12;
  const handleTimeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let raw = Number(e.target.value.replace(/[^0-9]/g, '')).toString();

    if (raw.length > 4) return;

    if (raw.length === 2) {
      if (Number(raw[1]) > 5) return;
    } else if (raw.length === 3) {
      if (Number(raw.slice(1)) > 59) return;
      raw = `${raw.slice(0, 1)}:${raw.slice(1)}`;
    } else if (raw.length === 4) {
      if (Number(raw.slice(2)) > 59) return;
      else if (Number(raw.slice(0, 2)) < 10) {
        raw = `0${raw.slice(0, 1)}:${raw.slice(2, 4)}`;
      } else if (Number(raw.slice(0, 2)) > 12) {
        raw = `0${raw.slice(0, 1)}:${raw.slice(1, 3)}`;
      } else raw = `${raw.slice(0, 2)}:${raw.slice(2)}`;
    }

    setState({ time: raw });
  };

  switch (state.modalType) {
    case 'add-transaction':
      return {
        heading: 'Add Transaction',
        secondButtonDisable: Object.entries(state).some(([key, value]) => (['checked', 'sourceDetails'].includes(key) ? false : !value)),
        description:
          'Record transactions under a specific reference ID so that each payment is tracked and reconciled to the correct merchant.',
        size: 'md' as const,
        secondButtonActionIsTerminal: false,
        secondButtonAction: () => setState({ modalType: 'review-transaction' }),
        content: (
          <>
            <div className="add-ref-modal">
              <div className="form-group">
                <label>What was the amount?</label>
                <input
                  data-testid="amount-input"
                  value={state.amount}
                  onChange={e => setState({ amount: stripNonNumeric(e.target.value) })}
                  type="text"
                  placeholder="0.00"
                />
              </div>

              <div className="form-group">
                <label>Transaction Timestamp</label>
                <div className="timestamp">
                  <div className="timestamp-value">
                    {state.date && getDate(state.date)} <span>{state.time && 'at'}</span> {state.time}
                    {state.time && state.time.length < 3 ? ':00' : ''} {state.timeOfDay.toUpperCase()}
                  </div>
                  <button
                    onClick={e => {
                      e.preventDefault();
                      setState({ ...(state.date ? { time: '', date: '', timeOfDay: '' } : { modalType: 'set-timestamp' }) });
                    }}
                    className="btn btn-link"
                  >
                    {state.date ? 'Clear' : 'Set Timestamp'}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label>Bank Reference</label>
                <input
                  maxLength={36}
                  data-testid="transaction-reference-input"
                  value={state.bankReference}
                  onChange={e => setState({ bankReference: e.target.value })}
                  type="text"
                  placeholder="Reference of this transaction"
                />
              </div>

              <div className="form-group">
                <label>Source Details</label>

                <div className="source-details">
                  <div className="source-details-input">
                    <input
                      maxLength={50}
                      data-testid="source-title-input"
                      value={state.details[0]}
                      onChange={e => setState({ details: [e.target.value, state.details[1]] })}
                      type="text"
                      className={state.details[1] && !state.details[0] ? 'error' : ''}
                      placeholder="Source Title"
                    />

                    <input
                      data-testid="source-content-input"
                      className={`${state.details[0] && !state.details[1] ? 'error' : ''} source-content-input`}
                      value={state.details[1]}
                      onChange={e => setState({ details: [state.details[0], e.target.value] })}
                      type="text"
                      placeholder="Source Content"
                      maxLength={150}
                    />
                  </div>
                  <button
                    disabled={!state.details[0] || !state.details[1]}
                    className="btn btn-link add-source-btn"
                    onClick={e => {
                      e.preventDefault();
                      setState({
                        sourceDetails: {
                          [state.details[0].trim().toLowerCase().split(' ').join('_')]: capitalizeFirst(state.details[1].trim()),
                          ...state.sourceDetails
                        },
                        details: ['', '']
                      });
                    }}
                  >
                    <i className="os-icon os-icon-plus" /> Add new source
                  </button>
                  <div className="source-details-items">
                    {Object.entries(state.sourceDetails).map(([title, content]) => (
                      <div className="source-details-item" key={title}>
                        <div className="source-info">
                          <span className="source-title">{capitalizeRemovedash(title)}</span>
                          <span className="source-content">{capitalizeFirst(content)}</span>
                        </div>
                        <button
                          onClick={e => {
                            e.preventDefault();
                            const newSourceDetails = { ...state.sourceDetails };
                            delete newSourceDetails[title];
                            setState({ sourceDetails: newSourceDetails });
                          }}
                          className="delete-source-details-btn"
                        >
                          <Icon name="trashIcon" width={16} fill="currentColor" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div className="confirmation-checkbox">
              <input checked={state.checked} onChange={e => setState({ checked: e.target.checked })} type="checkbox" />{' '}
              <p>
                Record this transaction as <strong>settled</strong>
              </p>
            </div>
          </>
        )
      };
    case 'set-timestamp':
      return {
        heading: 'Transaction Timestamp',
        secondButtonActionIsTerminal: false,
        description: 'Add subtitle that further explains information surrounding the modal title.',
        size: 'sm' as const,
        secondButtonDisable: !state.date || !state.time || !state.timeOfDay || timeIsInvalid,
        secondButtonText: 'Set Timestamp',
        firstButtonText: 'Cancel',
        secondButtonAction: () => setState({ modalType: 'add-transaction' }),
        content: (
          <div className="add-ref-modal">
            <div className="form-group">
              <label>Transaction Date</label>
              <div className="timestamp-field">{state.date ? getDate(state.date) : 'DD - MM - YYYY'}</div>
            </div>
            <div data-testid="pool-account-calendar">
              <Calendar
                className="secondary-datefilter--custom-calendar"
                tileClassName="secondary-datefilter--custom-calendar-tile"
                next2Label={null}
                prev2Label={null}
                onChange={value => {
                  if (value) {
                    setState({ date: String(value) });
                  }
                }}
                value={state.date}
                maxDate={new Date()}
              />
            </div>
            <div className="transaction-time-wrapper form-group">
              <label>Transaction Time</label>
              <div className="time">
                <input
                  data-testid="time-input"
                  value={state.time}
                  onChange={handleTimeInputChange}
                  type="text"
                  placeholder="00:00"
                  className={`time-input ${timeIsInvalid ? 'invalid-time' : ''}`}
                />
                <div className="am-pm">
                  <button
                    className={state.timeOfDay === 'am' ? 'selected' : ''}
                    onClick={e => {
                      e.preventDefault();
                      setState({ timeOfDay: 'am' });
                    }}
                  >
                    AM
                  </button>
                  <button
                    className={state.timeOfDay === 'pm' ? 'selected' : ''}
                    onClick={e => {
                      e.preventDefault();
                      setState({ timeOfDay: 'pm' });
                    }}
                  >
                    PM
                  </button>
                </div>
              </div>
            </div>
          </div>
        )
      };

    case 'review-transaction':
      return {
        heading: 'Review Transaction',
        description:
          'Please confirm whether the transaction is recorded under the correct  reference ID to ensure each payment is properly tracked and reconciled to the correct merchant.',
        size: 'md' as const,
        firstButtonText: 'Go Back',
        firstButtonAction: () => setState({ modalType: 'add-transaction' }),
        secondButtonText: 'Proceed',
        secondButtonActionIsTerminal: false,
        secondButtonAction: () => setState({ modalType: 'confirm-add-transaction' }),
        content: (
          <div className="review-transaction-modal">
            {state.checked && (
              <div className="review-banner">
                <Icon name="warningOrange" width={28} height={28} style={{ marginTop: -1 }} />
                <p>Please note that this action is irreversible. Once completed, transactions will be added to the merchants dashboard.</p>
              </div>
            )}

            <div className="review-transaction-details">
              <h6>Pool Account Transaction Summary</h6>
              <div>
                <div className="details-item">
                  <span className="label">Amount</span>
                  <span className="value">{formatAmount(state.amount)}</span>
                </div>
                <div className="details-item">
                  <span className="label">Timestamp</span>
                  <span className="value">
                    {getDate(state.date)}, {state.time} {state.timeOfDay.toUpperCase()}
                  </span>
                </div>

                <div className="details-item">
                  <span className="label">Bank Reference</span>
                  <span className="value">{state.bankReference}</span>
                </div>

                {Object.entries(state.sourceDetails).length > 0 && (
                  <div className="source-details">
                    <span className="label">Source Details:</span>
                    <div className="source-details-items">
                      {Object.entries(state.sourceDetails).map(([title, content]) => (
                        <div className="source-details-item" key={title}>
                          <div className="source-title">
                            <span className="source-title">{capitalizeRemovedash(title)}</span>
                          </div>
                          <span className="source-content">{capitalizeFirst(content)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )
      };

    case 'confirm-add-transaction':
      return {
        secondButtonActionIsTerminal: false,
        heading: `Are you sure you want to add${state.checked ? ' and settle' : ''} this transaction for this merchant?`,
        description: 'Kindly ensure you are adding the correct details of this transaction to enable easy reconciliation.',
        size: 'md' as const,
        secondButtonText: 'Yes, Add Transaction',
        secondButtonAction: addTransactionFn
      };

    case 'add-transaction-success':
      return {
        showButtons: false,
        size: 'sm' as const,

        content: (
          <div className="add-transaction-success-modal">
            <Icon name="circledSuccessWithLightGreenBg" width={80} height={80} />
            <h4>Done!</h4>
            <p className="text">You have successfully added a transaction for this merchant</p>
            <button onClick={() => setState(initialState)} type="button" className="dismiss-btn">
              Dismiss
            </button>
          </div>
        )
      };
    default:
      return {};
  }
};
