import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useParams } from 'react-router-dom';

import Modal from '+containers/Dashboard/Shared/Modal';
import TransactionDetails from '+containers/Dashboard/TransactionDetailsNew';
import { useFeedbackHandler, useSetUserAccess } from '+hooks';
import APIRequest from '+services/api-services';
import { IPayinDetails, PoolAccountTransactionDetailsType } from '+types';
import { formatAmount, history, isAllowed, switchTrxnMessage } from '+utils';

import {
  actionButtonsFn,
  generateHeaderSummary,
  generateMoreTransactionDetails,
  generateSourceDetails,
  settleTransactionModalContent
} from './poolAccountTransactionDetailsHelper';

import './index.scss';

const api = new APIRequest();

export default function PoolAccountTransactionDetails({
  data,
  isLoading: fetchingData = false
}: {
  data?: IPayinDetails['data'];
  isLoading?: boolean;
}) {
  const queryClient = useQueryClient();
  const { id } = useParams<{ id: string }>();
  const userAccess = useSetUserAccess() as { [key: string]: boolean };
  const [showDropdown, setShowDropdown] = useState(false);
  const [modal, setModal] = useState('');
  const [newStatus, setNewStatus] = useState('');

  const { feedbackInit } = useFeedbackHandler();

  const {
    data: poolAccountData,
    refetch,
    isLoading: fetchingPoolData
  } = useQuery<PoolAccountTransactionDetailsType>(
    [`POOL_ACCOUNT_TRANSACTION_DETAILS_${id}`],
    () => api.getSinglePoolAccountTransaction(id),
    {
      onError: () => {
        feedbackInit({
          message: `There has been an error fetching the details for the transaction: ${id.toUpperCase()}.`,
          type: 'danger',
          action: { name: 'retry', action: () => refetch() }
        });
        history.goBack();
      },
      enabled: !data && !!isAllowed(userAccess, ['pool_account_transactions.view'])
    }
  );

  const { mutateAsync: updateTransactionFn } = useMutation(
    () =>
      api.updatePoolAccountTransaction(id, {
        status: newStatus
      }),
    {
      onError: error => {
        feedbackInit({
          message: (error as { response: { data: { message: string } } }).response.data?.message,
          componentLevel: true,
          type: 'danger'
        });
      },
      onSuccess: () => {
        setModal('settle-update-success');

        queryClient.invalidateQueries([`POOL_ACCOUNT_TRANSACTION_DETAILS_${id}`]);
      }
    }
  );

  const isLoading = fetchingData || fetchingPoolData;
  const sourceDetails = (poolAccountData?.source_details ?? (data as IPayinDetails['data'])?.meta) as Record<string, string>;

  const status = (data ?? poolAccountData)?.status as keyof typeof switchTrxnMessage;
  const modalData = settleTransactionModalContent({ newStatus, modal, setModal, data: poolAccountData, updateTransactionFn });

  const summaries = generateHeaderSummary(data ?? poolAccountData);

  return (
    <div className="pool-account-transaction-details content-i">
      <div className="content-box">
        <TransactionDetails>
          <TransactionDetails.Header
            heading={formatAmount(data?.amount_charged ?? poolAccountData?.net_amount ?? '')}
            currency={(data ?? poolAccountData)?.currency}
            statusLabels={[
              {
                status: String(switchTrxnMessage[status]?.name),
                statusBg: String(switchTrxnMessage[status]?.backgroundColor),
                statusColor: String(switchTrxnMessage[status]?.color)
              }
            ]}
            isLoading={isLoading}
            summaries={summaries}
            actionButtons={actionButtonsFn({ setNewStatus, userAccess, data: poolAccountData, setModal, showDropdown, setShowDropdown })}
          />

          <TransactionDetails.Section
            isLoading={isLoading}
            heading="More Transaction Details"
            summaries={generateMoreTransactionDetails(data ?? poolAccountData)}
          />
          {!!Object.keys(sourceDetails || {}).length && (
            <TransactionDetails.Section isLoading={isLoading} heading="Source Details" summaries={generateSourceDetails(sourceDetails)} />
          )}
        </TransactionDetails>
      </div>
      {modal && <Modal close={() => setModal('')} {...modalData} />}
    </div>
  );
}
