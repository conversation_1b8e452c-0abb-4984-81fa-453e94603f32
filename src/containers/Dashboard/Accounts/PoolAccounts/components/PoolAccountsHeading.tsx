import React from 'react';

import CurrencyPicker from '+containers/Dashboard/Shared/CurrencyPicker';

import { poolAccountsCurrencies } from '../poolAccountsHelpers';

export default function PoolAccountsHeading({
  activeCurrency,
  onChangeCurrency
}: {
  activeCurrency: string;
  onChangeCurrency: (currency: string) => void;
}) {
  return (
    <div className="pool-accounts-intro">
      <div>
        <h1 className="pool-accounts-intro__heading">Pool Accounts</h1>
        <p className="pool-accounts-intro__heading-description">
          Kora enables businesses to accept bank transfers by collecting payments into pooled accounts. Unique Reference IDs assigned to
          merchants ensuring accurate payment identification and seamless reconciliation.
        </p>
      </div>
      <CurrencyPicker options={poolAccountsCurrencies} onChange={onChangeCurrency} activeCurrency={activeCurrency} />
    </div>
  );
}
