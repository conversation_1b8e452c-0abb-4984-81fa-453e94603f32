.pool-account-summary {
  color: #414f5f;

  .__wrapper {
    align-items: flex-start;
    display: flex;
    justify-content: space-between;
    margin: 1.5rem 0 0;

    .__icon-and-ref {
      display: flex;
      align-items: center;
      gap: 1rem;

      .__icon {
        width: 50px;
        height: 50px;
      }

      .__heading {
        display: flex;
        align-items: center;
        font-size: 1.5rem;
        font-weight: 600;
        line-height: 1.5rem;
        letter-spacing: -0.05rem;

        .copy-pool-reference {
          height: 1rem;
          width: 1rem;
        }
      }
    }

    .btn {
      background: #2376f3;
      border-radius: 8px;
      color: #fff;
      padding: 0.5rem 1.25rem;

      span {
        margin: 0;
      }

      &:disabled {
        background: #2376f39a;
        cursor: not-allowed;
      }
    }
  }

  .btn-link {
    margin: 0;
    padding: 0;
  }

  .__details {
    margin-top: 2rem;

    .subheading {
      color: #414f5f;
      font-size: 1rem;
      line-height: 1.625rem;
      letter-spacing: -0rem;
      max-width: 600px;
    }

    .trxn-layout-summary {
      .summary-item {
        display: grid;
        gap: 0.75rem;

        flex-grow: 0;
        min-width: 180px;

        .summary-value {
          color: #292b2c;
        }
      }
    }
  }

  &__table {
    margin-top: 2rem;
  }

  .add-ref-modal {
    color: #414f5f;
    max-height: 60vh;
    overflow-y: auto;
    margin: 0 0 0.75rem;

    .form-group {
      flex-grow: 1;

      label {
        display: block;
        font-size: 1rem;
        margin: 0 0 0.4rem;
      }

      input,
      textarea,
      .timestamp,
      .timestamp-field {
        border: 2px solid #dde2ec;
        font-size: 1rem;
        padding: 0.5rem 0.75rem;
        width: 100%;
      }

      .timestamp {
        display: flex;
        align-items: center;
        gap: 0.2rem;
        padding: 0.65rem;

        .timestamp-value {
          flex-grow: 1;

          > span {
            color: #94a7b7;
          }
        }

        .btn-link {
          font-weight: 500;
          justify-self: flex-end;
          margin: 0;
          padding: 0;
          text-decoration: none;
        }

        .btn-link:hover {
          text-decoration: none;
        }
      }

      input::placeholder,
      textarea::placeholder,
      .timestamp-field {
        color: #94a7b7;
      }

      .timestamp-field {
        cursor: not-allowed;
      }

      .source-details {
        &-input {
          display: flex;
          gap: 0.75rem;
          align-items: center;

          .error {
            border: 2px solid rgba(244, 23, 23, 0.776);
            border-radius: 4px;
          }
          .source-content-input {
            min-width: 68%;
          }
        }

        .add-source-btn {
          display: flex;
          gap: 0.25rem;
          align-items: center;
          margin: 0.5rem 0 0;
          font-size: 1rem;
          color: #047bf8;

          &:hover {
            text-decoration: none;
          }
        }

        &-items {
          margin: 1rem 0;
          padding: 1.5rem 0 0;
          border-top: 1px solid #dde2ec;

          .source-details-item {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all ease-in 0.2s;

            .source-info {
              display: flex;
              flex-direction: column;
              flex-grow: 1;
              background: #f1f6fa;
              border-radius: 5px;
              font-size: 1rem;
              padding: 0.5rem 1rem;

              .source-title {
                font-weight: 500;
                color: #94a7b7;
              }

              .source-content {
                color: #414f5f;
              }
            }

            .delete-source-details-btn {
              flex-shrink: 0;
              height: 30px;
              width: 30px;
              display: grid;
              place-items: center;
              border-radius: 100%;
              color: #414f5f;
              background: #f1f6fa;
            }
          }
        }
      }
    }

    .flex-group {
      display: flex;
      gap: 1rem;

      .form-group {
        display: flex;
        flex-direction: column;

        .timestamp {
          flex-grow: 1;
        }
      }

      .currency-dropdown {
        border-radius: 0;
        padding: 0.15rem 0.75rem;
      }
    }

    .transaction-time-wrapper {
      margin: 2rem 0 0;

      .time {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .time-input {
          border-radius: 4px;
        }

        .invalid-time {
          all: unset;
          border-radius: 4px;
          font-size: 1rem;
          padding: 0.5rem 0.75rem;
          width: 100%;
          border: 2px solid red;
        }

        .am-pm {
          background: #f1f6fa;
          display: flex;
          border-radius: 8px;
          padding: 0.5rem;

          button {
            color: inherit;
            padding: 0.25rem 0.5rem;
          }

          button.selected {
            box-shadow: 0px 3px 7px 0px #3e4b5b52;
            border-radius: 8px;
            color: #2376f3;
            font-weight: 500;
          }
        }
      }
    }
  }
  .confirmation-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    p {
      padding: 0rem;
      margin: 2px 0 0;
    }
  }
}

.review-transaction-modal {
  .review-banner {
    background: #fff7ed;
    border-radius: 8px;
    padding: 0.5rem;
    display: flex;
    gap: 0.5rem;
    align-items: flex-start;
    margin: 0;
  }

  .review-transaction-details {
    background: #f3f4f8;
    border-radius: 8px;
    margin-top: 1rem;
    padding: 0.5rem 0;
    max-height: 60vh;
    overflow-y: auto;

    h6 {
      border-bottom: 1px solid #dde2ec;
      padding: 0.75rem;
    }

    .details-item,
    .source-details {
      display: flex;
      font-weight: 500;
      justify-content: space-between;
      padding: 0.5rem 0.75rem;

      .label {
        color: #94a7b7;
      }

      .value {
        color: #292b2c;
      }
    }

    .source-details {
      border-top: 1px solid #dde2ec;
      flex-direction: column;
      gap: 0.5rem;
      margin: 0.75rem 0 0;
      padding-top: 1rem;
      color: #414f5f;

      .label {
        font-weight: 500;
        color: #414f5f;
      }

      .source-details-item {
        margin: 0.75rem 0;
        display: grid;
        gap: 0.5rem;

        .source-title {
          font-weight: 500;
          color: #94a7b7;
        }
      }
    }
  }
}

.add-transaction-success-modal {
  padding: 2rem 3rem;
  text-align: center;

  h4,
  .text {
    margin: 1rem 0;
  }

  .dismiss-btn {
    font-weight: 500;
  }
}
