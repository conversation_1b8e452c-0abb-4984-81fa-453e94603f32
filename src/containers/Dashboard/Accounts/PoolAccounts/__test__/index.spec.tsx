import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { type Mock } from 'vitest';

import { useSetUserAccess } from '+hooks';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import * as utils from '+utils';

import PoolAccounts from '../index';

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const routes = ['/', '/test-ref'];

function MockedPoolAccounts({ i = 0 }) {
  return (
    <MockIndexWithRoute route={i === 0 ? '/' : '/:id'} initialEntries={[routes[i]]}>
      <PoolAccounts />
    </MockIndexWithRoute>
  );
}

describe('Pool Accounts', () => {
  describe('Pool Accounts References Test', () => {
    test('Renders Pool Accounts', async () => {
      render(<MockedPoolAccounts />);
      expect(
        screen.getByText(
          '<PERSON><PERSON> enables businesses to accept bank transfers by collecting payments into pooled accounts. Unique Reference IDs assigned to merchants ensuring accurate payment identification and seamless reconciliation.'
        )
      ).toBeInTheDocument();
    });

    test('Renders pool accounts table contents', async () => {
      render(<MockedPoolAccounts />);
      expect(await screen.findByText('(2) Pool Account References')).toBeInTheDocument();

      expect(screen.getAllByText('Reference ID')).toHaveLength(1);
      expect(screen.getByText('test-ref')).toBeInTheDocument();
      expect(screen.getByText('KKK-OVI-BOB')).toBeInTheDocument();

      expect(screen.getAllByText('Merchant Name')).toHaveLength(1);
      expect(screen.getByText('Ovie5th')).toBeInTheDocument();
      expect(screen.getByText('Oscar')).toBeInTheDocument();

      expect(screen.getAllByText('Customer Name')).toHaveLength(1);
      expect(screen.getByText('Cheche')).toBeInTheDocument();
      expect(screen.getByText('Bob')).toBeInTheDocument();

      expect(screen.getAllByText('Date Created')).toHaveLength(1);
      expect(screen.getByText('26 May 2025')).toBeInTheDocument();
      expect(screen.getByText('25 May 2025')).toBeInTheDocument();
    });

    test('Navigates users to the pool account summary page, when a row on the table is clicked', async () => {
      render(<MockedPoolAccounts />);
      expect(await screen.findByText('(2) Pool Account References')).toBeInTheDocument();
    });
  });

  describe('Pool Accounts Summary Test', () => {
    test('Renders Pool Accounts Summary', async () => {
      render(<MockedPoolAccounts i={1} />);
      expect(await screen.findByText('Pool Account Reference Summary')).toBeInTheDocument();

      expect(screen.getByText('Merchant')).toBeInTheDocument();
      expect(screen.getByText('Ovie5th')).toBeInTheDocument();

      expect(screen.getByText('Customer')).toBeInTheDocument();
      expect(screen.getByText('Cheche')).toBeInTheDocument();

      expect(screen.getByText('Created At')).toBeInTheDocument();
      expect(screen.getByText(/26 May 2025/i)).toBeInTheDocument();
    });

    test('Renders pool accounts summary table contents', async () => {
      render(<MockedPoolAccounts i={1} />);
      expect(await screen.findByText('(2) Transactions')).toBeInTheDocument();

      expect(screen.getByText('Status')).toBeInTheDocument();
      expect(screen.getAllByText('Settled')).toHaveLength(2);
      expect(screen.getAllByText('Success')).toHaveLength(2);

      expect(screen.getByText('Transaction ID')).toBeInTheDocument();
      expect(screen.getByText('transaction-id-1')).toBeInTheDocument();
      expect(screen.getByText('transaction-id-2')).toBeInTheDocument();

      expect(screen.getByText('Transaction Date')).toBeInTheDocument();
      expect(screen.getByText('14 May 2025')).toBeInTheDocument();
      expect(screen.getByText('15 May 2025')).toBeInTheDocument();

      expect(screen.getByText('Amount')).toBeInTheDocument();
      expect(screen.getByText('200.00')).toBeInTheDocument();
      expect(screen.getByText('150.00')).toBeInTheDocument();
    });

    describe('Add Transaction Flow Test', () => {
      const mockUseSetUserAccess = useSetUserAccess as Mock;
      mockUseSetUserAccess.mockReturnValue({ 'pool_account_transactions.create': true });

      test('Expect Go Back button to be present', async () => {
        render(<MockedPoolAccounts i={1} />);
        expect(await screen.findByTestId('go-back-btn')).toBeInTheDocument();
      });

      test('Add transaction button is hidden if user does not have permission', async () => {
        mockUseSetUserAccess.mockReturnValue({ 'pool_account_transactions.create': false });

        render(<MockedPoolAccounts i={1} />);
        expect(await screen.findByText('Pool Account Reference Summary')).toBeInTheDocument();

        expect(screen.queryByTestId('add-transaction-btn')).not.toBeInTheDocument();
        mockUseSetUserAccess.mockReturnValue({ 'pool_account_transactions.create': true });
      });

      test('Add transaction button is rendered if user have permission', async () => {
        render(<MockedPoolAccounts i={1} />);
        expect(await screen.findByText('Pool Account Reference Summary')).toBeInTheDocument();

        expect(screen.getByTestId('add-transaction-btn')).toBeInTheDocument();
      });

      test('Open the add transaction form modal when user clicks on the add transaction button', async () => {
        render(<MockedPoolAccounts i={1} />);
        expect(await screen.findByText('Pool Account Reference Summary')).toBeInTheDocument();

        await userEvent.click(screen.getByTestId('add-transaction-btn'));

        expect(
          await screen.findByText(
            'Record transactions under a specific reference ID so that each payment is tracked and reconciled to the correct merchant.'
          )
        ).toBeInTheDocument();
      });

      test('Fill and validate the add transaction form field', async () => {
        render(<MockedPoolAccounts i={1} />);

        await userEvent.click(await screen.findByTestId('add-transaction-btn'));

        expect(screen.getByTestId('second-button')).toBeDisabled();

        await userEvent.type(screen.getByTestId('amount-input'), 'invalid-value');
        expect(screen.getByTestId('amount-input')).toHaveValue('');

        expect(screen.getByTestId('second-button')).toBeDisabled();

        await userEvent.type(screen.getByTestId('amount-input'), '200');
        expect(screen.getByTestId('amount-input')).toHaveValue('200');

        expect(screen.getByTestId('second-button')).toBeDisabled();

        await userEvent.click(screen.getByText('Set Timestamp'));

        expect(screen.getByTestId('second-button')).toBeDisabled();

        expect(await screen.findByText('Add subtitle that further explains information surrounding the modal title.')).toBeInTheDocument();

        // Select the date that is not disabled on the date picker
        Array.from({ length: 30 }, async (_, i) => {
          const dayString = String(i + 1);
          const day = screen.queryAllByText(dayString)[0];

          if (day?.getAttribute('aria-label')?.indexOf(dayString) !== -1) {
            await userEvent.click(day);
          }
        });

        expect(screen.getByTestId('second-button')).toBeDisabled();

        // Set and validate the time field
        await userEvent.type(screen.getByTestId('time-input'), '1999');
        expect(screen.getByTestId('time-input')).toHaveValue('1');

        expect(screen.getByTestId('second-button')).toBeDisabled();
        await userEvent.clear(screen.getByTestId('time-input'));

        await userEvent.type(screen.getByTestId('time-input'), '200');
        expect(screen.getByTestId('time-input')).toHaveValue('2:00');

        expect(screen.getByTestId('second-button')).toBeDisabled();
        await userEvent.clear(screen.getByTestId('time-input'));

        await userEvent.type(screen.getByTestId('time-input'), '2008');
        expect(screen.getByTestId('time-input')).toHaveValue('02:00');

        expect(screen.getByTestId('second-button')).toBeDisabled();

        await userEvent.click(screen.getByText('PM'));

        expect(screen.getByTestId('second-button')).toBeEnabled();

        // leave the timestamp modal and return to the form modal
        await userEvent.click(screen.getByTestId('second-button'));
        expect(await screen.findByText('What was the amount?')).toBeInTheDocument();

        await userEvent.type(screen.getByTestId('transaction-reference-input'), 'a-test-ref');
        expect(screen.getByTestId('transaction-reference-input')).toHaveValue('a-test-ref');

        //Enable Continue button when all fields are valid
        expect(screen.getByTestId('second-button')).toBeEnabled();
        await userEvent.click(screen.getByTestId('second-button'));

        // Review modal should be displayed
        expect(await screen.findByText('Review Transaction')).toBeInTheDocument();

        // Don't render the warning icon if the transaction to add is not automatically settled
        expect(
          screen.queryByText(
            'Please note that this action is irreversible. Once completed, transactions will be added to the merchants dashboard.'
          )
        ).not.toBeInTheDocument();

        expect(screen.getByTestId('second-button')).toBeEnabled();
        await userEvent.click(screen.getByTestId('second-button'));

        expect(
          await screen.findByText('Kindly ensure you are adding the correct details of this transaction to enable easy reconciliation.')
        ).toBeInTheDocument();

        await userEvent.click(screen.getByTestId('second-button'));

        expect(screen.getByText('Done!')).toBeInTheDocument();
      });
    });
  });
});
