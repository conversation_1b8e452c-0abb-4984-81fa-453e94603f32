import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import VirtualAccounts from '../index';

function MockedVirtualAccounts() {
  return (
    <MockIndexWithRoute route="/dashboard/accounts/virtual-accounts" initialEntries={['/dashboard/accounts/virtual-accounts', '/']}>
      <VirtualAccounts />
    </MockIndexWithRoute>
  );
}

describe('Virtual Accounts', () => {
  test('Renders Virtual Accounts', async () => {
    render(<MockedVirtualAccounts />);
    expect(
      screen.getByText(
        'Kora allows merchants to create and manage virtual bank accounts. Here’s an overview of the virtual account service for different merchants on Kora.'
      )
    ).toBeInTheDocument();
  });

  test('renders the Virtual Accounts component', async () => {
    render(<MockedVirtualAccounts />);

    expect(screen.getByRole('tab', { name: 'Overview' })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: 'Overview' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'NGN' })).toBeInTheDocument();
  });

  //this test is timing out
  test.skip('changes the active tab when a tab button is clicked', async () => {
    render(<MockedVirtualAccounts />);
    const virtualBankCardsButton = screen.getByRole('button', { name: 'Fixed Virtual Accounts' });

    expect(virtualBankCardsButton).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: 'Overview' })).toBeInTheDocument();

    await userEvent.click(virtualBankCardsButton);
    expect(
      await screen.findByText('These are the fixed virtual accounts that merchants have issued to their customers.')
    ).toBeInTheDocument();

    await userEvent.click(screen.getAllByText('Account Holders')[0]);
    expect(
      await screen.findByText('All account holders (customers that were issued fixed virtual accounts) appear here.')
    ).toBeInTheDocument();
    const texts = screen.getAllByText('Transactions');
    await userEvent.click(texts[0]);
    expect(await screen.findByText('These are all the transactions performed on the fixed virtual accounts.')).toBeInTheDocument();
  });

  test('switches to "Fixed Virtual Accounts" tab and displays correct content', async () => {
    render(<MockedVirtualAccounts />);
    const virtualBankCardsButton = screen.getByRole('button', { name: 'Fixed Virtual Accounts' });

    expect(screen.getByRole('heading', { name: 'Overview' })).toBeInTheDocument();
    expect(virtualBankCardsButton).toBeInTheDocument();
    await userEvent.click(virtualBankCardsButton);
    await screen.findByRole('heading', { name: 'Fixed Virtual Accounts' });
  });

  test('Virtual account page should render with the stats', async () => {
    render(<MockedVirtualAccounts />);

    expect(await screen.findByText('Fixed Virtual Accounts (NGN)')).toBeInTheDocument();
    expect(await screen.findByText('152')).toBeInTheDocument();
    expect(screen.getByText('Fixed Virtual Account Holders')).toBeInTheDocument();
    expect(screen.getByText('68')).toBeInTheDocument();
    expect(screen.getByText('Transaction Count (NGN)')).toBeInTheDocument();
    expect(screen.getByText('133')).toBeInTheDocument();
  });

  test('overview page should render the select currency option', async () => {
    render(<MockedVirtualAccounts />);
    expect(screen.getByTestId('currency-picker')).toBeInTheDocument();
  });

  test('see accounts button should route page to the accounts number page', async () => {
    render(<MockedVirtualAccounts />);
    await userEvent.click(await screen.findByText(/see accounts/i));
    expect(
      await screen.findByText('These are the fixed virtual accounts that merchants have issued to their customers.')
    ).toBeInTheDocument();
  });

  test('account numbers page should render the select currency option', async () => {
    render(<MockedVirtualAccounts />);
    await userEvent.click(await screen.findByText(/see accounts/i));
    expect(screen.getByTestId('currency-picker')).toBeInTheDocument();
  });

  test('see accounts holders button should route page to the accounts holders page', async () => {
    render(<MockedVirtualAccounts />);
    await userEvent.click(await screen.findByText(/see account holders/i));
    expect(
      await screen.findByText('All account holders (customers that were issued fixed virtual accounts) appear here.')
    ).toBeInTheDocument();
  });

  test('accounts holders page should not have select currency option', async () => {
    render(<MockedVirtualAccounts />);
    await userEvent.click(await screen.findByText(/see account holders/i));
    expect(screen.queryByTestId('currency-picker')).not.toBeInTheDocument();
  });

  test('see transactions button should route page to the transactions page', async () => {
    render(<MockedVirtualAccounts />);
    await userEvent.click(await screen.findByText(/see transactions/i));
    expect(await screen.findByText('These are all the transactions performed on the fixed virtual accounts.')).toBeInTheDocument();
  });

  test('transactions page should render the select currency option', async () => {
    render(<MockedVirtualAccounts />);
    await userEvent.click(await screen.findByText(/see transactions/i));
    expect(screen.getByTestId('currency-picker')).toBeInTheDocument();
  });

  test('Transactions table should render data properly', async () => {
    render(<MockedVirtualAccounts />);
    await userEvent.click(await screen.findByText(/see transactions/i));
    await screen.findByText(/KPY-CM-qb/i);
  });

  describe('account numbers for euro', () => {
    test('NGN shouuld not show upgrade requests', async () => {
      render(<MockedVirtualAccounts />);

      await userEvent.click(await screen.findByText(/see accounts/i));

      expect(screen.queryByTestId('upgrade_requests')).not.toBeInTheDocument();
      expect(screen.getAllByText(/active/i)).toHaveLength(7);
    });

    test('EUR shouuld show upgrade requests', async () => {
      render(<MockedVirtualAccounts />);

      const routeToAccountNumbers = await screen.findByText(/see accounts/i);
      await userEvent.click(routeToAccountNumbers);

      const eur = await screen.findByText(/eur/i);
      await userEvent.click(eur);

      await screen.findByText(/upgrade requests/i);
    });

    test('EUR shouuld show upgrade requests', async () => {
      render(<MockedVirtualAccounts />);

      const routeToAccountNumbers = await screen.findByText(/see accounts/i);
      await userEvent.click(routeToAccountNumbers);

      const eur = await screen.findByText(/eur/i);
      await userEvent.click(eur);

      await screen.findByText(/upgrade requests/i);
      await waitFor(() => expect(screen.getAllByText(/active/i)).toHaveLength(7));
    });

    test('upgrade request table should render 5 rows', async () => {
      render(<MockedVirtualAccounts />);

      const routeToAccountNumbers = await screen.findByText(/see accounts/i);
      await userEvent.click(routeToAccountNumbers);

      const eur = await screen.findByText(/eur/i);
      await userEvent.click(eur);

      const upgradeReq = await screen.findByText(/upgrade requests/i);
      await userEvent.click(upgradeReq);
      await waitFor(() => expect(screen.getAllByText(/pending/i)).toHaveLength(2));
    });

    test('pending upgrade request should open accept or decline modal', async () => {
      render(<MockedVirtualAccounts />);

      const routeToAccountNumbers = await screen.findByText(/see accounts/i);
      await userEvent.click(routeToAccountNumbers);

      const eur = await screen.findByText(/eur/i);
      await userEvent.click(eur);

      const upgradeReq = await screen.findByText(/upgrade requests/i);
      await userEvent.click(upgradeReq);

      const pendingRequest = await screen.findByTestId('table-row-0-column-0');
      await userEvent.click(pendingRequest);
    });
  });
});
