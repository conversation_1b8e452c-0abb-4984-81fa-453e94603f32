@import 'styles/kpy-custom/variables';

.virtual-accounts__container {
  .btn-link {
    padding: 0;
    margin: 0 0 1rem 0;
  }
  .copy-icon {
    img {
      width: 17px;
      height: 17px;
    }
  }
  .content-box {
    .info-summary-container {
      .first-section {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        row-gap: 3rem;
        margin-block: 3rem;
        padding-bottom: 3rem;
      }

      .info-summary-item {
        .label {
          color: #4e555b;
          margin-bottom: 5px;
          font-size: 14px;
          font-weight: 500;
        }
        .value {
          color: #4e555b;
          font-size: 1.75rem;
          font-weight: 500;
        }
        .description {
          color: #c2c3c6;
          font-size: 14px;
          margin-bottom: 0.8rem;
        }
      }

      .summary-link {
        color: $kpyblue;
        font-size: 14px;
        letter-spacing: -0.003em;
        font-weight: 400;
        background: none;
        border: none;
        padding: 0;

        span {
          display: inline-block;
          margin-left: 1.5px;
        }
      }
    }

    @media (max-width: $breakpoint-tablet) {
      padding: 0 !important;
      margin: 1rem;

      button {
        margin-left: 1rem;
      }
      @media (max-width: 600px) {
        button {
          margin-left: 0;
        }
      }
    }

    .os-tabs-virtual {
      @media (max-width: 400px) {
        flex-direction: column;
      }
    }
  }

  .vba-banner {
    background: #4725023d;
    border-radius: 10px;
    font-weight: 500;
    margin: 0 0 1rem;
    padding: 10px;
    text-align: center;

    &.suspended {
      background: #ffecad;
    }

    &.deleted {
      background: #ffd2da;
    }

    &.deactivated {
      background: #ffd2da;
    }

    &.rejected {
      background: #dde2ec;
    }

    button {
      all: unset;
      cursor: pointer;
      font-weight: 600;
      margin-left: 5px;

      &:hover {
        transform: translate(-1%, -1%);
      }
    }
  }

  .new-messages-count {
    position: absolute;
    left: 9.2rem;
    top: 14px;
    background-color: #f32345;
    height: 17px;
    padding: 1px 6px 2px;
    font-size: 0.7rem;
    color: white;
    border-radius: 17px;
  }
}

.virtual-account-summary {
  margin-top: 2.3rem;

  p {
    padding-left: 0;
  }

  ul {
    @media (max-width: $breakpoint-tablet) {
      margin: 0;
      line-height: 1.5rem;

      li {
        display: flex;
        flex-direction: row;

        :last-of-type {
          display: flex;
          width: 200px;
          text-align: right;
          justify-content: flex-end;
        }
      }

      .status-pill.status-pill-smaller {
        width: 8px;
      }
    }

    margin-left: 0;

    li {
      margin: 0;

      :first-of-type {
        margin: 0;
      }

      :last-of-type {
        margin: 0;
      }
    }
  }
}

.content-details-box {
  .content-details-head.--content-details {
    display: flex;
    flex-direction: row;
    border: 0;

    > .content-details-title {
      > div {
        &:first-child {
          > img {
            width: 3.2rem;
            padding-top: 0.3rem;
          }
        }
      }
    }

    .cd-tier {
      margin: 0.6rem;
      img {
        margin-right: 5px;
      }
    }
  }
}

.view-more-btn {
  all: unset;
  color: #2376f3;
  cursor: pointer;
  font-weight: 500;
  font-size: 1rem;
  margin: -2rem 0 2rem;
}

.memo-tooltip h2 {
  color: #fff;
}

.memo-tooltip p {
  font-size: 1rem;
}

.vba-acc-no-status {
  align-items: center;
  display: flex;
  gap: 0.5rem;

  > div {
    display: flex;
    gap: 0.5rem;

    .vba-acc-unverified {
      background: #f1f6fa;
      border-radius: 6px;
      margin-left: 0.5rem;
      padding: 1px 5px;
    }
  }
}

.vba-acc-unverified-status {
  background: #f1f6fa;
  border-radius: 6px;
  padding: 2px 5px;
  margin-left: 0.25rem;
}
