import React from 'react';
import { render, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import { server } from '+mock/mockServers';

import Users from '../index';

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));
function MockedUsers() {
  return (
    <MockIndexWithRoute route="/dashboard/users" initialEntries={['/dashboard/users', '/dashboard/users/roles']}>
      <Users />
    </MockIndexWithRoute>
  );
}

describe('Users', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({
    'admin_users.view': true,
    'admin_user_details.view': true,
    'admin_users.update': true,
    'admin_user_audit_logs.view': true
  });
  test('Users is accessible', async () => {
    const { container } = render(<MockedUsers />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('Renders Users with empty state', async () => {
    server.use(
      http.get('/admin/users', () => {
        return HttpResponse.json([], { status: 200 });
      })
    );
    const screen = render(<MockedUsers />);
    expect(screen.getByText('Dashboard Users')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByText('0 Users')).toBeInTheDocument());
  });

  test('Should display users on the table without invited user tab due to no permissions', async () => {
    const screen = render(<MockedUsers />);
    expect(screen.getByText('Dashboard Users')).toBeInTheDocument();

    expect(screen.queryByText('Invite New Users')).not.toBeInTheDocument();
    expect(screen.queryByText('Invited Users')).not.toBeInTheDocument();

    await waitFor(() => expect(screen.getByText('Status')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Name/Email')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Role')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Last Active')).toBeInTheDocument());

    await waitFor(() => expect(screen.getAllByText('Active')).toHaveLength(11));
    await waitFor(() => expect(screen.getByText('Custom-test Test')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('38 Users')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Lewis Dzeremo/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument());
  });

  test('Should display users on the table with invited user tab when permissions is present', async () => {
    mockUseSetUserAccess.mockReturnValue({
      'admin_users.view': true,
      'admin_user_details.view': true,
      'admin_users.update': true,
      'admin_user_audit_logs.view': true,
      'admin_user_invitations.view': true
    });
    const screen = render(<MockedUsers />);
    expect(screen.getByText('Dashboard Users')).toBeInTheDocument();

    await waitFor;
    await waitFor(() => expect(screen.getByText('Status')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Name/Email')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Role')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Last Active')).toBeInTheDocument());

    await waitFor(() => expect(screen.getAllByText('Active')).toHaveLength(11));
    await waitFor(() => expect(screen.getByText('Custom-test Test')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('38 Users')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Lewis Dzeremo/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument());
  });
});
