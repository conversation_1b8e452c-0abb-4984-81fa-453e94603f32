@import '+styles/kpy-custom/variables';

.invite-new-roles {
  .form-text-format {
    color: #3e4b5b;
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
  }
  .form-check-cont {
    padding: 0 0 0 1.22rem;

    .form-check-input {
      margin-top: 0.3em;
    }

    &.--explicit {
      margin-left: 3rem !important;
    }
  }

  .input-info {
    padding-top: 1.22rem;

    .rolename-length {
      text-align: right;
      font-size: 0.74rem;
      color: #3e4b5b;
      font-style: normal;
      font-weight: 400;
      margin-bottom: 0;
    }
  }
}

.feedback-modal-user-check {
  background-color: #f9fbfd !important;

  table thead th {
    border-bottom: none;
  }

  table tbody td {
    border-top: 2px solid #a9afbc;
  }

  .table-lightborder {
    .table-header {
      text-transform: capitalize;
      white-space: nowrap;
      font-weight: 400;
      font-size: 0.8rem;
      line-height: 23px;
      letter-spacing: -0.003em;
      color: #3e4b5b;
    }

    .table-data {
      color: rgba(62, 75, 91, 0.6);
      font-style: normal;
      font-weight: 600;
      font-size: 0.9rem;
      line-height: 1.08rem;
      text-transform: capitalize;
      position: relative;
      z-index: 9999;
    }
  }
}

.table-data-td {
  color: #354253;

  .table-data-check {
    display: flex;
    align-items: center;
    justify-content: center;

    .form-check-input {
      &:disabled {
        cursor: not-allowed;
      }
    }
  }
}

#permissions-info {
  width: 300px;
  padding: 0.5rem 1rem;
  color: #fff;
}

.form-check-input.explicit-input {
  accent-color: green;
}

.form-check-cont {
  padding: 0 0 0 1.22rem;

  .form-check-input {
    margin-top: 0.3em;
  }

  &.--explicit {
    label {
      color: #3e4b5b;
      font-weight: 500;
      font-size: 0.9rem;
    }
  }
}
.permission-modify-roles {
  .rolename-container {
    .explicit-label {
      background-color: #f1f6fa;
      height: 2.125rem;
      padding: 0.3rem 0.4rem;
      display: flex;
      align-items: center;
      max-width: 11.6rem;
      gap: 0.5rem;
      border-radius: 0.375rem;

      & > .text {
        font-weight: 600;
        font-size: 0.8rem;
        color: #94a7b7;
      }

      .count-wrapper {
        border-radius: 50%;
        background-color: #94a7b7;
        width: 1.25rem;
        height: 1.25rem;
        display: flex;
        justify-content: center;
        align-items: center;

        & > p {
          color: #fff !important;
          font-size: 0.8rem;
          font-weight: 600 !important;
        }
      }
    }
  }
}

.export-container {
  display: flex;
  margin-block: 0 !important;

  ul {
    display: flex;
    padding-inline: 0;
    list-style-type: none;

    li {
      display: flex;
      button {
        color: #2376f3;
        font-weight: 600;

        &:hover {
          color: #2376f3;
        }
      }
    }
  }
}
