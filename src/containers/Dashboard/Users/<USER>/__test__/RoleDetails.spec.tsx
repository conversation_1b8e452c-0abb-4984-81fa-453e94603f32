import React from 'react';
import { render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import RoleDetails from '../RoleDetails';

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

function MockedRoleDetails() {
  return (
    <MockIndexWithRoute route="/dashboard/users/roles/:id" initialEntries={['/dashboard/users/roles/121', '/']}>
      <RoleDetails />
    </MockIndexWithRoute>
  );
}

describe('RoleDetails', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;

  test('Should Not Render Delete Role button if user has no access', async () => {
    mockUseSetUserAccess.mockReturnValue({
      'custom_roles.update': true,
      'my_custom_roles.update': true,
      'system_roles.update': true
    });
    const screen = render(<MockedRoleDetails />);
    expect(screen.getByText('Back to Roles')).toBeInTheDocument();
    expect(screen.queryByText('Edit role name')).not.toBeInTheDocument();
    expect(screen.queryByText('Delete Role')).not.toBeInTheDocument();
  });

  test('Should Render Role Details and Delete Role button', async () => {
    mockUseSetUserAccess.mockReturnValue({
      'custom_roles.update': true
    });
    const screen = render(<MockedRoleDetails />);
    expect(screen.getByText('Back to Roles')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByText('Edit role name')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Delete Role')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Assignees')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Permissions')).toBeInTheDocument());
  });

  test('RoleDetails is accessible', async () => {
    const { container } = render(<MockedRoleDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Should show delete user role modal', async () => {
    const screen = render(<MockedRoleDetails />);

    await waitFor(() => expect(screen.getByText('Delete Role')).toBeInTheDocument());
    userEvent.click(screen.getByText('Delete Role'));

    waitFor(() => expect(screen.getByText('Delete Role')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Cancel')).toBeInTheDocument());
  });
});
