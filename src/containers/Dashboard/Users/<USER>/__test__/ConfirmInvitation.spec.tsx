import React, { ChangeEvent } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import ConfirmInvitation from '../ConfirmInvitation';

let saveState = false;
let roleName = '';

const user = userEvent;

const saveStateToggle = (e: ChangeEvent<HTMLInputElement>) => {
  saveState = Boolean(e.target.value);
};

const getRoleName = (value: string) => {
  roleName = value;
};

describe('Confirm Invititation Component', () => {
  it('check invite input is not rendered if checkInvite prop is false', () => {
    render(<ConfirmInvitation saveState={saveState} saveStateToggle={saveStateToggle} getRoleName={getRoleName} roleName={roleName} />);

    expect(screen.queryByTestId('save-state-input')).not.toBeInTheDocument();
    expect(screen.queryByTestId('role-name-input')).not.toBeInTheDocument();
  });

  it('save state checkbox works', async () => {
    render(
      <ConfirmInvitation
        checkInvite
        saveState={saveState}
        saveStateToggle={saveStateToggle}
        getRoleName={getRoleName}
        roleName={roleName}
      />
    );

    const saveStateInput = screen.getByTestId('save-state-input');

    await user.click(saveStateInput);

    expect(screen.getByText('Save modified permissions in a new role')).toBeInTheDocument();
    expect(saveState).toBeTruthy();
  });

  it('handle role name change works', () => {
    const { rerender } = render(
      <ConfirmInvitation checkInvite saveState saveStateToggle={saveStateToggle} getRoleName={getRoleName} roleName={roleName} />
    );

    const roleNameInput = screen.getByTestId('role-name-input');
    const roleNameLengthText = screen.getByTestId('role-name-length');

    user.type(roleNameInput, 'admin');

    rerender(<ConfirmInvitation checkInvite saveState saveStateToggle={saveStateToggle} getRoleName={getRoleName} roleName={roleName} />);

    expect(roleNameLengthText.innerHTML).toBe(`${roleName.length}/40`);
  });
});
