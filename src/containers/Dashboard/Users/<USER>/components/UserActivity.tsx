/* eslint-disable camelcase */
import React from 'react';
import { useQuery } from 'react-query';

import Table from '+dashboard/Shared/Table';
import { useFeedbackHandler, useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import { AuditEventType } from '+types';
import { getDate, getTime, logError } from '+utils';

import '../../index.scss';

const api = new APIRequest();
interface IUserActivityProps {
  userId?: string | number | null;
  userName?: string | null;
  email?: string;
}

const UserActivity = ({ userId, userName, email }: IUserActivityProps) => {
  const { feedbackInit } = useFeedbackHandler();

  const searchQuery = useSearchQuery();
  const page = searchQuery.value.page ?? '1';
  const limit = searchQuery.value.limit ?? '25';

  const {
    data: audits,
    isLoading,
    isFetching,
    refetch
  } = useQuery([`AUDIT_LOGS_${userId}`, page, email, limit, { email }], () => api.getLogs(page, limit, { email }), {
    keepPreviousData: true,
    onError: error => {
      if (error) {
        logError(error);
        feedbackInit({
          message: `There has been an error getting this ${userName ?? 'user'} activity log`,
          type: 'danger',
          action: {
            action: () => refetch(),
            name: 'Try again'
          }
        });
      }
    },
    enabled: !!userId && !!email
  });

  const paging = audits?.paging;
  const paginate = (currentPage: number | string) => {
    searchQuery.setQuery({ page: String(currentPage) });
  };
  const handlePageSize = (pageSize: number | string) => {
    searchQuery.setQuery({ limit: String(pageSize) });
  };

  const renderEvents = (event: AuditEventType) => {
    return {
      data: {
        created_at: (
          <div style={{ margin: 0, padding: 0 }}>
            {getDate(event?.created_at)}&nbsp;<span style={{ color: '#A9AFBC' }}>{getTime(event.created_at)}</span>
          </div>
        ),
        event: (
          <div style={{ margin: 0, padding: 0 }}>
            <span style={{ fontWeight: 500 }}>{event?.event}</span> : {event?.description}
          </div>
        ),
        event_id: <span>{event?.event_id}</span>
      }
    };
  };

  return (
    <div className="userdetails-container">
      <div className="tab-pane active" id="tab_activity">
        <Table
          className="--audit-log-table"
          hasPagination
          header={false}
          tableHeadings={['Timestamp', 'Event', 'Event ID']}
          totalItems={paging?.total_items || 0}
          pageSize={paging?.page_size || 0}
          loading={isLoading || isFetching}
          loaderBackground="#f5f6f6"
          current={paging?.current}
          actionFn={paginate}
          limitAction={handlePageSize}
          annotation="actions"
          tableWrapperClassName="table-responsive"
          emptyStateHeading="There are no logs yet"
          renderFields
          hasFilter={false}
          data={audits?.data || []}
        >
          {renderEvents}
        </Table>
      </div>
    </div>
  );
};

export default UserActivity;
