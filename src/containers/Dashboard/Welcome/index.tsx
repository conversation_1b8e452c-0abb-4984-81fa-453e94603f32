import React from 'react';
import { useShallow } from 'zustand/react/shallow';

import useStore from '+store';
import { capitalize } from '+utils';

import bloomLogin from '+assets/img/dashboard/bloom-login.svg';

import './index.scss';

const Welcome = () => {
  const { email } = useStore(useShallow(state => state.profile));

  return (
    <section className="container">
      <div className="content-wrapper">
        <img className="welcome-logo" alt="Bloom login" src={bloomLogin} />
        <p className="welcome-title" data-testid="welcome-title">
          Welcome to the internal dashboard, <span>{`${capitalize(email?.split('@')[0] ?? 'Admin')}!`}</span> 🌟
        </p>
        <p className="welcome-title--sub">The secure back office for managing transactions, merchants and all users.</p>
      </div>
    </section>
  );
};

export default Welcome;
