import React from 'react';
import { render,screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import { mockedVerificationsData } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';

import { VerificationEventTable } from '..';

const MockedVerificationEventTable = (props) => {
  const resolovedData = mockedVerificationsData;
  return (
    <MockIndex>
      <VerificationEventTable resolvedData={resolovedData} {...props} />
    </MockIndex>
  );
};

describe('Verification Event Table', () => {
  test('Renders Verification Event Table', async () => {
    render(<MockedVerificationEventTable />);
    expect(screen.getByText(/Verification Events/i)).toBeInTheDocument();
  });

  test('Verification Event Table is accessible', async () => {
    const { container } = render(<MockedVerificationEventTable />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Verification  Event Table shows correct data', async () => {
    const { getByText, getAllByText } = render(<MockedVerificationEventTable allMerchants />);
    await waitFor(() => expect(getByText('Reference')).toBeInTheDocument());
    await waitFor(() => expect(getByText('VR-WF3W54oLwD604Eqf7')).toBeInTheDocument());
    await waitFor(() => expect(getByText('Merchant')).toBeInTheDocument());
    await waitFor(() => expect(getByText(/Teis Ent/i)).toBeInTheDocument());
    await waitFor(() => expect(getByText('Individual / Business')).toBeInTheDocument());
    await waitFor(() => expect(getByText('Samuel')).toBeInTheDocument());
    await waitFor(() => expect(getAllByText('Valid')[0]).toBeInTheDocument());
    await waitFor(() => expect(getAllByText('KYC')[0]).toBeInTheDocument());
    await waitFor(() => expect(getByText('Region')).toBeInTheDocument());
    await waitFor(() => expect(getAllByText('NG')[0]).toBeInTheDocument());
  });

  test('Verification shows no merchant column when showing for a particulary marchant', async () => {
    const { getByText, queryByText } = render(<MockedVerificationEventTable allMerchants={false} />);
    await waitFor(() => expect(getByText('Reference')).toBeInTheDocument());
    await waitFor(() => expect(getByText('VR-WF3W54oLwD604Eqf7')).toBeInTheDocument());
    await waitFor(() => expect(queryByText('Merchant')).not.toBeInTheDocument());
    await waitFor(() => expect(queryByText('Teis Ent.')).not.toBeInTheDocument());
  });
});
