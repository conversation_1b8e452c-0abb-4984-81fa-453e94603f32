.ies {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 30px;
  row-gap: 50px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
  }
  &__event {
    width: 300px;

    @media (max-width: 1024px) {
      width: auto;
    }

    @media (max-width: 768px) {
      width: auto;
    }
  
  }

  &__event-title {
    font-size: 0.937rem;
    font-weight: 500;
    line-height: 22.8px;
  }

  &__event-description {
    font-size: 0.937rem;
    color: rgba(169, 175, 188, 1);
    margin-bottom: 10px;
  }

  &__event-value {
    font-size: 1.87rem;
    font-weight: 600;
    margin-bottom: 5px;
  }

  &__event-cta {
    text-decoration: none;

    &:hover {
      text-decoration: none;
    }
  }

}
