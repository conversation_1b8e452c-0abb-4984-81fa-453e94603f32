import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import ConfigurationFee from '../ConfigurationFee';

describe('ConfigurationFee', () => {
  it('renders without crashing', () => {
    render(
      <ConfigurationFee title="Test Title" type="KYC">
        <div>Child Content</div>
      </ConfigurationFee>
    );
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('displays the correct header', () => {
    render(
      <ConfigurationFee title="Test Title" type="KYC">
        <div>Child Content</div>
      </ConfigurationFee>
    );
    expect(screen.getByText('Current Fee')).toBeInTheDocument();
    expect(screen.getByText('New Fee')).toBeInTheDocument();
  });

  it('displays children content when provided', () => {
    render(
      <ConfigurationFee title="Test Title" type="KYC">
        <div>Child Content</div>
      </ConfigurationFee>
    );
    expect(screen.getByText('Child Content')).toBeInTheDocument();
  });

  it('displays no content message when no children are provided', () => {
    render(<ConfigurationFee title="Test Title" type="KYC" />);
    expect(screen.getByText('KYC verification is currently unavailable for this country.')).toBeInTheDocument();
  });

  it('has no accessibility violations', async () => {
    const { container } = render(
      <ConfigurationFee title="Test Title" type="KYC">
        <div>Child Content</div>
      </ConfigurationFee>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
