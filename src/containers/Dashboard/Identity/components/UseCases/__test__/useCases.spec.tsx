import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { KAASFeeType } from '+types';

import UseCases from '..';

describe('UseCases', () => {
  const mockUpdateUseCases = vi.fn();
  const mockUpdateSectionState = vi.fn();

  const useCaseOptions: KAASFeeType[] = [
    {
      key: 'use_case_1',
      title: 'Use Case 1',
      currentFee: 100,
      currency: 'NGN',
      modified: false,
      selected: true
    },
    {
      key: 'use_case_2',
      title: 'Use Case 2',
      currentFee: 200,
      currency: 'NGN',
      modified: true,
      selected: false
    }
  ];

  it('should render the CustomCheckbox with the correct label', () => {
    render(
      <UseCases
        useCaseOptions={useCaseOptions}
        category="kyc"
        updateUseCases={mockUpdateUseCases}
        updateSectionState={mockUpdateSectionState}
        sectionState={true}
        region='ng'
      />
    );

    const checkboxLabel = screen.getByText('Turn off KYC verification in Nigeria for all merchants');
    expect(checkboxLabel).toBeInTheDocument();
  });

  it('should call updateSectionState when the CustomCheckbox is clicked', async () => {
    render(
      <UseCases
        useCaseOptions={useCaseOptions}
        category="kyc"
        updateUseCases={mockUpdateUseCases}
        updateSectionState={mockUpdateSectionState}
        sectionState={true}
        region='ng'
        canUpdateUseCases={true}
      />
    );

    const checkbox = screen.getAllByRole('checkbox');
    await userEvent.click(checkbox[0]);

    expect(mockUpdateSectionState).toHaveBeenCalledWith(false);
  });

  it('should render the UseCase component with the correct title and description', () => {
    render(
      <UseCases
        useCaseOptions={useCaseOptions}
        category="kyc"
        updateUseCases={mockUpdateUseCases}
        updateSectionState={mockUpdateSectionState}
        sectionState={true}
        region='ng'
      />
    );

    const useCaseTitle = screen.getByText('ID Verification');
    const useCaseDescription = screen.getByText(
      'Choose whether merchants can verify identity documents and/or compare the data submitted with data extracted from the documents'
    );

    expect(useCaseTitle).toBeInTheDocument();
    expect(useCaseDescription).toBeInTheDocument();
  });

  it('should call updateUseCases when a use case is updated and canUpdateUseCases is true', async () => {
    render(
      <UseCases
        useCaseOptions={useCaseOptions}
        category="kyc"
        updateUseCases={mockUpdateUseCases}
        updateSectionState={mockUpdateSectionState}
        sectionState={true}
        region='ng'
        canUpdateUseCases={true}
      />
    );

    const useCaseOption = screen.getByText('Use Case 1');
    await userEvent.click(useCaseOption);

    expect(mockUpdateUseCases).toHaveBeenCalled();
  });

  it('should not call updateUseCases when a use case is updated and canUpdateUseCases is false', async () => {
    render(
      <UseCases
        useCaseOptions={useCaseOptions}
        category="kyc"
        updateUseCases={mockUpdateUseCases}
        updateSectionState={mockUpdateSectionState}
        sectionState={true}
        region='ng'
        canUpdateUseCases={false}
      />
    );

    const useCaseOption = screen.getByText('Use Case 1');
    await userEvent.click(useCaseOption);

    expect(mockUpdateUseCases).not.toHaveBeenCalled();
  });

  it('has no accessibility violations', async () => {
    const { container } = render(
      <UseCases
        useCaseOptions={useCaseOptions}
        category="kyc"
        updateUseCases={mockUpdateUseCases}
        updateSectionState={mockUpdateSectionState}
        sectionState={false}
        region='ng'
      />
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
