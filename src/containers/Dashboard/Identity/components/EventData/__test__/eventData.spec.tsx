
import React from 'react';
import { render, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';
import EventData, { TEventData } from '..';


const Mockdata = {
  name: '<PERSON>',
  age: '30',
  email: '',
  address: {
    street: '123 Main St',
    city: 'New York',
    state: 'NY'
  }
} as unknown as  TEventData;
const MockedEventData = ({ data }: { data: TEventData }) => {
  return (
    <MockIndex>
      <EventData data={data} />
    </MockIndex>
  );
};

describe('Event Data', () => {
  test('Renders Event Data', () => {
    const { getByText } = render(<MockedEventData data={Mockdata} />);
    expect(getByText('Event Data')).toBeInTheDocument();
  });

  test('Event Data is accessible', async () => {
    const { container } = render(<MockedEventData data={Mockdata} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Renders Event Data with data', () => {

    const { getByText } = render(<MockedEventData data={Mockdata} />);
    waitFor(() => expect(getByText('Event Data')).toBeInTheDocument());
    waitFor(() => expect(getByText('John Doe')).toBeInTheDocument());
    waitFor(() => expect(getByText('30')).toBeInTheDocument());
    waitFor(() => expect(getByText('123 Main')).toBeInTheDocument());
    waitFor(() => expect(getByText('New York')).toBeInTheDocument());
    waitFor(() => expect(getByText('NY')).toBeInTheDocument());
  });
});
