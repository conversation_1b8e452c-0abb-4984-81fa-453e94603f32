import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import UpdateMerchantDetailsInput from '../UpdatingMerchantDetails/UpdateMerchantDetailsInput';

describe('UpdateMerchantDetailsInput', () => {
  it('renders label and input for text', () => {
    render(<UpdateMerchantDetailsInput label="Business Name" name="businessName" value="Acme Corp" onChange={() => {}} />);
    expect(screen.getByLabelText(/Business Name/i)).toBeInTheDocument();
    expect(screen.getByDisplayValue('Acme Corp')).toBeInTheDocument();
  });

  it('renders label and select for isSelect', () => {
    const options = [
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' }
    ];
    render(<UpdateMerchantDetailsInput label="Country" name="country" isSelect value="2" options={options} onSelectChange={() => {}} />);
    expect(screen.getByLabelText(/Country/i)).toBeInTheDocument();
    expect(screen.getByDisplayValue('Option 2')).toBeInTheDocument();
  });

  it('calls onChange for text input', async () => {
    const handleChange = vi.fn();
    render(<UpdateMerchantDetailsInput label="Business Name" name="businessName" value="" onChange={handleChange} />);
    const input = screen.getByLabelText(/Business Name/i);
    await userEvent.type(input, 'A');
    expect(handleChange).toHaveBeenCalled();
  });

  it('calls onSelectChange for select', async () => {
    const handleSelect = vi.fn();
    const options = [
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' }
    ];
    render(<UpdateMerchantDetailsInput label="Country" name="country" isSelect value="" options={options} onSelectChange={handleSelect} />);
    const select = screen.getByLabelText(/Country/i);
    await userEvent.selectOptions(select, '2');
    expect(handleSelect).toHaveBeenCalledWith({ label: 'Option 2', value: '2' }, null);
  });

  it('shows error message if error is true', () => {
    render(<UpdateMerchantDetailsInput label="Business Name" name="businessName" value="" error errorMessage="This is an error" />);
    expect(screen.getByText('This is an error')).toBeInTheDocument();
  });
});
