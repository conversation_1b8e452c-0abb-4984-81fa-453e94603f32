import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { http, HttpResponse } from 'msw';
import { vi } from 'vitest';

import { mockedCountriesData } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import MerchantModals from '../MerchantModals';

const mockSetModal = vi.fn();
const mockClose = vi.fn();
const mockDetails = { name: 'Acme', email: '<EMAIL>', country: 'NG' };

const ActionModalType = {
  updateMerchantDetails: 'update-merchant-details',
  updateMerchantDetailsConfirm: 'update-merchant-details-confirm'
};

server.use(
  http.get('/admin/misc/countries', () => {
    return HttpResponse.json(mockedCountriesData);
  }),

  http.get('/admin/merchants/tier-levels', () => {
    return HttpResponse.json({ data: [] });
  })
);

describe('MerchantModals - updateMerchantDetails', () => {
  it('renders the updateMerchantDetails modal', () => {
    render(
      <MockIndex>
        <MerchantModals
          close={mockClose}
          type={ActionModalType.updateMerchantDetails}
          details={mockDetails}
          id="23"
          verifyMerchant={{}}
          updateMerchantBvn={{}}
          rejectMerchant={{}}
          setModal={vi.fn()}
          setFeedbacks={vi.fn()}
        />
      </MockIndex>
    );

    expect(screen.getByText('Edit Merchant Profile')).toBeInTheDocument();
    expect(screen.getByText(/Updated merchant details will be applied across all payment channels/)).toBeInTheDocument();
  });

  it('renders the confirmation modal and verifies the API payload on submission', async () => {
    let apiCalled = false;
    let requestData: any = null;

    mockSetModal.mockClear();

    server.use(
      http.patch('/admin/merchants/23/profile', async ({ request }) => {
        requestData = await request.json();
        apiCalled = true;

        expect(requestData).toHaveProperty('name');
        expect(requestData).toHaveProperty('email');
        expect(requestData).toHaveProperty('business_address_details');

        return HttpResponse.json({
          data: {
            status: true
          }
        });
      })
    );

    render(
      <MockIndex>
        <MerchantModals
          close={mockClose}
          type={ActionModalType.updateMerchantDetailsConfirm}
          details={mockDetails}
          id="23"
          verifyMerchant={{}}
          updateMerchantBvn={{}}
          rejectMerchant={{}}
          setModal={mockSetModal}
          setFeedbacks={vi.fn()}
        />
      </MockIndex>
    );

    expect(screen.getByText('Confirm Update')).toBeInTheDocument();
    expect(screen.getByText(/Kindly confirm that you want to apply the update/)).toBeInTheDocument();
    expect(screen.getByText('Yes Apply')).toBeInTheDocument();

    const applyButton = screen.getByText('Yes Apply');
    fireEvent.click(applyButton);

    await vi.waitFor(
      () => {
        expect(apiCalled).toBe(true);
      },
      { timeout: 3000 }
    );
  });
});
