@import 'styles/kpy-custom/variables';

.user-avatar {
  width: 70px;
  height: 70px;
  border-radius: 40px;
  overflow: hidden;
  margin-bottom: 1rem;
}
.user-avatar img {
  max-width: 100%;
  height: auto;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;

  img:nth-child(1) {
    width: 15px;
  }
}

.merchant-id {
  display: flex;
  gap: 0.25rem;
}

.main-container {
  min-height: 80vh;
  display: flex;
  flex-direction: column;

  .compliance-ticker {
    display: inline-block;
    margin: -20px 0 0 5px;
  }

  &__footer {
    margin-top: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    gap: 1rem;

    @media (min-width: $breakpoint-tablet) {
      flex-direction: row;
    }
  }
}

.date {
  color: #9fa8b6;
  font-size: 0.9rem;
}

.footer-actions {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 1rem;

  @media (min-width: $breakpoint-desktop) {
    flex-direction: row;
  }

  button {
    border: none;
    padding: 0.5em 1em;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    span {
      font-size: 0.9rem;
      font-weight: 500;
    }

    img {
      width: 12px;
    }
  }
  &__verified {
    background: rgba(36, 179, 20, 0.15);
    color: #24b314;
  }

  &__verify {
    background-color: #24b314;
    color: #ffffff;

    &:disabled {
      opacity: 0.5;
    }
  }

  &__send {
    background-color: #307ef3;
    color: #ffffff;

    &:disabled {
      opacity: 0.5;
    }
  }
  &__unsent {
    font-weight: 400;
    color: #9fa8b6;
    background-color: transparent;
  }
  .count {
    border-radius: 100%;
    width: 18px;
    height: 18px;
    background-color: #f32345;
    color: #ffffff;
    font-size: 0.8rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

#c-out {
  cursor: not-allowed;
}

.kyc-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0 30px 0;
  flex-direction: column;

  @media (min-width: $breakpoint-tablet) {
    flex-direction: row;

    &__right {
      margin-top: 0;
    }
  }

  &__left,
  &__right,
  &__risk {
    display: flex;
    align-items: center;
  }

  &__right {
    margin-top: 1.5rem;
    flex-direction: column;

    @media (min-width: $breakpoint-desktop) {
      flex-direction: row;
    }
  }

  &__left {
    font-size: 1rem;
    font-weight: 300;
    line-height: 1.5;
    color: #636c72;
    text-align: left;

    h3 {
      word-break: break-all;
      margin-bottom: 0.2rem;
    }
  }

  .merchant-ellipsis {
    padding: 12px 20px;
    top: 199px;
    right: 122px;

    @media (min-width: $breakpoint-tablet) {
      top: 117px;
      right: 28px;
    }

    @media (min-width: $breakpoint-desktop) {
      top: 103px;
      right: 40px;
    }
  }

  &__title {
    margin-left: 1rem;
  }

  &__risk {
    gap: 0.5rem;
    margin-bottom: 1rem;

    span {
      font-weight: bold;
      color: '#3E4B5B';
    }

    @media (min-width: $breakpoint-desktop) {
      margin-bottom: 0;
    }
  }
}

.kyc-tab-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  margin-bottom: 30px;

  @media (min-width: $breakpoint-desktop) {
    flex-direction: row;
  }

  .feedback-container {
    margin-top: 2rem;

    @media (min-width: $breakpoint-desktop) {
      margin-top: 0;
    }

    button {
      border: none;
      background-color: #eaf2fe;
      color: #292b2c;
      font-size: 0.9rem;
      padding: 0.5em 1em;
      border-radius: 5px;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      &:disabled {
        color: #a0a0a0;

        img {
          opacity: 0.3;
        }
      }

      img {
        width: 18px;
      }
    }

    p {
      color: #24b314;
      font-weight: 600;
      font-size: 0.9rem;
      margin-top: 1rem;
      text-align: center;

      img {
        width: 14px;
      }
    }
  }
}

// Components
.leading-text {
  width: 95%;
  max-width: 70ch;
  color: #999;
  font-weight: 300;
  font-size: 0.9rem;
  // padding-bottom: 1rem;
}

.details-wrapper {
  width: 95%;
  max-width: 800px;

  &.is-disabled {
    opacity: 0.5;
  }
}

// BusinessProfile, MerchantDocuments, MerchantRepresentatives, MerchantSettlementAccount
.bk-text {
  color: #292b2c;
}

.spacing {
  margin: 0;

  @media (min-width: $breakpoint-tablet) {
    margin-left: 3rem;
  }
}
.vnin-text {
  color: rgba(41, 43, 44, 0.9);
  font-weight: 400;
}
.copy-text {
  color: #2376f3;
}
.gy-text {
  color: #9fa6ae;
  word-break: break-all;
}
.gy-link {
  color: #2376f3;
  word-break: break-all;
  display: block;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.gn-text {
  color: #24b314;
}
.red-text {
  color: #f32345;
}
.rd-text {
  color: #5e0513;
}
.og-text {
  color: #ffb447;
}

.add-doc {
  margin-top: 40px;
  color: rgba(16, 38, 73, 0.5);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
}

.entry {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  background-color: #f9fbfd;
  border-radius: 10px;
  padding: 10px 20px;

  svg {
    height: 20px;
    width: 20px;
  }
  @media (min-width: $breakpoint-tablet) {
    flex-direction: row;
  }

  &__two {
    flex-direction: column;
    align-items: flex-start;
  }
}

.entry-invalid {
  background-color: #ffd2da;
  .gy-text {
    color: #414f5f;
  }
}
.entry-valid {
  background-color: #e4fff199;
  .gy-text {
    color: #414f5f;
  }
}
.entry-group {
  display: grid;
  gap: 10px;
  grid-template-columns: 1fr;

  @media (min-width: $breakpoint-desktop) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.entry-doc {
  margin-bottom: 1rem;
  flex-direction: column;
  gap: 1rem;

  @media (min-width: $breakpoint-tablet) {
    flex-direction: row;
  }

  &__left,
  &__right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  &__right {
    flex-direction: column;

    @media (min-width: $breakpoint-tablet) {
      flex-direction: row;
    }

    .gy-text {
      max-width: 10rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      @media (min-width: $breakpoint-desktop) {
        max-width: 15rem;
      }
    }
  }
}

.entry-pill {
  color: #354253;
  background-color: #eaeaea;
  border-radius: 5px;
  padding: 0.5em 1em;
}

.entry-acc {
  margin-bottom: 1rem;
  flex-direction: column;
  gap: 1rem;

  &.active {
    background: rgba(72, 206, 176, 0.1);
  }

  &.rejected {
    background: rgba(243, 35, 69, 0.1);
  }

  @media (min-width: $breakpoint-tablet) {
    flex-direction: row;
  }

  &__left {
    display: flex;
    align-items: center;
    flex-direction: column;

    @media (min-width: $breakpoint-tablet) {
      align-items: center;
      flex-direction: row;
    }

    & > * {
      margin-right: 20px;
    }
  }

  &__right {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    img {
      width: 12px;
    }
  }

  &:hover {
    border: 1px solid #e2eefc;
  }

  button,
  .button-div {
    border: none;
    background-color: transparent;
    color: #2376f3;
    font-size: 0.9rem;
    cursor: pointer;
  }

  .bvn-declined {
    color: #94a7b7;
  }
}

// ActionModal Component
.ellipsis {
  position: relative;
  display: flex;
  justify-content: flex-end;
  padding: unset;

  &__icon {
    cursor: pointer;
    border: none;
    background: initial;
    &:active,
    &:focus {
      border: none;
    }
  }

  &__nav {
    position: absolute;
    right: 20px;
    top: 80px;
    width: min-content;
    white-space: nowrap;
    text-align: left;
    z-index: 5;
    filter: drop-shadow(0px 2px 20px rgba(15, 24, 33, 0.09));
  }

  &__item {
    cursor: pointer;
    list-style: none;

    span {
      font-size: 13px;
      line-height: 23px;
      letter-spacing: -0.21px;
      color: #3e4b5b;
    }

    &:not(:last-of-type) {
      margin-bottom: 10px;
    }
  }

  @media (max-width: 1140px) {
    justify-content: flex-start;
    padding: 0px;
    &__nav {
      right: 0;
      top: -430%;
    }
  }
}

// Feedback
.feedback-kyc {
  border-top: 1px solid rgb(222, 226, 230);
  padding: 20px 0;
  &__text {
    color: #8f98a6;
    font-style: italic;
    font-size: 0.9rem;
  }
}

.feedback-group {
  display: flex;
  gap: 2rem;
  width: 95%;
  max-width: 800px;
  margin-bottom: 1rem;

  &__logo {
    width: 30px;
    img {
      width: 100%;
      object-fit: cover;
    }
  }

  &__details {
    position: relative;
    display: flex;
    flex-direction: column;
    background-color: #f1f6fa;
    padding: 10px 20px;
    border-radius: 0 5px 5px 5px;
    width: 100%;
    max-width: fit-content;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -20px;
      width: 20px;
      height: 20px;
      background-color: #f1f6fa;
      clip-path: polygon(0 0, 100% 0, 100% 100%);
    }

    span {
      font-size: 1rem;
      color: #102649;
      &:nth-child(1) {
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
      &:nth-child(2) {
        mix-blend-mode: normal;
        opacity: 0.5;
        word-break: break-word;
      }
    }
  }
}

.feedback-modal {
  p:nth-child(1) {
    color: #3e4b5b;
    opacity: 0.7;
  }
  p:nth-of-type(2) {
    color: #a9afbc;
    background-color: #f3f4f8;
    border-radius: 5px;
    padding: 1rem;
  }
}

.feedback-unsent {
  color: #102649;
  background-color: #f1f6fa;
  border-radius: 5px;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  mix-blend-mode: normal;

  &__heading {
    font-size: 1rem;
    opacity: 0.8;
  }

  &__details {
    opacity: 0.5;
    margin-bottom: 0.5rem;
  }

  &__actions {
    display: flex;
    gap: 1rem;
    justify-content: space-between;

    span:nth-of-type(1) {
      opacity: 1;
    }
    button {
      opacity: 0.8;
      border: none;
      color: #8c91a5;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background-color: transparent;

      img {
        width: 16px;
      }
    }
  }
}

.bvn-details-modal {
  text-align: center;

  .bvn-number {
    font-size: 1.5rem;
    color: #414f5f;
    margin-top: 16px;
    margin-bottom: 6px;
  }

  button {
    border: none;
    background-color: transparent;
    color: #2376f3;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;

    img {
      width: 12px;
    }
  }

  .bvn-details {
    background-color: #f1f6fa;
    padding: 0 20px 25px;
    border-radius: 7px;
    margin-top: 20px;

    div {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #dde2ec;
      p {
        color: #414f5f;
        font-size: 13px;
        margin-top: 1rem !important;
        margin-bottom: 10px;
        span {
          font-weight: 500;
        }
      }
    }
  }
}

// Merchant tab switch
.tab-switch {
  display: inline-block;

  &__list {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    border: 2px solid #2376f3;
    border-radius: 4px;
    align-items: center;

    li {
      border-radius: 4px;
    }

    li + li button {
      border-left: 2px solid #2376f3;
    }

    li:first-child button {
      border-top-left-radius: 1px;
      border-bottom-left-radius: 1px;
    }

    li:last-child button {
      border-top-right-radius: 1px;
      border-bottom-right-radius: 1px;
    }
  }

  &__button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.125rem 0.75rem;
    padding-top: 3px;
    border-radius: 0;
    font-weight: 500;
    color: #2376f3;

    &.active {
      color: white;
      background-color: #2376f3;
    }
  }

  &__count {
    background-color: #f32345;
    color: #ffffff;
    font-size: 0.8rem;
    width: 1rem;
    height: 1rem;
    border-radius: 100%;
    justify-content: center;
    align-items: center;
    display: inline-flex;
  }
}

.risk-container {
  height: 450px;
  overflow-y: auto;

  input {
    padding: 7px 11px;
    border: 2.5px solid #e3e7ee;
    box-shadow: none;
    font-size: 14px;

    &:focus {
      box-shadow: none;
    }
  }

  .country-option {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .country-flag {
    width: 24px;
    height: 18px;
    border-radius: 2px;
    overflow: hidden;
  }

  .country-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .address-form-group {
    display: flex;
    gap: 1rem;

    .form-group {
      width: 100%;
    }
  }

  em {
    font-size: 12px;
  }

  .risk-list {
    width: 100%;
    padding: 7px 11px;
    font-weight: unset;
  }

  .list-dropdown--overlay {
    height: 151%;
    width: 106%;
    top: -38%;
    left: -3%;
    position: relative;
    width: 100%;
    left: 0;
    top: -3px;
  }
}

.merchant-class {
  width: 100%;
  padding: 7px 11px;
  font-weight: unset;
  cursor: pointer;
  border: 2px solid #dde2ec;
}

// rejected
.rejected-modal-content {
  padding: 0 0.89rem;

  > p {
    &:first-child {
      font-size: 0.9rem;
      font-style: normal;
      font-weight: 400;
      line-height: 23px;
      letter-spacing: -0.057px;
      opacity: 0.7;
    }
  }

  .rejected-modal-message {
    display: flex;
    padding: 0.75rem 0.95rem;
    align-items: flex-start;
    gap: 0.47rem;
    align-self: stretch;
    background-color: #fff8e1;
    margin-bottom: 1.42rem;
    margin-top: 0.76rem;
    border-radius: 0.24rem;

    img {
      width: 1.142rem;
      height: 1.142rem;
    }

    p {
      margin-bottom: 0;
      color: #414f5f;
      font-size: 0.9rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.089rem;
      letter-spacing: -0.057px;
    }
  }

  .rejected-modal-radio {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.95px;
    padding: 0.5rem 1.42rem;
  }

  .modal-input-rejected {
    height: auto;
  }
}

#merchant-name {
  align-items: center;
  display: flex;
  gap: 0.5rem;
}

.sen-disabled {
  background: #f1f6fa;
  border-radius: 6px;
  color: #000;
  display: inline-block;
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
}

.tier-option {
  color: #94a7b7;
  font-size: 0.8rem;
  margin-bottom: 0;
}

.tier-ranking {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.tier-menu {
  gap: 0.5rem;
  margin: 0;
  display: flex;
  align-items: center;
  justify-items: center;
  cursor: pointer;
}

.kyc-heading__title_unique {
  color: #c10b28;
  font-size: 0.9rem;
  background-color: #ffd2da;
  padding: 0.3rem 0.6rem;
  border-radius: 0.3rem;
}
