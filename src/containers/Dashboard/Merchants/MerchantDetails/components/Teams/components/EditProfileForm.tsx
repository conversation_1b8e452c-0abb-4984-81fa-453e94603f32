import React, { useEffect } from 'react';
import { Field, Formik, FormikProps } from 'formik';

import { useFeedbackHandler } from '+hooks';
import { EmailValidation } from '+utils';

interface IEditProfileFormProps {
  initialValues?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  onSubmit: (values: any) => void;
}

type EditProfileFormValues = {
  firstName: string;
  lastName: string;
  email: string;
};

const EditProfileForm = ({ initialValues = { firstName: '', lastName: '', email: '' }, onSubmit }: IEditProfileFormProps) => {
  const { feedbackInit } = useFeedbackHandler();

  const validate = (values: typeof initialValues) => {
    const errors: Partial<typeof values> = {};

    if (!values.firstName) {
      errors.firstName = 'First name is required';
    }

    if (!values.lastName) {
      errors.lastName = 'Last name is required';
    }

    if (!values.email) {
      errors.email = 'Email is required';
    } else if (EmailValidation(values.email)) {
      errors.email = 'Invalid email format';
    }

    return errors;
  };

  const formRef = React.useRef<FormikProps<EditProfileFormValues> | null>(null);

  return (
    <Formik
      initialValues={initialValues}
      validate={validate}
      innerRef={formRef}
      onSubmit={(values, { setSubmitting }) => {
        try {
          onSubmit(values);
          setSubmitting(false);
        } catch (error) {
          feedbackInit({
            message: 'An error occurred while updating the profile',
            type: 'danger',
            componentLevel: true
          });
          setSubmitting(false);
        }
      }}
    >
      {({ values, errors, touched, handleSubmit }) => {
        useEffect(() => {
          onSubmit(values);
        }, [values.firstName, values.lastName, values.email]);
        return (
          <form onSubmit={handleSubmit} className="form-center col-sm-12">
            <fieldset className="form-group my-1">
              <div className="form-group">
                <Field name="firstName">
                  {({ field }: { field: any }) => (
                    <>
                      <label htmlFor="firstName">First Name</label>
                      <input
                        {...field}
                        id="firstName"
                        name="firstName"
                        className="form-control"
                        type="text"
                        placeholder="Enter first name"
                        aria-required="true"
                        aria-describedby={touched.firstName && errors.firstName ? 'firstName-error' : undefined}
                        onChange={e => {
                          field.onChange(e);
                          onSubmit({ ...values, firstName: e.target.value });
                        }}
                      />
                      {touched.firstName && errors.firstName && (
                        <div className="input__errors" id="firstName-error" role="alert">
                          <p>{errors.firstName}</p>
                        </div>
                      )}
                    </>
                  )}
                </Field>
              </div>

              <div className="form-group">
                <Field name="lastName">
                  {({ field }: { field: any }) => (
                    <>
                      <label htmlFor="lastName">Last Name</label>
                      <input
                        {...field}
                        id="lastName"
                        name="lastName"
                        className="form-control"
                        type="text"
                        placeholder="Enter last name"
                        aria-required="true"
                        aria-describedby={touched.lastName && errors.lastName ? 'lastName-error' : undefined}
                      />
                      {touched.lastName && errors.lastName && (
                        <div className="input__errors" id="lastName-error" role="alert">
                          <p>{errors.lastName}</p>
                        </div>
                      )}
                    </>
                  )}
                </Field>
              </div>

              <div className="form-group">
                <Field name="email">
                  {({ field }: { field: any }) => (
                    <>
                      <label htmlFor="email">Email Address</label>
                      <input
                        {...field}
                        id="email"
                        name="email"
                        className="form-control"
                        type="email"
                        placeholder="Enter email address"
                        aria-required="true"
                        aria-describedby={touched.email && errors.email ? 'email-error' : undefined}
                      />
                      {touched.email && errors.email && (
                        <div className="input__errors" id="email-error" role="alert">
                          <p>{errors.email}</p>
                        </div>
                      )}
                    </>
                  )}
                </Field>
              </div>

              <button type="submit" className="btn btn-primary" aria-label="Update profile information">
                Update
              </button>
            </fieldset>
          </form>
        );
      }}
    </Formik>
  );
};

export default EditProfileForm;
