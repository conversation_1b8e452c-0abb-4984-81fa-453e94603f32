.transaction-overview-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 50px;
}

.transaction-overview-stats {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  gap: 24px;
}
.overview-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 20px;
  flex-direction: row-reverse;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  & .customer-insights-header {
    margin-top: 10px;

    @media screen and (max-width: 576px) {
      margin-top: 0;
      flex-wrap: wrap;
    }
  }
}
.trasaction-details-body {
  border-top: none;
}
.transaction-details-g-menu {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20px;
  margin-bottom: 50px !important;
}
.filter-container {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-bottom: 30px !important;
}
.chart-container {
  flex: 0 0 1;
  max-width: 300px;
  margin-right: 40px;
}

.doughnut-chart-wrapper {
  display: flex;
  gap: 20px;
}

.doughnut-chart {
  position: relative;
  width: 150px;
  height: 150px;
}

.doughnut-center-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.doughnut-center-label strong {
  display: block;
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.doughnut-center-label span {
  font-size: 12px;
  color: #777;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 12px;
  font-size: 12px;
}

.legend-color {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
}

.transaction-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 20px;
  align-items: center;
  justify-content: center;
  width: 60%;
  margin-left: 40px;
}

.metric-card {
  width: 100%;
}
.metric-card:nth-child(3),
.metric-card:nth-child(4) {
  grid-row: 2;
}

.metric-card:nth-child(3) {
  grid-column: 1;
}

.metric-card:nth-child(4) {
  grid-column: 2;
}

.metric-label {
  font-size: 14px;
  color: #718096;
  font-weight: 400;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.error-value {
  color: #e65252;
}

@media (max-width: 992px) {
  .transaction-overview-stats {
    flex-direction: column;
  }
  .transaction-metrics {
    width: 100%;
    margin-left: 0px;
    justify-content: center;
  }
  .chart-container {
    margin-right: 0;
    max-width: 100%;
  }

  .doughnut-chart {
    margin: 0 auto;
  }

  .metric-card {
    flex: 0 0 calc(50% - 20px);
  }
}

@media (max-width: 576px) {
  .metric-card {
    flex: 0 0 100%;
  }
}

// customer analytics //

.customer-analytics-container {
  display: flex;
  overflow: hidden;
  width: 100%;
  gap: 20px;
  margin-top: 30px;
  justify-content: space-between;
}
.customer-analytics__duration {
  align-self: flex-start;
}
.tabs-navigation {
  width: 35%;
}

.tabs-list {
  width: 80%;
  display: flex;
  flex-direction: column;
  p {
    color: #94a7b7;
    padding-left: 10px;
  }
}

.tab-button {
  width: 100%;
  text-align: left;
  color: #4b5563;
  background: transparent;
  border: none;
  cursor: pointer;
  transition:
    background-color 0.2s,
    color 0.2s;
  position: relative;
  padding: 10px;
  border-radius: 8px;
}

.tab-button.active {
  background-color: #eff6ff;
  color: #2563eb;
  font-weight: 500;
}

.content-area {
  flex: 1;
}

.customer-tab-content {
  max-width: 36rem;
}

.content-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #292b2c;
}

.content-description {
  color: #94a7b7;
  margin-bottom: 1rem;
}

.action-button {
  color: #3b82f6;
  font-weight: 500;
  background: transparent;
  border: none;
  padding: 0.25rem 0;
  cursor: pointer;
}

.action-button:hover {
  text-decoration: underline;
}

.action-button-disabled {
  color: #94a7b7;
  font-weight: 500;
  background: transparent;
  border: none;
  padding: 0.25rem 0;
  cursor: not-allowed;
}

.transaction-type-selector {
  position: relative;
  display: inline-block;
  padding: 0px;
  margin-bottom: 5px;
}
.transaction-overview-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
  }
  h6{
    margin-bottom: -0.5rem !important;
  }
}
.transaction-type-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 4px;
  margin-top: 0.5rem;
}

.transaction-type-button h6 {
  margin: 0;
  padding-right: 8px;
  color: #94a7b7;
}

.dropdown-toggle-trn {
  white-space: nowrap;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: block;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #94a7b7;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #94a7b7;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  cursor: pointer;
}

.dropdown-item:hover,
.dropdown-item:focus {
  color: #94a7b7;
  text-decoration: none;
  background-color: #f8f9fa;
}

.dropdown-item.active,
.dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #007bff;
}
.reduced-height-datefilter {
  height: 30px;
  padding: 0px !important;
  width: 120px;
}

.reduced-height-datefilter input.form-control {
  height: 100%;
  line-height: 1;
  font-weight: 500;
  padding: 0px !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
  width: 75%;
}

.reduced-height-datefilter > .form-group.--search-container i {
  width: 1.05rem;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0.7rem;
  left: 7.2rem !important;
  font-size: 0.7rem;
  font-weight: 900;
}
@media (max-width: 650px) {
  .customer-analytics-container {
    display: flex;
    flex-direction: column;
  }
  .tabs-navigation {
    width: 100%;
  }
  .tabs-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .tab-button.active {
    background-color: transparent;
    border-bottom: 2px solid #2563eb;
    color: #2563eb;
    font-weight: 500;
    border-radius: 0px;
  }
  .tab-button {
    background-color: transparent;
    border-bottom: 1px solid #e5e7eb;
    color: #4b5563;
    font-weight: 500;
    border-radius: 0px;
    display: flex;
    justify-content: center;
  }
}
.overview-divider {
      background-color: #dde2ec !important;
      width: 1.5px;
      height: 18px;
      margin: 10px;
}
.transactionoverview-divider{
  background-color: #dde2ec !important;
  width: 1.5px;
  height: 18px;
  margin-top: 10px;
}
.overview-divider-customer {
  background-color: transparent !important;
  width: 1.5px;
  height: 18px;
  margin-top: 10px;
}
.payins-customer-icon-wrap {
  margin-right: 10px;
  padding: 0px;
}
.payins-customer-name {
  width: 100%;
  display: flex;
  flex-direction: row;
}
.overview_invoice {
  width: 100%;
  margin-bottom: 100px;
}
