import React, { useState } from 'react';
import { Doughnut } from 'react-chartjs-2';

import DateFilter from '+containers/Dashboard/Shared/DateFilter';
import Icon from '+containers/Dashboard/Shared/Icons';
import CurrencyPicker from '+shared/CurrencyPicker';
import LoadingPlaceholder from '+shared/LoadingPlaceHolder';
import { IDateRange, ITransactionChart, Nullable } from '+types';
import { formatAmount } from '+utils';

import useGetMerchantTransactionAnalytics from './hooks/useGetMerchantTransactionAnalytics';
import { chartData, chartOptions, EmptyChartState, hasChartData, renderChartLegend, summaryValues } from './TransactionAnalyticsHelper';

import '../../index.scss';

interface ITransactionChartExtended extends ITransactionChart {
  durationTabs: Record<string, string | string[] | Nullable<string>[] | null[]>;
}

const TransactionChart: React.FC<ITransactionChartExtended> = ({ setActiveCurrency, availableCurrencies, activeCurrency }) => {
  const [transactionType, setTransactionType] = useState<'payins' | 'payouts'>('payins');
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState<IDateRange>({
    startDate: 'all_time',
    endDate: 'all_time'
  });
  const [isDateCleared, setIsDateCleared] = useState<boolean>(false);

  const customDateOptions = ['All Time', 'Today', 'Last 5 days', 'Last 7 days', 'Last 30 days', 'Last 90 days'] as const;

  const toggleDropdown = () => setDropdownOpen(prev => !prev);

  const { data, isLoading } = useGetMerchantTransactionAnalytics(transactionType, activeCurrency, dateRange);

  const summary = data?.data?.data;
  const statusList = summary?.status || [];

  const handleTransactionTypeChange = (type: 'payins' | 'payouts') => {
    setTransactionType(type);
    setDropdownOpen(false);
  };

  return (
    <div className="content-details-box">
      <div className="content-details-body trasaction-details-body">
        <div className="element-wrapper element-info">
          <div className="element-actions">
            <form className="merchant-details-g-menu form-inline transaction-details-g-menu">
              <div className="filter-container" style={{ display: 'flex', gap: '20px', alignItems: 'center', paddingBottom: '30px' }}>
                <DateFilter
                  selectedDate={dateRange}
                  onDateChange={date => {
                    setDateRange(date);
                    setIsDateCleared(false);
                  }}
                  isCleared={isDateCleared}
                  hideCalendarIcon={true}
                  customDateOptions={customDateOptions}
                  className="reduced-height-datefilter"
                />

                <CurrencyPicker
                  options={availableCurrencies}
                  onChange={value => setActiveCurrency(value)}
                  activeCurrency={activeCurrency?.toUpperCase()}
                  label={null}
                />
              </div>
            </form>
          </div>
          <span className="transaction-overview-header">
            <h6 className="">Transaction Overview</h6>
            <span className="transactionoverview-divider" />
            <div className="transaction-type-selector">
              <div className="dropdown">
                <button onClick={toggleDropdown} className="dropdown-toggle-trn transaction-type-button" type="button">
                  <h6>{transactionType === 'payins' ? 'Pay-ins' : 'Payouts'}</h6>
                  <Icon name="caretDown" width={10} height={10} style={{marginTop: '10px'}} />
                </button>
                {dropdownOpen && (
                  <div className="dropdown-menu show">
                    <button
                      className={`dropdown-item ${transactionType === 'payins' ? 'active' : ''}`}
                      onClick={() => handleTransactionTypeChange('payins')}
                    >
                      Pay-ins
                    </button>
                    <button
                      className={`dropdown-item ${transactionType === 'payouts' ? 'active' : ''}`}
                      onClick={() => handleTransactionTypeChange('payouts')}
                    >
                      Payouts
                    </button>
                  </div>
                )}
              </div>
            </div>
          </span>
        </div>

        <div className="transaction-overview-container">
          <div className="transaction-overview-stats">
            <div className="chart-container">
              {isLoading ? (
                <LoadingPlaceholder type="table" content={1} background="#f5f6f6" />
              ) : hasChartData(statusList) ? (
                <div className="doughnut-chart-wrapper">
                  <div className="doughnut-chart">
                    <Doughnut data={chartData(statusList)} options={chartOptions} />
                  </div>
                  <div className="chart-legend">{renderChartLegend(statusList)}</div>
                </div>
              ) : (
                <EmptyChartState />
              )}
            </div>

            <div className="transaction-metrics">
              {summaryValues(summary, activeCurrency, transactionType).map(item => (
                <div className="metric-card" key={item?.label || '--'}>
                  <h3 className="metric-label">{item?.label || '--'}</h3>
                  <p className={`metric-value ${item?.isError ? 'error-value' : ''}`}>
                    {item.label.includes('Value') ? formatAmount(item?.value) : item?.value?.toLocaleString() || '--'}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionChart;
