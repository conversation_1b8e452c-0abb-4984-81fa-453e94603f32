import React from 'react';
import { Link } from 'react-router-dom';

import LoadingPlaceholder from '+shared/LoadingPlaceHolder';
import { TabContentProps, TabId } from '+types';

export const tabs: { id: TabId; label: string }[] = [
  { id: 'pay-in', label: 'Pay-ins' },
  { id: 'payouts', label: 'Payouts' }
];

const PayInTabContent: React.FC<TabContentProps & { activeCurrency: string }> = ({ customer, isLoading, id, activeCurrency }) => {
  if (isLoading) {
    return <LoadingPlaceholder type="table" content={1} background="#f5f6f6" />;
  }

  const payInCount = customer?.count || 0;

  return (
    <div className="customer-tab-content">
      <h2 className="content-title">{payInCount.toLocaleString()}</h2>
      <p className="content-description">Customers with unique details (emails) have made payments to this merchant.</p>
      {payInCount > 0 ? (
        <Link className="action-button" to={`/dashboard/merchants/customers/${id}?subTab=payins-customers&currency=${activeCurrency}`}>
          See Pay-ins Customers
        </Link>
      ) : (
        <button className="action-button-disabled" disabled>
          No Pay-ins Customers
        </button>
      )}
    </div>
  );
};

const PayoutTabContent: React.FC<TabContentProps & { activeCurrency: string }> = ({ customer, isLoading, id, activeCurrency }) => {
  if (isLoading) {
    return <LoadingPlaceholder type="table" content={1} background="#f5f6f6" />;
  }

  const payoutCount = customer?.count || 0;

  return (
    <div className="customer-tab-content">
      <h2 className="content-title">{payoutCount.toLocaleString()}</h2>
      <p className="content-description">Customers that have received payouts from this merchant.</p>
      {payoutCount > 0 ? (
        <Link className="action-button" to={`/dashboard/merchants/customers/${id}?subTab=payouts-customers&currency=${activeCurrency}`}>
          See Payout Customers
        </Link>
      ) : (
        <button className="action-button-disabled" disabled>
          No Payout Customers
        </button>
      )}
    </div>
  );
};

export const getTabContent = (tabId: TabId, props: TabContentProps & { activeCurrency: string }) => {
  switch (tabId) {
    case 'pay-in':
      return <PayInTabContent {...props} />;
    case 'payouts':
      return <PayoutTabContent {...props} />;
    default:
      return null;
  }
};
