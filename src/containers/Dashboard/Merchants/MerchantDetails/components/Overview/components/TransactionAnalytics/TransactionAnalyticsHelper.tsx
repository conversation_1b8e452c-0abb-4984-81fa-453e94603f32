import React from 'react';

const STATUS_MAPPING: Record<string, { name: string; color: string }> = {
  success: { name: 'Successful', color: '#48CEB0' },
  failed: { name: 'Failed', color: '#F32345' },
  processing: { name: 'Processing', color: '#FFB447' },
  pending: { name: 'Pending', color: '#6474FF' },
  reversed: { name: 'Reversed', color: '#2CADFF' },
  expired: { name: 'Expired', color: '#414F5F' }
};

type StatusObj = { status: string; count: number };

export const getStatuses = (statusList: StatusObj[] = []) =>
  Object.keys(STATUS_MAPPING).map(key => {
    const match = statusList.find(s => s.status?.toLowerCase() === key);
    return {
      name: STATUS_MAPPING[key].name,
      value: match ? match.count : 0,
      color: STATUS_MAPPING[key].color
    };
  });

export const summaryValues = (summary: any, activeCurrency: string, transactionType: string) => [
  { label: 'Total Transactions', value: summary?.count ?? 0 },
  {
    label: 'Successful Transactions',
    value: summary?.status?.find((s: StatusObj) => s.status === 'success')?.count ?? 0
  },
  {
    label: 'Failed Transactions',
    value: summary?.status?.find((s: StatusObj) => s.status === 'failed')?.count ?? 0,
    isError: true
  },
  {
    label: `${transactionType === 'payins' ? 'Pay-ins' : 'Payouts'} Value (${String(activeCurrency).toUpperCase()})`,
    value: summary?.total_amount ?? 0
  }
];

export const hasChartData = (statusList: StatusObj[] = []) => {
  const statuses = getStatuses(statusList);
  return statuses.some(status => status.value > 0);
};

export const chartData = (statusList: StatusObj[] = []) => {
  const statuses = getStatuses(statusList);
  return {
    labels: statuses.map(status => status.name),
    datasets: [
      {
        data: statuses.map(status => status.value),
        backgroundColor: statuses.map(status => status.color),
        borderWidth: 0,
        hoverOffset: 4
      }
    ]
  };
};

export const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  cutout: '50%',
  plugins: {
    legend: { display: false },
    tooltip: {
      backgroundColor: '#07002C',
      titleColor: 'white',
      bodyColor: 'white',
      borderColor: '#07002C',
      borderWidth: 0,
      caretSize: 5,
      displayColors: true
    }
  }
};

export const renderChartLegend = (statusList: StatusObj[] = []) => {
  if (!hasChartData(statusList)) {
    return null;
  }

  const statuses = getStatuses(statusList);

  return statuses
    .filter(status => status.value > 0)
    .map(status => (
      <div className="legend-item" key={status.name}>
        <span className="legend-color" style={{ backgroundColor: status.color }}></span>
        <span className="legend-label">{status?.name}</span>
      </div>
    ));
};

export const EmptyChartState = () => (
  <div className="empty-chart-state">
    <div className="empty-state-content">
      <p>No transaction data available</p>
    </div>
  </div>
);
