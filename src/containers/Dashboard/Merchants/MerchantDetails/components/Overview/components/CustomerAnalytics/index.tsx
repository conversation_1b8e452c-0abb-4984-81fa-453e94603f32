import React, { useState } from 'react';

import CurrencyPicker from '+containers/Dashboard/Shared/CurrencyPicker';
import DateFilter from '+containers/Dashboard/Shared/DateFilter';
import { useSearchQuery } from '+hooks';
import { ICustomerAnalytics, IDateRange, Nullable, TabId } from '+types';

import { getTabContent, tabs } from './CustomersAnalyticsHelper';
import useGetMerchantCustomerAnalytics from './hooks/useGetMerchantCustomerAnalytics';

import '../../index.scss';

interface ICustomerAnalyticsExtended extends ICustomerAnalytics {
  durationTabs: Record<string, string | string[] | Nullable<string>[] | null[]>;
}

const CustomerAnalytics = ({
  duration,
  activeCurrency,
  setActiveCurrency,
  availableCurrencies,
  durationTabs
}: ICustomerAnalyticsExtended) => {
  const searchQuery = useSearchQuery();
  const [activeTab, setActiveTab] = useState<TabId>('pay-in');
  const [dateRange, setDateRange] = useState<IDateRange>({
    startDate: 'all_time',
    endDate: 'all_time'
  });
  const [isDateCleared, setIsDateCleared] = useState<boolean>(false);
  const customDateOptions = ['All Time', 'Today', 'Last 5 days', 'Last 7 days', 'Last 30 days', 'Last 90 days'] as const;

  const getTransactionType = (tabId: TabId): 'payins' | 'payouts' => {
    switch (tabId) {
      case 'pay-in':
        return 'payins';
      case 'payouts':
        return 'payouts';
      default:
        return 'payins';
    }
  };

  const handleTabClick = (tabId: TabId) => {
    setActiveTab(tabId);
    searchQuery.setQuery({ subTab: tabId }, true);
  };

  const { data, isLoading, id } = useGetMerchantCustomerAnalytics(getTransactionType(activeTab), activeCurrency, dateRange);
  const customer = data?.data?.data;

  const currentDurationRange = durationTabs[duration as keyof typeof durationTabs];

  return (
    <div className="content-details-box">
      <div className="content-details-body trasaction-details-body">
        <div className="element-wrapper element-info">
          <div className="element-actions">
            <form className="merchant-details-g-menu form-inline transaction-details-g-menu">
              <div className="filter-container" style={{ display: 'flex', gap: '20px', alignItems: 'center', paddingBottom: '30px' }}>
                <DateFilter
                  selectedDate={dateRange}
                  onDateChange={date => {
                    setDateRange(date);
                    setIsDateCleared(false);
                  }}
                  isCleared={isDateCleared}
                  hideCalendarIcon={true}
                  customDateOptions={customDateOptions}
                  className="reduced-height-datefilter"
                />

                <CurrencyPicker
                  options={availableCurrencies}
                  onChange={value => setActiveCurrency(value)}
                  activeCurrency={activeCurrency?.toUpperCase()}
                  label={null}
                />
              </div>
            </form>
          </div>
          <span className="transaction-overview-header">
            <h6 className="">Customer Insights</h6>
            <span className="overview-divider-customer" />
          </span>
        </div>

        <div className="customer-analytics-container">
          <div className="tabs-navigation">
            <nav className="tabs-list">
              <h6>Customers</h6>
              {tabs.map(tab => (
                <button
                  key={tab?.id}
                  className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => handleTabClick(tab?.id)}
                  aria-selected={activeTab === tab?.id}
                  role="tab"
                  aria-controls={`panel-${tab?.id}`}
                >
                  {tab?.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="content-area">
            {getTabContent(activeTab, {
              duration,
              durationRange: currentDurationRange,
              activeCurrency,
              customer,
              isLoading,
              id,
              tabId: activeTab as TabId
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerAnalytics;
