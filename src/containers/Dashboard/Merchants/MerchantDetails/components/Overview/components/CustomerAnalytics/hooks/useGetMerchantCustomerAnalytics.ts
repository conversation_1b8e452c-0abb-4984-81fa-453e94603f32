import { useFeedbackHand<PERSON>, useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import { CustomerAnalytics } from '+types';
import { filteredOutObjectProperty, formatAllTime, getPresentDate, logError, queriesParams } from '+utils';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

const api = new APIRequest();

const useGetMerchantCustomerAnalytics = (
  transactionType: 'payins' | 'payouts',
  activeCurrencyTab: string,
  dateRange?: { startDate: string; endDate: string }
): {
  isLoading: boolean;
  data: CustomerAnalytics;
  activeCurrencyTab: string;
  id: string;
} => {
  const { id } = useParams<{ id: string }>();
  const searchQuery = useSearchQuery();
  const dateFrom = dateRange?.startDate || searchQuery.value.dateFrom || getPresentDate().dateFrom;
  const dateTo = dateRange?.endDate || searchQuery.value.dateTo || getPresentDate().dateTo;
  const sortingParams = {
    dateFrom: formatAllTime(dateFrom),
    dateTo: formatAllTime(dateTo),
    ...filteredOutObjectProperty(searchQuery.value, [
      queriesParams.activeCurrency,
      queriesParams.subTab,
      queriesParams.tab,
      queriesParams.dateFrom,
      queriesParams.dateTo,
    ])
  };
  const { feedbackInit } = useFeedbackHandler();

  const { data, isLoading } = useQuery(
    [`MERCHANT_CUSTOMER_${transactionType}_ANALYTICS_${id}`, transactionType, activeCurrencyTab, {
      ...filteredOutObjectProperty(searchQuery.value, [
        queriesParams.activeCurrency
      ]),
      dateFrom: dateRange?.startDate,
      dateTo: dateRange?.endDate
    }],
    () => api.getMerchantCustomerAnalytics(transactionType, id, activeCurrencyTab, sortingParams),
    {
      onError: e => {
        logError(e);
        feedbackInit({
          message: `There has been an error fetching Customer Analytics for merchant: ${id}.`,
          type: 'danger'
        });
      }
    }
  );

  return {
    isLoading,
    data: data ?? { data: { count: 0 } },
    activeCurrencyTab,
    id,
  };
}

export default useGetMerchantCustomerAnalytics;

