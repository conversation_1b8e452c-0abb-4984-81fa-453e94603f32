import React, { useState } from 'react';
import * as Flags from 'country-flag-icons/react/3x2';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

import Icon from '+containers/Dashboard/Shared/Icons';
import RiskLevel from '+containers/Dashboard/Shared/RiskLevel';
import TierIcon from '+containers/Dashboard/Shared/TierIcon';
import ToolTip from '+containers/Dashboard/Shared/Tooltip';
import { useSearchQuery } from '+hooks';
import useFeedbackHandler from '+hooks/useFeedbackHandler';
import useQueryStorage from '+hooks/useQueryStorage';
import APIRequest from '+services/api-services';
import { CurrencyType, Nullable } from '+types';
import { durationMonth, durationToday, durationWeek, storageDataKey } from '+utils';

import CustomerAnalytics from './components/CustomerAnalytics';
import TransactionChart from './components/TransactionAnalytics';

import '../index.scss';

const api = new APIRequest();

interface SentinalData {
  classification?: string;
  identifier?: string;
}

type TabsT = 'today' | 'this_week' | 'this_month' | 'all_time';

export default function GeneralTab({ sentinalData }: { sentinalData: SentinalData }) {
  const { id } = useParams<{ id: string }>();
  const { feedbackInit } = useFeedbackHandler();
  const { getQueryData } = useQueryStorage();
  const searchQuery = useSearchQuery();

  const availableCurrencies = getQueryData(storageDataKey.AVAILABLE_CURRENCIES);

  const [transactionDuration, setTransactionDuration] = useState<string>(searchQuery.value.transaction_duration ?? 'all_time');
  const [transactionCurrency, setTransactionCurrency] = useState<string>(searchQuery.value.transaction_currency ?? 'NGN');

  const [customerDuration, setCustomerDuration] = useState<string>(searchQuery.value.customer_duration ?? 'all_time');
  const [customerCurrency, setCustomerCurrency] = useState<string>(searchQuery.value.customer_currency ?? 'NGN');

  const durationTabs: Record<TabsT, string | string[] | Nullable<string>[] | null[]> = {
    today: durationToday(),
    this_week: durationWeek(),
    this_month: durationMonth(),
    all_time: [null, null]
  };

  const overviewDuration = {
    today: 'Today',
    this_week: 'This Week',
    this_month: 'This Month',
    all_time: 'All Time'
  };

  const errorDispatch = (message: string) =>
    feedbackInit({
      message,
      type: 'danger'
    });

  const { data: merchantKYCInfo } = useQuery(`${id}_MERCHANT_KYC`, () => api.getMerchant(id), {
    keepPreviousData: true,
    onError: () => errorDispatch(`There has been an error fetching merchant kyc data.`)
  });

  const { data: merchantDetails } = useQuery(`MERCHANT_DETAILS_${id}`, () => api.getMerchantKyc(id), {
    onError: () => {
      feedbackInit({
        message: `There has been an error fetching merchant's kyc details`,
        type: 'danger'
      });
    }
  });

  const businessProfile = merchantDetails?.info;
  const businessInfo = merchantDetails?.merchant;
  const businessContact = merchantDetails?.info?.contact?.support_phone;
  const Flag = Flags[merchantKYCInfo?.country?.iso2 as keyof typeof Flags] || (() => null);

  const setTransactionDurationHandler = (newDuration: string) => {
    setTransactionDuration(newDuration);
  };

  const setTransactionCurrencyHandler = (newCurrency: string) => {
    const upperCaseCurrency = newCurrency.toUpperCase();
    setTransactionCurrency(upperCaseCurrency);
  };

  const setCustomerDurationHandler = (newDuration: string) => {
    setCustomerDuration(newDuration);
  };

  const setCustomerCurrencyHandler = (newCurrency: string) => {
    const upperCaseCurrency = newCurrency.toUpperCase();
    setCustomerCurrency(upperCaseCurrency);
  };

  return (
    <div className="nav-content active">
      <div className="element-box business__info">
        <div className="businnes-content-details-body  overview_invoice">
          <div className="detail-box">
            <div className="overview_invoice_body-about">
              <div className="invoice-desc">
                <div className="desc-label">About</div>
                <div className="desc-value">{merchantKYCInfo?.description || 'Not available'}</div>
                <div className="value-pair overview_tier">
                  {merchantKYCInfo?.risk_level && (
                    <RiskLevel riskLevel={merchantKYCInfo?.risk_level} titleColor="#3b618e" hideDivider={true} />
                  )}
                  <span className="overview-divider" />
                  <div className="value-pair align-items-center tier-value-pair mr-0 d-flex ">
                    {merchantKYCInfo?.tier_level?.name?.toLowerCase() === 'none' ||
                      (merchantKYCInfo?.tier_level?.name && (
                        <TierIcon
                          id={Number(merchantKYCInfo?.tier_level?.name?.slice?.(-1))}
                          roundedBorderColor="#1C3B6F"
                          variant="yellow"
                        />
                      ))}
                    <div className="d-flex align-items-center">
                      <p className="my-0 ml-2">{merchantKYCInfo?.tier_level?.name}</p>
                      {merchantKYCInfo?.tier_level?.description && (
                        <ToolTip type="tier info" message={<p className="tier-tooltip-text">{merchantKYCInfo?.tier_level?.description}</p>}>
                          <Icon name="infoQuestionMark" width={11} height={11} className="tier-tooltip-text-questionmark" />
                        </ToolTip>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="vertical-divider"></div>
            <div className="overview_invoice_body">
              <div className="invoice-desc-overview">
                <div className="desc-label">Business Type</div>
                <div className="desc-value">{businessProfile?.business_type || 'Not available'}</div>
              </div>
              <div className="vertical-divider"></div>

              <div className="invoice-desc-overview">
                <div className="desc-label">Business Website</div>
                <div className="desc-value">{businessProfile?.business_profile?.website || 'Not available'}</div>
              </div>
              <div className="vertical-divider"></div>

              <div className="invoice-desc-overview">
                <div className="desc-label">Country</div>
                <span className="desc-value-country">
                  {merchantKYCInfo?.country?.iso2 && Flag && (
                    <span>
                      <Flag />
                    </span>
                  )}
                  {merchantKYCInfo?.country?.name}
                </span>
              </div>
            </div>

            <div className="vertical-divider"></div>
            <div className="overview_invoice_body">
              <div className="invoice-desc-overview">
                <div className="desc-label">Email</div>
                <div className="desc-value">{businessInfo?.email || 'Not available'}</div>
              </div>

              <div className="invoice-desc-overview">
                <div className="desc-label">Phone Number</div>
                <div className="desc-value">{businessContact || 'Not available'}</div>
              </div>
            </div>
          </div>
        </div>

        <TransactionChart
          duration={transactionDuration}
          setDuration={setTransactionDurationHandler}
          activeCurrency={transactionCurrency}
          setActiveCurrency={setTransactionCurrencyHandler}
          availableCurrencies={availableCurrencies as CurrencyType[]}
          overviewDuration={overviewDuration}
          durationTabs={durationTabs}
        />

        <CustomerAnalytics
          duration={customerDuration}
          setDuration={setCustomerDurationHandler}
          activeCurrency={customerCurrency}
          setActiveCurrency={setCustomerCurrencyHandler}
          availableCurrencies={availableCurrencies as CurrencyType[]}
          overviewDuration={overviewDuration}
          durationTabs={durationTabs}
        />
      </div>
    </div>
  );
}
