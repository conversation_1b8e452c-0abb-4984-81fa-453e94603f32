import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';

import { mockedBankTrxPaymentPreferenceList } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { TCurrentPaymnentPreferenceSettings, TPaymentPreferenceRequest } from '+types';

import PaymentPreferenceTabs from '..';

expect.extend(toHaveNoViolations);

const prefCurrent = {
  overpayment: {
    action: 'return_all'
  },
  underpayment: {
    action: 'return_excess'
  },
  last_updated_at: '2024-08-20T10:30:42.000Z'
};

const mockData = mockedBankTrxPaymentPreferenceList.data.data as TPaymentPreferenceRequest[];

const MockPaymentPrefernceTabs = ({
  currentPref = prefCurrent,
  loading = false,
  data = mockData
}: {
  currentPref?: TCurrentPaymnentPreferenceSettings;
  loading?: boolean;
  data?: TPaymentPreferenceRequest[];
}) => {
  return (
    <MockIndex>
      <PaymentPreferenceTabs currentPref={currentPref} loading={loading} data={data} />
    </MockIndex>
  );
};

describe('Test for PaymentPreferenceTabs', () => {
  describe('Basic functionality', () => {
    it('should render PaymentPreferenceTabs', async () => {
      render(<MockPaymentPrefernceTabs />);

      expect(screen.getByTestId('Current Preference')).toBeInTheDocument();
      expect(screen.getByTestId('Preference Request')).toBeInTheDocument();
      expect(screen.getByTestId('Preference Request History')).toBeInTheDocument();
    });

    it('should be accessible', async () => {
      const { container } = render(<MockPaymentPrefernceTabs />);

      await waitFor(async () => {
        const result = await axe(container);
        expect(result).toHaveNoViolations();
      });
    });

    it('Should render the Pending alert banner on render if the first item in the mockData array has a status of pending', async () => {
      render(<MockPaymentPrefernceTabs />);

      const banner = await screen.findByTestId('payment-preference-alert');
      expect(banner).toBeInTheDocument();
    });

    it('Should not render the Pending alert banner on render if the first item in the mockData array has a status of approved', async () => {
      render(<MockPaymentPrefernceTabs data={[...mockData].reverse()} />);

      const banner = await screen.queryByTestId('payment-preference-alert');
      expect(banner).not.toBeInTheDocument();
    });

    it('Should render the Preference Request component if there is a pending Preference request', async () => {
      render(<MockPaymentPrefernceTabs />);

      expect(await screen.findByTestId('preference-request')).toBeInTheDocument();
      expect(screen.queryByTestId('current-preference')).not.toBeInTheDocument();
    });

    it('Should not render the Pending alert banner on render if the first item in the mockData array has a status of approved', async () => {
      render(<MockPaymentPrefernceTabs data={[...mockData].reverse()} />);

      expect(await screen.findByTestId('current-preference')).toBeInTheDocument();
      expect(screen.queryByTestId('preference-request')).not.toBeInTheDocument();
    });
  });
});
