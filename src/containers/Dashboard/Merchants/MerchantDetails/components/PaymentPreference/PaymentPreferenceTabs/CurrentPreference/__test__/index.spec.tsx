import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import CurrentPreference from '..';

expect.extend(toHaveNoViolations);

const prop = { overpayment: { action: 'return_excess' }, underpayment: { action: 'return_all' } };

const MockCurrentPreference = ({ pref = prop }: { pref?: typeof prop }) => {
  return (
    <MockIndexWithRoute route="/:id" initialEntries={['/1']}>
      <CurrentPreference pref={pref} />
    </MockIndexWithRoute>
  );
};

describe('Test for CurrentPreference', () => {
  describe('Basic functionality', () => {
    it('should be accessible', async () => {
      const { container } = render(<MockCurrentPreference />);

      await waitFor(async () => {
        const result = await axe(container);
        expect(result).toHaveNoViolations();
      });
    });
  });

  describe('Overpayment UI functionality test', () => {
    it('Should render the overpayment element and the sub-text element', async () => {
      render(<MockCurrentPreference />);

      const overpaymentsElement = await screen.findByTestId('overpayment');
      const overpaymentsElementSubtext = await screen.findByTestId('overpayment-subtext');

      expect(overpaymentsElement).toBeInTheDocument();
      expect(overpaymentsElementSubtext).toBeInTheDocument();
    });

    it('Overpayment info modal should open when info button is clicked and should also close when close is clicked', async () => {
      render(<MockCurrentPreference />);

      const overpaymentInfoBtn = await screen.findByTestId('overpayment-info-btn');
      userEvent.click(overpaymentInfoBtn);

      expect(await screen.findByTestId('overpayment-info-modal')).toBeInTheDocument();

      const closeBtn = await screen.findByTestId('first-button');
      userEvent.click(closeBtn);

      await waitFor(() => {
        expect(screen.queryByTestId('overpayment-info-modal')).not.toBeInTheDocument();
      });
    });

    it('Overpayment setting should be "return excess"', async () => {
      render(<MockCurrentPreference />);

      expect(await screen.findByTestId('overpayment-setting')).toHaveTextContent(/return excess/i);
    });
  });

  describe('Underpayment UI functionality test', () => {
    it('Should render the underpayment element and the sub-text element', async () => {
      render(<MockCurrentPreference />);

      const underpaymentsElement = await screen.findByTestId('underpayment');
      const underpaymentsElementSubtext = await screen.findByTestId('underpayment-subtext');

      expect(underpaymentsElement).toBeInTheDocument();
      expect(underpaymentsElementSubtext).toBeInTheDocument();
    });

    it('Underpayment info modal should open when info button is clicked and should also close when close is clicked', async () => {
      render(<MockCurrentPreference />);

      const underpaymentInfoBtn = await screen.findByTestId('underpayment-info-btn');
      userEvent.click(underpaymentInfoBtn);

      expect(await screen.findByTestId('underpayment-info-modal')).toBeInTheDocument();

      const closeBtn = await screen.findByTestId('first-button');
      userEvent.click(closeBtn);

      await waitFor(() => {
        expect(screen.queryByTestId('underpayment-info-modal')).not.toBeInTheDocument();
      });
    });

    it('Underpayment setting should be "return excess"', async () => {
      render(<MockCurrentPreference />);

      expect(await screen.findByTestId('underpayment-setting')).toHaveTextContent(/return all/i);
    });
  });
});
