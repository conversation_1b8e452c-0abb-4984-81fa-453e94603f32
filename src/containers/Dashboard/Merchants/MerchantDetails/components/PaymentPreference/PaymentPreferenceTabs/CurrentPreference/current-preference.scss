@import '../../../../../../../../styles/kpy-custom/variables';

.current-preference {
  background: #f9fbfd;
  border-radius: 10px;
  padding: 2rem;

  .grey-section__subsection-item:last-child {
    margin-top: 2rem;
  }

  h6 {
    display: flex;
    align-items: center;
  }

  h6 > button {
    border: none;
    background: none;
    cursor: pointer;
    padding: 0;
    margin-top: -3px;
  }

  h6 > button > img {
    width: 1rem;
  }

  &__modal-content article > p {
    color: rgba(62, 75, 91, 0.7);
  }

  &__last-updated {
    display: flex;
    gap: 0.5rem;
    color: #a9afbc;
  }

  &__pref {
    min-width: 250px;
  }

  @media (min-width: $breakpoint-desktop-sm) {
    &__heading-sub {
      flex-direction: row;
      align-items: start;
      justify-content: space-between;
      gap: 1.5rem;
      margin-bottom: 0;
    }
  }

  @media (min-width: $breakpoint-desktop) {
    &__heading {
      width: 60%;
    }
  }
}
