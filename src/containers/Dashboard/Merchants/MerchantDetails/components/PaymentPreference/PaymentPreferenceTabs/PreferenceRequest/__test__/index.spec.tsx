import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import React from 'react';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import { mockedBankTrxPaymentPreferenceList } from '+mock/mockData';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import PreferenceRequest from '..';

const prop = { overpayment: { action: 'return_all' }, underpayment: { action: 'return_excess' } };
const req = mockedBankTrxPaymentPreferenceList.data.data[0];
vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));
const MockPreferenceRequest = ({
  currentPref = prop,
  pendingRequest = req
}: {
  currentPref?: typeof prop;
  pendingRequest?: typeof req;
}) => {
  return (
    <MockIndexWithRoute route="/:id" initialEntries={['/1']}>
      <PreferenceRequest currentPref={currentPref} pendingRequest={pendingRequest} />
    </MockIndexWithRoute>
  );
};

describe('Test for PreferenceRequest', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  describe('Basic functionality', () => {
    it('should be accessible', async () => {
      const { container } = render(<MockPreferenceRequest />);

      await waitFor(async () => {
        const result = await axe(container);
        expect(result).toHaveNoViolations();
      });
    });
  });

  describe('Preference Request UI functionality test', () => {
    it('Should render the Preference Request element and the subtitle element', async () => {
      render(<MockPreferenceRequest />);

      const title = await screen.findByTestId('preference-request-title');
      const subtitle = await screen.findByTestId('preference-request-subtitle');

      expect(title).toBeInTheDocument();
      expect(subtitle).toBeInTheDocument();
    });

    it('Should render the empty state if there is no pending Preference Request', async () => {
      render(<MockPreferenceRequest pendingRequest={{} as typeof req} />);

      const title = await screen.findByText(/No Preference request/i);
      const subtitle = await screen.findByText(/Preference request from this merchant will appear here/i);

      expect(title).toBeInTheDocument();
      expect(subtitle).toBeInTheDocument();
    });

    describe('Approve pending request flow', () => {
      it('Should open and close approve pending request modal', async () => {
        mockUseSetUserAccess.mockReturnValue({ 'bank_transfer_settings_requests.process': true });
        render(<MockPreferenceRequest />);

        const approve = await screen.findByTestId('approve');
        await userEvent.click(approve);

        await waitFor(() => {
          expect(screen.getByTestId('approve-preference-request-modal')).toBeInTheDocument();
        });

        await userEvent.click(screen.getByTestId('first-button'));

        await waitFor(() => {
          expect(screen.queryByTestId('approve-preference-request-modal')).not.toBeInTheDocument();
        });
        await waitFor(() => {
          expect(screen.queryByTestId('success-modal')).not.toBeInTheDocument();
        });
      });

      it('Should approve pending request modal', async () => {
        render(<MockPreferenceRequest />);

        const approve = await screen.findByTestId('approve');
        await userEvent.click(approve);

        await waitFor(() => {
          expect(screen.getByTestId('approve-preference-request-modal')).toBeInTheDocument();
        });

        await userEvent.click(screen.getByTestId('second-button'));

        await waitFor(() => {
          expect(screen.getByTestId('success-modal')).toBeInTheDocument();
        });

        await userEvent.click(screen.getByTestId('dismiss-btn'));

        await waitFor(() => {
          expect(screen.queryByTestId('success-modal')).not.toBeInTheDocument();
        });
      });
    });

    describe('Decline pending request flow', () => {
      it('Should open and close decline pending request modal', async () => {
        render(<MockPreferenceRequest />);

        const decline = await screen.findByTestId('decline');
        await userEvent.click(decline);

        await waitFor(() => {
          expect(screen.getByTestId('decline-preference-request-modal')).toBeInTheDocument();
        });

        await userEvent.click(screen.getByTestId('first-button'));

        await waitFor(() => {
          expect(screen.queryByTestId('decline-preference-request-modal')).not.toBeInTheDocument();
        });
        await waitFor(() => {
          expect(screen.queryByTestId('success-modal')).not.toBeInTheDocument();
        });
      });

      it('Should decline not activate the continue button in the decline modal if the input text field is less than 10', async () => {
        render(<MockPreferenceRequest />);

        const decline = await screen.findByTestId('decline');
        await userEvent.click(decline);

        await waitFor(() => {
          expect(screen.getByTestId('decline-preference-request-modal')).toBeInTheDocument();
        });

        expect(screen.getByTestId('second-button')).toBeDisabled();
      });

      it('Should decline pending request modal', async () => {
        render(<MockPreferenceRequest />);

        const decline = await screen.findByTestId('decline');
        await userEvent.click(decline);

        await waitFor(() => {
          expect(screen.getByTestId('decline-preference-request-modal')).toBeInTheDocument();
        });

        await userEvent.type(screen.getByTestId('preference-decline-input-reason'), 'the user is anonymous to us');

        await userEvent.click(screen.getByTestId('second-button'));

        await waitFor(() => {
          expect(screen.getByTestId('success-modal')).toBeInTheDocument();
        });

        await userEvent.click(screen.getByTestId('dismiss-btn'));

        await waitFor(() => {
          expect(screen.queryByTestId('success-modal')).not.toBeInTheDocument();
        });
      });
    });

    describe('Preference request dropdown', () => {
      it('Should have the "requested preference" text as title', async () => {
        render(<MockPreferenceRequest />);

        const title = (await screen.findAllByTestId('preference-dropdown-header'))[0];
        expect(title).toHaveTextContent(/requested preference/i);
      });

      it('Should render the dropdown content when clicked and hides content when clicked again', async () => {
        render(<MockPreferenceRequest />);

        const dropdownBtn = (await screen.findAllByTestId('preference-dropdown-btn'))[0];
        await userEvent.click(dropdownBtn);

        expect(await screen.findByTestId('preference-dropdown-content-wrapper')).toBeInTheDocument();

        await userEvent.click(dropdownBtn);

        await waitFor(() => {
          expect(screen.queryByTestId('preference-dropdown-content-wrapper')).not.toBeInTheDocument();
        });
      });

      it('Dropdown content overpayment and underpayment from and to settings ', async () => {
        render(<MockPreferenceRequest />);

        const dropdownBtn = (await screen.findAllByTestId('preference-dropdown-btn'))[0];
        await userEvent.click(dropdownBtn);

        const dropdownItemTitle = await screen.findAllByTestId('preference-dropdown-item-title');
        const from = await screen.findAllByTestId('from');
        const to = await screen.findAllByTestId('to');

        expect(dropdownItemTitle[0]).toHaveTextContent(/overpayment/i);
        expect(from[0]).toHaveTextContent(/return all/i);
        expect(to[0]).toHaveTextContent(/return excess/i);

        expect(dropdownItemTitle[1]).toHaveTextContent(/underpayment/i);
        expect(from[1]).toHaveTextContent(/return excess/i);
        expect(to[1]).toHaveTextContent(/return all/i);
      });
    });

    describe('Request Information dropdown', () => {
      it('Should have the "preference request" text as title', async () => {
        render(<MockPreferenceRequest />);

        const title = (await screen.findAllByTestId('preference-dropdown-header'))[1];
        expect(title).toHaveTextContent(/request information/i);
      });

      it('Should render the dropdown content when clicked and hides content when clicked again', async () => {
        render(<MockPreferenceRequest />);

        const dropdownBtn = (await screen.findAllByTestId('preference-dropdown-btn'))[1];
        await userEvent.click(dropdownBtn);

        expect(await screen.findByTestId('preference-dropdown-content-wrapper')).toBeInTheDocument();

        await userEvent.click(dropdownBtn);

        await waitFor(() => {
          expect(screen.queryByTestId('preference-dropdown-content-wrapper')).not.toBeInTheDocument();
        });
      });

      it('request information should show request status and date requested', async () => {
        render(<MockPreferenceRequest />);

        const dropdownBtn = (await screen.findAllByTestId('preference-dropdown-btn'))[1];
        await userEvent.click(dropdownBtn);

        const dropdownItemTitle = await screen.findAllByTestId('preference-dropdown-item-title');

        expect(dropdownItemTitle[0]).toHaveTextContent(/request status/i);
        expect(screen.getByTestId('preference-request-status')).toHaveTextContent(/pending/i);

        expect(dropdownItemTitle[1]).toHaveTextContent(/date requested/i);
        expect(screen.getByTestId('preference-request-time')).toHaveTextContent(/20 Aug 2024 12:00 PM/i);
      });
    });

    describe('Letter of attestation dropdown', () => {
      it('Should have the "preference request" text as title', async () => {
        render(<MockPreferenceRequest />);

        const title = (await screen.findAllByTestId('preference-dropdown-header'))[2];
        expect(title).toHaveTextContent(/letter of attestation/i);
      });

      it('Should render the dropdown content when clicked and hides content when clicked again', async () => {
        render(<MockPreferenceRequest />);

        const dropdownBtn = (await screen.findAllByTestId('preference-dropdown-btn'))[2];
        await userEvent.click(dropdownBtn);

        expect(await screen.findByTestId('preference-dropdown-content-wrapper')).toBeInTheDocument();

        await userEvent.click(dropdownBtn);

        await waitFor(() => {
          expect(screen.queryByTestId('preference-dropdown-content-wrapper')).not.toBeInTheDocument();
        });
      });

      it('Render the letter of attestation content', async () => {
        render(<MockPreferenceRequest />);

        const dropdownBtn = (await screen.findAllByTestId('preference-dropdown-btn'))[2];
        await userEvent.click(dropdownBtn);

        const content = await screen.findByTestId('preference-dropdown-content-wrapper');

        expect(content).toHaveTextContent(/Signed Attestation Letter/i);
        expect(content).toHaveTextContent(/download/i);
      });
    });
  });
});
