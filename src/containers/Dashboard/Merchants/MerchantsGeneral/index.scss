@import 'styles/kpy-custom/variables';

.merchants-summary {
  padding-bottom: 25px;
  display: grid;
  grid-gap: 1rem;

  @media (min-width: $breakpoint-tablet) {
    grid-template-columns: repeat(3, 1fr);
  }

  p {
    color: #3e4b5b;
    font-weight: 600;
  }

  div {
    @media (max-width: $breakpoint-tablet) {
      margin-bottom: 1rem;
    }

    p {
      margin-bottom: 0;

      &:nth-of-type(2) {
        font-size: 2.5rem;
        margin-bottom: 0.6rem;
        word-break: break-all;
      }

      &:nth-of-type(3) {
        color: #c2c3c6;
        font-weight: 500;
      }
    }
  }
}

.edit-merchant {
  img {
    width: 30px;
    margin-right: 10px;
  }

  @media (min-width: 1000px) {
    width: 20%;
  }
}
.div-table.--merchant-table,
.div-table.--upload-chargeback-table,
.div-table.--merchant-team-members-table {
  &.--heading {
    div {
      width: 100%;
      &:nth-of-type(1) {
        min-width: 20% !important;
        text-align: left;
      }

      &:nth-of-type(2) {
        min-width: 16%;
        text-align: left;
        margin-right: 0.5rem;
      }

      &:nth-of-type(3) {
        min-width: 14%;
        text-align: left;
      }

      &:nth-of-type(4) {
        min-width: 18%;
        text-align: left;
      }
      &:nth-of-type(5) {
        min-width: 15%;
        text-align: left;
      }

      &:nth-of-type(6) {
        min-width: 16%;
        text-align: right;
      }
    }
    @media (min-width: 1140px) {
      display: flex;
      font-size: 0.71rem;
      padding: 0.3rem 1.1rem;
    }
  }

  &.--heading,
  &.--row {
    @media (min-width: 1140px) {
      div {
        width: 100%;
        &:nth-of-type(1) {
          min-width: 20%;
          text-align: left;
        }

        &:nth-of-type(2) {
          min-width: 16%;
          text-align: left;
          margin-right: 0.5rem;
        }

        &:nth-of-type(3) {
          min-width: 14%;
          text-align: left;
        }

        &:nth-of-type(4) {
          min-width: 20%;
          text-align: left;
        }

        &:nth-of-type(5) {
          min-width: 14%;
          text-align: left;
        }

        &:nth-of-type(6) {
          min-width: 16%;
          text-align: right;
        }
      }
    }
  }

  &.--row {
    background-color: #ffffff;

    &:hover {
      box-shadow: 0 2px 5px rgba(69, 101, 173, 0.1);
      transform: none;
      background-color: rgba(179, 192, 223, 0.1);
    }

    @media (min-width: 1140px) {
      flex-direction: row;
      font-size: 0.9rem;

      div {
        margin: 0;
      }
    }
  }
}
.div-table.--merchant-table {
  &.--heading,
  &.--row {
    div {
      &:nth-of-type(6) {
        padding-right: 1.5rem;
      }
    }
  }
}
.div-table.--merchant-team-members-table {
  &.--heading {
    div {
      &:nth-of-type(1) {
        padding-left: 0;
      }
      &:nth-of-type(2) {
        padding-left: 0.1rem;
      }
      &:nth-of-type(3) {
        padding-left: 1.5rem;
      }
      &:nth-of-type(4) {
        padding-left: 1.8rem;
      }
      &:nth-of-type(5) {
        padding-left: 2rem;
      }
    }
  }
  &.--row {
    div {
      &:nth-of-type(1) span:nth-of-type(2) {
        display: inline-block !important;
        max-width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.tooltip-tab {
  margin-left: -20px;
  margin-top: -28px;
}

.merchant-tier-tooltip-filter-tier {
  position: absolute;

  @media (min-width: 768px) {
    div.text-tooltip--content.merchant-tier-tooltip-filter-content {
      top: 6px;
      margin-left: 10%;
      left: 0;
      width: 100% !important;
    }
  }
}

@media (min-width: 1000px) {
  .div-table.--compliance-table.--heading div:nth-of-type(2).merchant-tier-tooltip-filter-content {
    padding-left: 0.75rem !important;
  }
}

.general-merchant-table-wrapper {
  @media (min-width: 1140px) {
    .div-table.--merchant-table.--heading,
    .div-table.--upload-chargeback-table.--heading {
      padding: 0.1rem;
    }
  }
  .div-table.--merchant-table.--heading.--merchant-team-members-table {
    > div[data-testid='columnheader'] {
      &:last-child {
        padding-right: 1.75rem;
      }
    }
  }
}
.toggle-buttons {
  margin-bottom: 2rem;
}
.merchant-team-member-details-modal {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  margin-top: 2px;
}

.merchant-team-member-details-modal p {
  margin-top: 15px;
}

.merchant-team-member-details-modal:not(:last-child) {
  border-bottom: 1px solid lightgrey;
}

.merchant-team-member-details-modal > p:nth-child(2) {
  font-weight: 500;
}

.merchant-team-member-details-modal > p:first-child {
  color: grey;
}

.merchant-team-member-details-modal:last-child span {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.merchant-team-member-details-modal:last-child span p:first-child {
  font-weight: 500;
  color: black;
}

.merchant-team-member-details-modal:last-child span p.annotation {
  color: grey;
  font-size: 0.9rem;
}
