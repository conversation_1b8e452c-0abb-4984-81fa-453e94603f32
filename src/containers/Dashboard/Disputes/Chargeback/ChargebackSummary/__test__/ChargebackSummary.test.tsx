import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { http, HttpResponse } from 'msw';

import { mockedChargebackData, mockedChargebackDataWithEmpty } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';
import useChargebackStore from '+store/chargebackStore';

import ChargebackSummary from '../ChargebackSummary';

function MockedChargebackSummary() {
  return (
    <MockIndex>
      <ChargebackSummary />
    </MockIndex>
  );
}

describe('ChargebackSummary', () => {
  beforeEach(() => {
    server.use(
      http.post('/admin/chargebacks/bulk', () => {
        return HttpResponse.json(
          {
            status: true,
            message: 'Chargeback list uploaded successfully',
            data: {
              batch_reference: 'KPY-CHG-BTCH-kDXRb39hor2Bdf7',
              errorMessages: [
                {
                  message: 'Payment Source Record not found for these processor references',
                  references: ['KP_12335689', 'KP_123456789']
                }
              ]
            }
          },
          { status: 200 }
        );
      })
    );
  });

  it('should render successfully', () => {
    useChargebackStore.setState({
      chargebackData: mockedChargebackData,
      paginatedData: {
        data: mockedChargebackData.chargebacks,
        paging: {
          total_items: 4,
          page_size: 10,
          current: 1,
          count: 4,
          next: 2
        }
      }
    });
    render(<MockedChargebackSummary />);

    expect(screen.getByText('Chargeback upload Summary')).toBeInTheDocument();
    expect(screen.getByText('4 Chargeback(s)')).toBeInTheDocument();
    expect(screen.getByText('no value gotten')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit Entries' })).toBeInTheDocument();
    expect(screen.getByText('Add chargeback')).toBeInTheDocument();
    expect(screen.getAllByText('Provider reference:')).toHaveLength(4);
    expect(screen.getAllByText('Chargeback Amount:')).toHaveLength(4);
    expect(screen.getAllByText('Chargeback reason:')).toHaveLength(4);
    expect(screen.getByRole('button', { name: 'Submit Entries' })).toBeEnabled();
  });

  it('should render uploaded csv data', async () => {
    useChargebackStore.setState({
      chargebackData: mockedChargebackData,
      paginatedData: {
        data: mockedChargebackData.chargebacks,
        paging: {
          total_items: 4,
          page_size: 10,
          current: 1,
          count: 4,
          next: 2
        }
      }
    });
    render(<MockedChargebackSummary />);
    expect(screen.getByText('2,000.00')).toBeInTheDocument();
    expect(screen.getByText('below value')).toBeInTheDocument();
    expect(screen.getByText('KP_123456789')).toBeInTheDocument();
    expect(screen.getByText('40,000.00')).toBeInTheDocument();
    expect(screen.getByText('no value gotten')).toBeInTheDocument();
    expect(screen.getByText('KP_12335689')).toBeInTheDocument();
    expect(screen.getByText('50,000.00')).toBeInTheDocument();
    expect(screen.getByText('KP_165335689')).toBeInTheDocument();
    expect(screen.getByText('got some of the value')).toBeInTheDocument();
    expect(screen.getByText('10,987,409.00')).toBeInTheDocument();
    expect(screen.getByText('KP_565335689')).toBeInTheDocument();
    expect(screen.getByText('transaction doesnt exist')).toBeInTheDocument();
  });

  it('should display warning banner for csv with empty column', () => {
    render(<MockedChargebackSummary />);

    act(() => {
      useChargebackStore.setState({
        chargebackData: mockedChargebackDataWithEmpty,
        paginatedData: {
          data: mockedChargebackDataWithEmpty.chargebacks,
          paging: {
            total_items: 4,
            page_size: 10,
            current: 1,
            count: 4,
            next: 2
          }
        }
      });
    });
    expect(
      screen.getByText('Some errors were detected in your uploaded file and have been highlighted. Update them to upload your entries')
    ).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit Entries' })).toBeDisabled();
  });

  it('should display success banner for csv upload', async () => {
    render(<MockedChargebackSummary />);

    act(() => {
      useChargebackStore.setState({
        chargebackData: mockedChargebackData,
        paginatedData: {
          data: mockedChargebackData.chargebacks,
          paging: {
            total_items: 4,
            page_size: 10,
            current: 1,
            count: 4,
            next: 2
          }
        }
      });
    });
    const submitButton = screen.getByRole('button', { name: 'Submit Entries' });
    expect(submitButton).toBeEnabled();
    userEvent.click(submitButton);
    await waitFor(() => expect(screen.getByText(/These chargebacks were created successfully./i)).toBeInTheDocument());
  });
});

describe('ChargebackSummary error test', () => {
  beforeEach(() => {
    server.use(
      http.post('/admin/chargebacks/bulk', () => {
        return HttpResponse.json(
          {
            status: true,
            message: 'Chargeback list uploaded successfully',
            data: {
              batch_reference: 'KPY-CHG-BTCH-kDXRb39hor2Bdf7',
              errorMessages: [
                {
                  message: 'Payment Source Record not found for these processor references',
                  references: ['KP_12335689', 'KP_123456789', 'KP_165335689', 'KP_565335689']
                }
              ]
            }
          },
          { status: 200 }
        );
      })
    );
  });

  it('should display error banner after CSV data is uploaded and display modal with the references', async () => {
    useChargebackStore.setState({
      chargebackData: mockedChargebackData,
      paginatedData: {
        data: mockedChargebackData.chargebacks,
        paging: {
          total_items: 4,
          page_size: 10,
          current: 1,
          count: 4,
          next: 2
        }
      }
    });

    render(<MockedChargebackSummary />);
    const submitButton = screen.getByText(/Submit Entries/i);

    expect(submitButton).toBeEnabled();
    userEvent.click(submitButton);
    await waitFor(() =>
      expect(
        screen.getByText(
          /You are unable to proceed because you need to make some modifications on the csv file, please make the edits and re-upload the file to continue/i
        )
      ).toBeInTheDocument()
    );
    await waitFor(() => expect(screen.getByText(/view error/i)).toBeInTheDocument());
    userEvent.click(screen.getByText(/view error/i));
    await waitFor(() => expect(screen.getByText(/Backend Errors/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Payment Source Record not found for these processor references/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/KP_565335689/i)).toBeInTheDocument());
  });

  it('should display error banner after CSV data is uploaded', async () => {
    render(<MockedChargebackSummary />);
    const submitButton = screen.getByRole('button', { name: 'Submit Entries' });
    expect(submitButton).toBeDisabled();
    useChargebackStore.setState({
      chargebackData: mockedChargebackData,
      paginatedData: {
        data: mockedChargebackData.chargebacks,
        paging: {
          total_items: 4,
          page_size: 10,
          current: 1,
          count: 4,
          next: 2
        }
      }
    });
    await waitFor(() => expect(submitButton).toBeEnabled());
    userEvent.click(submitButton);
    await waitFor(() =>
      expect(
        screen.getByText(
          /You are unable to proceed because you need to make some modifications on the csv file, please make the edits and re-upload the file to continue/i
        )
      ).toBeInTheDocument()
    );
    await waitFor(() => expect(screen.getByText(/view error/i)).toBeInTheDocument());
  });
});
