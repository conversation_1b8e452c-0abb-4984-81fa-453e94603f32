.div-table {
  display: flex;

  &.--heading {
    font-size: 0.81rem;
    font-weight: 500;
    color: rgba(90, 99, 126, 0.49);
    text-transform: uppercase;
    display: none;
    padding-bottom: 0 !important;

    @media (min-width: $breakpoint-desktop) {
      display: flex;
      font-size: 0.71rem;

      div {
        font-weight: 400;
        padding: 0.75rem;
      }
    }
  }

  &.--row {
    font-size: 0.87rem;
    flex-direction: column;
    word-wrap: break-word;
    background-color: white;
    cursor: pointer;

    .body-row-header {
      font-weight: 600;
      margin-right: 10px;
      min-width: fit-content;

      @media (min-width: $breakpoint-desktop) {
        display: none;
      }
    }

    div {
      word-break: break-all;
      padding: 1rem 1rem 0;

      @media (max-width: $breakpoint-desktop) {
        &:last-of-type {
          padding-bottom: 1rem;
        }
      }

      @media (min-width: $breakpoint-desktop) {
        padding: 0.75rem;
      }
    }

    @media (min-width: $breakpoint-desktop) {
      flex-direction: row;

      div {
        span:not(:first-of-type) {
          display: inline-block;
          max-width: 160px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &.no-ellipsis {
            overflow: visible;
            text-overflow: initial;
          }
        }
      }
    }

    &:hover {
      transform: scale(1.009, 1.009);
      box-shadow: 0 1px 12px 0 rgba(113, 113, 113, 0.05);
    }
  }

  .annotation {
    font-size: 0.8rem;
    color: rgba(74, 74, 74, 0.5);
    margin-left: 5px;
  }

  .trxn-id {
    color: #007bff;
    font-weight: 500;
    text-transform: uppercase;
  }

  .text-tooltip-w {
    display: none;
    padding: 0 0.75rem !important;

    @media (min-width: 1140px) {
      display: inline-block;
      margin-left: auto !important;

      #refunds_and_cashbacks-info {
        left: 2rem;
        width: 180px;
      }
    }
  }
}

.div-table.issuing-merchants {
  &.--heading div,
  & .--column {
    flex-basis: 0;

    @media (min-width: 1000px) {
      &:nth-of-type(1) {
        img {
          width: 30px;
          margin-right: 10px;
        }

        flex-grow: 1;
      }

      &:nth-of-type(2) {
        .value {
          max-width: 100%;
        }

        flex-grow: 1;
      }

      &:nth-of-type(3) {
        flex-grow: 1;
      }

      &:nth-of-type(4) {
        text-align: right;
        justify-content: end;
        flex-grow: 1;
      }
    }
  }
}

.div-table.issued-cards {
  img {
    width: 30px;
    margin-right: 6px;
  }

  &.--heading,
  &.--row {
    div {
      flex-basis: 0;

      @media (min-width: 1000px) {
        &,
        & > span + span {
          min-width: unset;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &:nth-of-type(1) {
          min-width: 15ch;
          flex-grow: 1;
        }

        &:nth-of-type(2) {
          flex-grow: 2;
        }

        &:nth-of-type(3) {
          flex-grow: 2;
        }

        &:nth-of-type(4) {
          flex-grow: 2;
          text-align: start !important;
          justify-content: start;
        }

        &:last-of-type {
          flex-grow: 1;
          justify-content: end;
          text-align: end !important;
        }
      }
    }
  }
}

.div-table.merchant-reserved-cards {
  img {
    width: 30px;
    margin-right: 6px;
  }

  &.--heading,
  &.--row {
    div {
      flex-basis: 0;

      @media (min-width: 1000px) {
        &,
        & > span + span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &:nth-of-type(1) {
          min-width: 15ch;
          flex-grow: 2;
        }

        &:nth-of-type(2) {
          flex-grow: 2;
        }

        &:nth-of-type(3) {
          flex-grow: 1;
          text-align: start !important;
        }

        &:nth-of-type(4) {
          flex-grow: 1;
          text-align: start !important;
        }

        &:last-of-type {
          min-width: 15ch !important;
          flex-grow: 1;
          text-align: end;

          & .card-container {
            justify-content: end;
          }
        }
      }
    }
  }
}

.div-table.cards-transactions {
  &.--heading,
  &.--row {
    div {
      flex-basis: 0;

      @media (min-width: 1000px) {
        &,
        & > span + span {
          min-width: unset !important;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &:nth-of-type(1) {
          min-width: 15ch;
          flex-grow: 1.25;
        }

        &:nth-of-type(2) {
          flex-grow: 2;
        }

        &:nth-of-type(3) {
          flex-grow: 1;
        }

        &:nth-of-type(4) {
          flex-grow: 2;
          text-align: start !important;
        }

        &:nth-of-type(5) {
          flex-grow: 2;
          text-align: start !important;
        }

        &:last-of-type {
          flex-grow: 1;
          text-align: end;
          justify-content: end;
        }
      }
    }
  }
}

.div-table.issuance-chargebacks {
  &.--heading,
  &.--row {
    div {
      flex-basis: 0;

      @media (min-width: 1000px) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:nth-of-type(1) {
          min-width: 15ch;
          flex-grow: 2;
        }

        &:nth-of-type(2) {
          flex-grow: 2;
        }

        &:nth-of-type(3) {
          flex-grow: 2;
        }

        &:nth-of-type(4) {
          flex-grow: 2;
          text-align: start !important;
        }

        &:nth-of-type(5) {
          flex-grow: 2;
          justify-content: start;
        }

        &:last-of-type {
          flex-grow: 2;
          text-align: end !important;
          justify-content: end !important;
        }
      }
    }
  }
}

.div-table.balance-funding {
  &.--heading,
  &.--row {
    @media (min-width: 1140px) {
      div {
        &:nth-of-type(1) {
          width: 20%;
        }

        &:nth-of-type(2) {
          width: 25%;
        }

        &:nth-of-type(3) {
          width: 25%;
        }

        &:nth-of-type(4) {
          width: 15%;
        }

        &:nth-of-type(5) {
          width: 15%;
          text-align: right;
        }
      }
    }
  }
}

.div-table.balance-history {
  &.--heading div,
  & .--column {
    flex-basis: 0;

    @media (min-width: 1000px) {
      word-break: break-word;

      &:nth-of-type(1) {
        flex-grow: 2 !important;
      }

      &:nth-of-type(2) {
        flex-grow: 4 !important;
      }

      &:nth-of-type(3) {
        justify-content: start;
        min-width: unset !important;
        flex-grow: 2;
      }

      &:last-of-type {
        text-align: end !important;
        justify-content: end;
        flex-grow: 1;
      }
    }
  }

  &.--row {
    div {
      span:not(:first-of-type) {
        display: unset;
      }
    }
  }
}

.div-table.--billing-history {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          width: 16%;
        }

        &:nth-of-type(2) {
          width: 18%;
        }

        &:nth-of-type(3) {
          width: 18%;
        }

        &:nth-of-type(4) {
          width: 16%;
        }

        &:nth-of-type(5) {
          width: 16%;
        }

        &:nth-of-type(6) {
          width: 16%;
        }
      }
    }
  }
}

.div-table.chargeback {
  &.--heading div,
  & .--column {
    @media (min-width: 1140px) {
      padding-right: 10px;

      &:nth-of-type(1) {
        width: 10%;
        min-width: 90px;
      }

      &:nth-of-type(2) {
        .value {
          max-width: 100%;
        }

        width: 30%;
      }

      &:nth-of-type(3) {
        width: 10%;
      }

      &:nth-of-type(4) {
        width: 20%;
      }

      &:nth-of-type(5) {
        width: 10%;
        text-align: center;
        min-width: 10%;

        span {
          width: 100%;
        }
      }

      &:nth-of-type(6) {
        font-weight: 400;
        width: 20%;

        span:not(:first-of-type) {
          display: inline;
        }
      }
    }
  }
}

.div-table.events {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        flex-basis: 0;

        &:nth-of-type(2) {
          flex-grow: 1;
        }

        &:nth-of-type(2) {
          text-align: left;
          flex-grow: 3;
        }

        &:nth-of-type(3) {
          text-align: left;
          flex-grow: 2;
        }

        &:last-of-type {
          text-align: left;
          flex-grow: 1;
          // max-width: 15ch;
          // overflow-wrap: break-word;
        }
      }
    }
  }
}

.div-table.--card-detail-trxn {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        flex-basis: 0;

        & > span + span {
          max-width: unset !important;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &:nth-of-type(1) {
          flex-grow: 1;
        }

        &:nth-of-type(2) {
          flex-grow: 2;
        }

        &:nth-of-type(3) {
          flex-grow: 1;
        }

        &:nth-of-type(4) {
          flex-grow: 1;
        }

        &:last-of-type {
          text-align: right;
          justify-content: end;
          flex-grow: 1;
        }
      }
    }
  }
}

.div-table.--merchant-table {
  &.--heading div,
  & .--column {
    @media (min-width: 1140px) {
      &:nth-of-type(1) {
        width: 20%;
      }

      &:nth-of-type(2) {
        width: 16%;
      }

      &:nth-of-type(3) {
        width: 14%;
      }

      &:nth-of-type(4) {
        width: 20%;
      }

      &:nth-of-type(5) {
        width: 14%;
      }

      &:nth-of-type(6) {
        width: 16%;
        justify-content: flex-end;
      }
    }
  }
}

.div-table.--event-log-table {
  &.--heading div {
    @media (min-width: 1140px) {
      &:nth-of-type(1) {
        width: 32%;
      }

      &:nth-of-type(2) {
        width: 17%;
      }

      &:nth-of-type(3) {
        width: 22.3%;
      }

      &:nth-of-type(4) {
        width: 13%;
      }

      &:nth-of-type(5) {
        width: 20%;
        flex: 1;
      }
    }
  }

  & .--column {
    @media (min-width: 1000px) {
      &:nth-of-type(1) {
        width: 30.5%;
      }

      &:nth-of-type(2) {
        width: 17.5%;
      }

      &:nth-of-type(3) {
        width: 23%;
      }

      &:nth-of-type(4) {
        width: 14%;
      }

      &:nth-of-type(5) {
        width: 15%;
      }
    }
  }

  & .--row {
    @media (min-width: 1000px) {
      padding: 0.5rem 0.25rem;
    }
  }
}

.div-table.--audit-log-table {
  &.--heading div,
  & .--column {
    @media (min-width: 1000px) {
      &:nth-of-type(1) {
        width: 24%;
      }

      &:nth-of-type(2) {
        flex: 1;
      }

      &:nth-of-type(3) {
        width: fit-content;
      }
    }

    @media (min-width: 1140px) {
      &:nth-of-type(1) {
        width: 20%;
      }
    }
  }
}

.div-table.--history-table {
  &.--row,
  &.--heading {
    @media (min-width: 1024px) {
      div {
        flex-basis: 0;
        min-width: 0;

        & > span + span {
          max-width: unset;
        }

        &:nth-of-type(1) {
          flex-grow: 1;
        }

        &:nth-of-type(2) {
          flex-grow: 2;
        }

        &:nth-of-type(3) {
          flex-grow: 1.5;
        }

        &:nth-of-type(4) {
          flex-grow: 1;
          text-align: start;
        }

        &:nth-of-type(5) {
          flex-grow: 1.5;
          text-align: start;
        }

        &:last-of-type {
          flex-grow: 1.5;
          text-align: end;
          justify-content: end;
        }
      }
    }
  }

  &.--row {
    .status-text {
      @media (min-width: 1140px) {
        display: none;
      }
    }
  }
}
.div-table.--payin-transaction-history-table {
  &.--row,
  &.--heading {
    @media (min-width: 1024px) {
      div {
        flex-basis: 0;
        min-width: 0;

        & > span + span {
          max-width: unset;
        }
        &:nth-of-type(1) {
          flex-grow: 0;
        }

        &:nth-of-type(2) {
          flex-grow: 1;
        }

        &:nth-of-type(3) {
          flex-grow: 2;
        }

        &:nth-of-type(4) {
          flex-grow: 1.5;
        }

        &:nth-of-type(5) {
          flex-grow: 1;
          text-align: start;
        }

        &:nth-of-type(6) {
          flex-grow: 1.5;
          text-align: start;
        }

        &:last-of-type {
          flex-grow: 1.5;
          text-align: end;
          justify-content: end;
        }
      }
    }
  }

  &.--row {
    .status-text {
      @media (min-width: 1140px) {
        display: none;
      }
    }
  }
}
.div-table.--payout-transaction-history-table {
  &.--row,
  &.--heading {
    @media (min-width: 1024px) {
      div {
        flex-basis: 0;
        min-width: 0;

        & > span + span {
          max-width: unset;
        }
        &:nth-of-type(1) {
          flex-grow: 0;
        }

        &:nth-of-type(2) {
          flex-grow: 1;
        }

        &:nth-of-type(3) {
          flex-grow: 2;
        }

        &:nth-of-type(4) {
          flex-grow: 1.5;
        }

        &:nth-of-type(5) {
          flex-grow: 1;
          text-align: start;
        }

        &:nth-of-type(6) {
          flex-grow: 1.5;
          text-align: start;
        }

        &:last-of-type {
          flex-grow: 1.5;
          text-align: end;
          justify-content: end;
        }
      }
    }
  }

  &.--row {
    .status-text {
      @media (min-width: 1140px) {
        display: none;
      }
    }
  }
}
.div-table.--balance-history {
  & .--column {
    &:nth-of-type(4) {
      display: block !important;
    }
  }

  &.--row,
  &.--heading {
    @media (min-width: 1024px) {
      div {
        margin: 0;

        &:nth-of-type(1) {
          min-width: 20%;
          padding-left: 25px !important;
        }

        &:nth-of-type(2) {
          min-width: 43%;

          span {
            @media (min-width: 1024px) {
              max-width: 350px;
            }

            @media (min-width: 1400px) {
              max-width: 450px;
            }
          }
        }

        &:nth-of-type(3) {
          min-width: 15%;
          text-align: left !important;
        }

        &:nth-of-type(4) {
          min-width: 22%;
          padding-right: 25px !important;

          @media (min-width: 1024px) {
            text-align: right !important;
          }
        }
      }
    }
  }

  &.--reserve-history {
    &.--row,
    &.--heading {
      @media (min-width: 1024px) {
        div {
          &:nth-of-type(1) {
            min-width: 12%;
          }

          &:nth-of-type(2) {
            min-width: 45%;
          }

          &:nth-of-type(3) {
            min-width: 11%;
            text-align: center;
          }

          &:nth-of-type(4) {
            min-width: 11%;

            @media (min-width: 1024px) {
              text-align: center !important;
            }
          }

          &:nth-of-type(5) {
            min-width: 9%;

            @media (min-width: 1024px) {
              text-align: right !important;
            }
          }
        }
      }
    }
  }
}

.div-table.--lien-history {
  position: relative;
  &.--row {
    div {
      &:nth-of-type(4) {
        color: #2376f3;
        font-weight: 500;
      }
    }
  }
  &.--row,
  &.--heading {
    transform: none;
    @media (min-width: 1024px) {
      div {
        &:nth-of-type(1) {
          min-width: 10%;
        }

        &:nth-of-type(2) {
          min-width: 20%;
        }

        &:nth-of-type(3) {
          min-width: 20%;
        }

        &:nth-of-type(4) {
          min-width: 15%;
        }

        &:nth-of-type(5) {
          min-width: 17%;
        }

        &:nth-of-type(6) {
          min-width: 12%;
        }

        &:nth-of-type(7) {
          min-width: 6%;
        }
      }
    }
  }
}

.div-table.--lien-events-history {
  &.--row {
    div {
      &:nth-of-type(2),
      &:nth-of-type(5) {
        color: #2376f3;
        font-weight: 500;
      }
      &:nth-of-type(6) {
        font-weight: 600;
      }
    }
  }
  &.--row,
  &.--heading {
    @media (min-width: 1024px) {
      div {
        &:nth-of-type(1) {
          min-width: 15%;
        }

        &:nth-of-type(2) {
          min-width: 20%;
        }

        &:nth-of-type(3) {
          min-width: 25%;
          text-transform: capitalize;
          max-width: 350px !important;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &:nth-of-type(4) {
          min-width: 15%;
        }

        &:nth-of-type(5) {
          min-width: 15%;
        }

        &:nth-of-type(6) {
          display: block !important;
          min-width: 10%;
          text-align: right;
        }
      }
    }
  }
}

.div-table.--webhooks-table {
  &.--heading {
    @media (min-width: $breakpoint-desktop) {
      display: flex;
      font-size: 0.71rem;
      padding: 0.3rem 1.1rem;
    }

    div {
      font-weight: 400;
    }
  }

  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        margin: 0;

        &:nth-of-type(1) {
          min-width: 15%;
          text-align: left;
        }

        &:nth-of-type(2) {
          min-width: 25%;
          text-align: left;
          margin-right: 0.5rem;
        }

        &:nth-of-type(3) {
          min-width: 15%;
          text-align: left;
        }

        &:nth-of-type(4) {
          min-width: 15%;
          text-align: center;
        }

        &:nth-of-type(5) {
          min-width: 10%;
          text-align: center;
        }

        &:nth-of-type(6) {
          min-width: 15%;
          text-align: right;
        }
      }
    }
  }

  &.--row {
    font-size: 0.81rem;

    @media (min-width: $breakpoint-desktop) {
      flex-direction: row;
      font-size: 0.9rem;
    }
  }
}

.div-table.--settlement-table {
  @extend .div-table;

  &.--heading {
    @media (min-width: $breakpoint-desktop) {
      display: flex;
      font-size: 0.71rem;
      padding: 0.3rem 1.1rem;
    }

    &.bulk-action-heading {
      &.--heading {
        @media (min-width: $breakpoint-desktop) {
          padding: 0.3rem 0;

          div {
            &:nth-of-type(1) {
              width: 4%;
            }

            &:nth-of-type(2) {
              width: 15%;
            }

            &:nth-of-type(3) {
              width: 15%;
            }

            &:nth-of-type(4) {
              width: 12%;
            }

            &:nth-of-type(5) {
              width: 13%;
            }

            &:nth-of-type(6) {
              width: 13%;
            }

            &:nth-of-type(7) {
              width: 16%;
            }

            &:nth-of-type(8) {
              width: 13%;
            }
          }
        }
      }
    }
  }

  &.bulk-action-row {
    &.--row {
      div {
        &:nth-of-type(1) {
          min-width: 2%;
        }

        &:nth-of-type(2) {
          min-width: 15%;

          .trxn-id {
            flex: 1;
          }
        }

        &:nth-of-type(3) {
          min-width: 15%;
        }

        &:nth-of-type(4) {
          min-width: 12%;
        }

        &:nth-of-type(5) {
          min-width: 13%;
        }

        &:nth-of-type(6) {
          min-width: 13%;
        }

        &:nth-of-type(7) {
          min-width: 15%;
        }

        &:nth-of-type(8) {
          min-width: 15%;
        }
      }
    }
  }

  &.--heading {
    background-color: #f9fbfd;
  }
}

.div-table.--chargeback-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        width: 100%;

        &:nth-of-type(1) {
          min-width: 11% !important;
        }

        &:nth-of-type(2) {
          min-width: 18% !important;
        }

        &:nth-of-type(3) {
          min-width: 18% !important;
          text-align: left;
        }

        &:nth-of-type(4) {
          min-width: 10% !important;
          text-align: left;
        }

        &:nth-of-type(5) {
          min-width: 10% !important;
        }

        &:nth-of-type(6) {
          min-width: 16%;
        }

        &:nth-of-type(7) {
          min-width: 20%;
        }
      }
    }

    span:not(:first-of-type) {
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &.--heading {
    background-color: #f9fbfd;
  }
}

.div-table.--settlement-payout-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          width: 14%;
        }

        &:nth-of-type(2) {
          width: 17%;
        }

        &:nth-of-type(3) {
          width: 15%;
        }

        &:nth-of-type(4) {
          width: 20%;
        }

        &:nth-of-type(5) {
          width: 16%;
        }

        &:nth-of-type(6) {
          width: 18%;
        }
      }
    }
  }
}

.div-table.--settlement-txn-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          min-width: 20%;
        }

        &:nth-of-type(2) {
          min-width: 25%;
        }

        &:nth-of-type(3) {
          min-width: 20%;
        }

        &:nth-of-type(4) {
          min-width: 10%;
        }

        &:nth-of-type(5) {
          min-width: 10%;
          text-align: right;
        }

        &:nth-of-type(6) {
          min-width: 10%;
          text-align: right;
        }
      }
    }
  }
}

.div-table.--pool-accounts {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        &:nth-of-type(1) {
          width: 25%;
        }

        &:nth-of-type(2) {
          width: 28%;

          span {
            max-width: 100%;
          }
        }

        &:nth-of-type(3) {
          width: 26%;
        }

        &:nth-of-type(4) {
          width: 20%;
          text-align: right;
          justify-content: right;
        }
      }
    }
  }
}

.div-table.--pool-account-summary {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        &:nth-of-type(1) {
          width: 20%;
        }

        &:nth-of-type(2) {
          width: 35%;

          span {
            max-width: 100%;
          }
        }

        &:nth-of-type(3) {
          width: 20%;
        }

        &:nth-of-type(4) {
          width: 25%;
          text-align: right;
          justify-content: right;
        }
      }
    }
  }
}

.div-table.--vba-fixed-virtual-accounts {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        &:nth-of-type(1) {
          width: 28%;
        }

        &:nth-of-type(2) {
          min-width: 24%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &:nth-of-type(3) {
          width: 28%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &:nth-of-type(4) {
          width: 30%;
          text-align: left;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &:nth-of-type(5) {
          min-width: 20%;
          text-align: left;
        }

        &:nth-of-type(6) {
          min-width: 12%;
          text-align: left;
        }
      }
    }
  }
}

.div-table.--vba-trx {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        &:nth-of-type(1) {
          width: 15%;
          // color: unset;
        }

        &:nth-of-type(2) {
          width: 25%;
        }

        &:nth-of-type(3) {
          width: 30%;
        }

        &:nth-of-type(4) {
          width: 15%;
        }

        &:nth-of-type(5) {
          width: 15%;
          text-align: right;
          justify-content: right;
        }
      }
    }
  }
}

.div-table.--vba-acc-trx {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        &:nth-of-type(1) {
          width: 20%;
        }

        &:nth-of-type(2) {
          width: 35%;

          span {
            max-width: 100%;
          }
        }

        &:nth-of-type(3) {
          width: 20%;
        }

        &:nth-of-type(4) {
          width: 25%;
          text-align: right;
          justify-content: right;
        }
      }
    }
  }
}

.div-table.--vba-acc-holders {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        &:nth-of-type(1) {
          width: 10%;
        }

        &:nth-of-type(2) {
          width: 30%;

          span {
            max-width: 100%;
          }
        }

        &:nth-of-type(3) {
          width: 10%;
        }

        &:nth-of-type(4) {
          width: 15%;
        }

        &:nth-of-type(5) {
          width: 20%;
        }

        &:nth-of-type(6) {
          width: 15%;
        }
      }
    }
  }
}

.div-table.--vba-acc-nums {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        &:nth-of-type(1) {
          width: 10%;
        }

        &:nth-of-type(2) {
          width: 30%;
        }

        &:nth-of-type(3) {
          width: 20%;
        }

        &:nth-of-type(4) {
          width: 10%;
        }

        &:nth-of-type(5) {
          width: 10%;
        }

        &:nth-of-type(6) {
          width: 20%;
        }
      }
    }
  }
}

.div-table.--vba-acc-holder-events {
  &.--heading,
  &.--row {
    @media (min-width: 1000px) {
      div {
        &:nth-of-type(1) {
          width: 20%;
        }

        &:nth-of-type(2) {
          width: 50%;
        }

        &:nth-of-type(3) {
          width: 30%;
        }
      }
    }
  }
}

.div-table.--generate-report {
  &.--heading div {
    &:nth-of-type(2) {
      width: 70%;
      padding-left: 10px !important;
    }

    &:nth-of-type(3) {
      padding-left: 72px;
    }
  }

  & .--column {
    @media (min-width: 1000px) {
      &:nth-of-type(1) {
        width: 2.8%;
      }

      &:nth-of-type(2) {
        padding-left: 0 !important;
        width: 75%;
      }

      &:nth-of-type(3) {
        justify-content: start;
      }
    }

    @media (min-width: 1140px) {
      &:nth-of-type(1) {
        width: 2.8%;
      }

      &:nth-of-type(2) {
        padding-left: 0 !important;
        width: 75%;
      }

      &:nth-of-type(3) {
        justify-content: start;
      }
    }
  }

  &.--row {
    &.div-table .text-tooltip-w {
      display: block !important;
      margin-left: -6px !important;
    }

    div.text-tooltip--content {
      width: 260px;
      height: fit-content;
      padding: 0.3rem 0.8rem;
      text-align: center;
    }

    &.div {
      padding-left: 0px !important;
    }

    @media (min-width: 1000px) {
      height: 3rem;

      &.text-tooltip-w div {
        padding-top: 0px;
      }
    }
  }
}

.div-table.--compliance-table {
  @media (min-width: $breakpoint-desktop) {
    > div {
      width: 20%;
    }
  }

  .status-pill {
    @media (min-width: $breakpoint-desktop) {
      margin-left: 50px;
    }
  }

  .feedback-notifier {
    width: 18px;
    margin-right: -20px;

    @media (max-width: $breakpoint-desktop) {
      margin-right: 10px;
    }
  }

  &.--heading {
    div {
      &:nth-of-type(2) {
        @media (min-width: $breakpoint-desktop) {
          padding-left: 55px !important;
        }
      }
    }
  }

  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          min-width: 20%;
        }

        &:nth-of-type(2) {
          min-width: 20%;
        }

        &:nth-of-type(3) {
          min-width: 12%;
        }

        &:nth-of-type(4) {
          min-width: 20%;
        }

        &:nth-of-type(5) {
          min-width: 12%;
        }

        &:nth-of-type(6) {
          min-width: 15%;
          text-align: right;
          justify-content: flex-end;
        }
      }
    }
  }

  &.compliance-rejected-table {
    .status-pill {
      @media only screen and (min-width: $breakpoint-desktop) {
        margin-left: 0 !important;
      }
    }

    &.--heading,
    &.--row {
      @media (min-width: $breakpoint-desktop) {
        div {
          &:nth-of-type(1) {
            min-width: 22.314%;
          }

          &:nth-of-type(2) {
            min-width: 10.43%;
            padding-left: 0 !important;
          }

          &:nth-of-type(3) {
            min-width: 21.27%;
            max-width: 18.32rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            span {
              max-width: 200px;
            }
          }

          &:nth-of-type(4) {
            min-width: 16.51%;
          }

          &:nth-of-type(5) {
            min-width: 11.51%;
          }

          &:nth-of-type(6) {
            min-width: 15.5%;
            text-align: right;
          }
        }
      }
    }
  }
}

.div-table.--teams-table {
  position: relative;

  &.--heading,
  &.--row {
    transform: none;

    div {
      &:nth-of-type(1) {
        min-width: 20%;
      }

      &:nth-of-type(2) {
        min-width: 20%;
      }

      &:nth-of-type(3) {
        min-width: 15%;
      }

      &:nth-of-type(4) {
        min-width: 10%;
      }

      &:nth-of-type(5) {
        min-width: 10%;
      }

      &:nth-of-type(6) {
        min-width: 25%;
      }
    }
  }
}

.div-table.--currency-pair-table {
  position: relative;

  &.--heading,
  &.--row {
    transform: none;

    div {
      &:nth-of-type(1) {
        min-width: 15%;
      }

      &:nth-of-type(2) {
        min-width: 20%;
      }

      &:nth-of-type(3) {
        min-width: 40%;
      }

      &:nth-of-type(4) {
        min-width: 25%;
      }
    }
  }
}

.div-table.--merchant-account-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      > div {
        &:nth-of-type(1) {
          min-width: 15%;
        }

        &:nth-of-type(2) {
          min-width: 30%;
        }

        &:nth-of-type(3) {
          min-width: 55%;
        }
      }
    }
  }
}

.div-table.--bulkpayouts-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          width: 15%;
        }

        &:nth-of-type(2) {
          width: 20%;
        }

        &:nth-of-type(3) {
          width: 25%;
        }

        &:nth-of-type(4) {
          width: 20%;
        }

        &:nth-of-type(5) {
          width: 20%;
        }
      }
    }
  }
}

.div-table.--bulkpayouts-draft-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          width: 20%;
        }

        &:nth-of-type(2) {
          width: 15%;
        }

        &:nth-of-type(3) {
          width: 15%;
        }

        &:nth-of-type(4) {
          width: 15%;
        }

        &:nth-of-type(5) {
          width: 20%;
        }

        &:nth-of-type(6) {
          width: 15%;
        }
      }
    }
  }
}

.div-table.--reversal-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          width: 10%;
        }

        &:nth-of-type(2) {
          width: 18%;
        }

        &:nth-of-type(3) {
          width: 27%;
        }

        &:nth-of-type(4) {
          width: 15%;
        }

        &:nth-of-type(5) {
          width: 15%;
        }

        &:nth-of-type(6) {
          width: 15%;
        }
      }
    }
  }
}

.div-table.--payout-reversal-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          width: 0.2%;
        }

        &:nth-of-type(2) {
          width: 10%;
        }

        &:nth-of-type(3) {
          width: 18%;
        }

        &:nth-of-type(4) {
          width: 19%;
        }

        &:nth-of-type(5) {
          width: 17%;
        }

        &:nth-of-type(6) {
          width: 18%;
        }

        &:nth-of-type(7) {
          width: 17.8%;
        }
      }
    }
  }
}

.div-table.--chargeback-table {
  &.--heading,
  &.--row {
    @media (min-width: 1140px) {
      div {
        &:nth-of-type(1) {
          min-width: 10%;
        }

        &:nth-of-type(2) {
          min-width: 20%;
        }

        &:nth-of-type(3) {
          min-width: 18%;
        }

        &:nth-of-type(4) {
          min-width: 15%;

          span:nth-child(2) {
            max-width: 90px !important;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        &:nth-of-type(5) {
          min-width: 15%;

          span:nth-child(2) {
            max-width: 95px !important;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        &:nth-of-type(6) {
          min-width: 15%;
        }

        &:nth-of-type(7) {
          min-width: 16%;
          text-align: center;
        }
      }
    }
  }
}

.div-table.--disputes-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          min-width: 10% !important;
        }

        &:nth-of-type(2) {
          min-width: 20%;
        }

        &:nth-of-type(3) {
          min-width: 20%;
        }

        &:nth-of-type(4) {
          min-width: 20%;
        }

        &:nth-of-type(5) {
          min-width: 15%;
        }
        &:nth-of-type(6) {
          text-align: right;
          min-width: 15%;
        }
      }
    }
  }
}

.div-table.--user-roles-table {
  &.--heading,
  &.--row {
    div {
      &:nth-of-type(1) {
        min-width: 30%;
        @media (min-width: $breakpoint-desktop) {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }

      &:nth-of-type(2) {
        min-width: 30%;
        span {
          max-width: 300px !important;

          @media (min-width: $breakpoint-desktop) {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
        }
      }

      &:nth-of-type(3) {
        min-width: 20%;
      }

      &:nth-of-type(4) {
        min-width: 20%;
      }
    }
  }
}

.div-table.--reconciliation-history-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          min-width: 15%;
        }

        &:nth-of-type(2) {
          min-width: 50% !important;
        }

        &:nth-of-type(3) {
          min-width: 20%;
        }

        &:nth-of-type(4) {
          min-width: 15%;

        }
      }
    }
  }
  &.--row {
    div {
      &:nth-of-type(4) {
        color: #2376f3;
        .action {
          display: flex;
          align-items: center;
          column-gap: 0.3rem;
          font-weight: 500;
        }
      }
    }
  }
}

.table-wrapper {
  border: 2px solid #f1f6fa;
  border-radius: 10px;

  @media (max-width: 1140px) {
    background: #f9fbfd;
  }

  .table {
    margin-bottom: 0;
  }

  .element-box-tp {
    margin: 0;
    border-radius: 10px;
  }

  .--heading {
    background: #f9fbfd;
    border-radius: 0px;
    border-bottom: 2px solid #f1f6fa;

    @media (min-width: 1140px) {
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
  }

  .--row + .--row {
    margin-top: 0px;
  }

  .--row,
  tr {
    margin: 0;
    background: #ffffff;
    border-radius: 0px;
    border-bottom: 2px solid #f1f6fa;
    color: #102649;

    @media (max-width: 1140px) {
      &:first-child {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
    }

    &:last-child {
      border-bottom-left-radius: 10px;
      border-bottom: 0px;
      border-bottom-right-radius: 10px;
    }
  }

  .pagination-container {
    padding: 2px 15px 15px;
    background: #ffffff;
    border-top: 2px solid #f1f6fa;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }
}

.table-wrapper {
  border: 2px solid #f1f6fa;
  border-radius: 10px;

  @media (max-width: 1140px) {
    background: #f9fbfd;
  }

  &.--has-pagination {
    .--heading {
      border-bottom: 0px solid !important;
    }

    .--row,
    tr {
      border-top: 2px solid #f1f6fa;
      border-bottom: 0px solid !important;
    }
  }

  .table {
    margin-bottom: 0;
  }

  .element-box-tp {
    margin: 0;
    border-radius: 10px;
  }

  .--heading {
    background: #f9fbfd;
    border-radius: 0px;
    border-bottom: 2px solid #f1f6fa;

    @media (min-width: 1140px) {
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
  }

  .--row + .--row {
    margin-top: 0px;
  }

  .--row,
  tr {
    margin: 0;
    border-radius: 0px;
    border-bottom: 2px solid #f1f6fa;
    color: #102649;

    @media (max-width: 1140px) {
      &:first-child {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
    }

    &:last-child {
      border-bottom-left-radius: 10px;
      border-bottom: 0px;
      border-bottom-right-radius: 10px;
    }
  }

  .pagination-container {
    padding: 2px 15px 15px;
    background: #ffffff;
    border-top: 2px solid #f1f6fa;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }
}

.table-wrapper {
  border: 2px solid #f1f6fa;
  border-radius: 10px;

  @media (max-width: 1140px) {
    background: #f9fbfd;
  }

  &.--has-pagination {
    .--heading {
      border-bottom: 0px solid !important;
    }

    .--row,
    tr {
      border-top: 2px solid #f1f6fa;
      border-bottom: 0px solid !important;
    }
  }

  .table {
    margin-bottom: 0;
  }

  .element-box-tp {
    margin: 0;
    border-radius: 10px;
  }

  .--heading {
    background: #f9fbfd;
    border-radius: 0px;
    border-bottom: 2px solid #f1f6fa;

    @media (min-width: 1140px) {
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
  }

  .--row + .--row {
    margin-top: 0px;
  }

  .--row,
  tr {
    margin: 0;
    border-radius: 0px;
    border-bottom: 2px solid #f1f6fa;
    color: #102649;

    @media (max-width: 1140px) {
      &:first-child {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
    }

    &:last-child {
      border-bottom-left-radius: 10px;
      border-bottom: 0px;
      border-bottom-right-radius: 10px;
    }
  }

  .pagination-container {
    padding: 2px 15px 15px;
    background: #ffffff;
    border-top: 2px solid #f1f6fa;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }
}

.div-table.--chargeback-summary-table {
  &.--heading {
    div {
      width: 100%;
    }

    @media (min-width: $breakpoint-desktop) {
      display: flex;
      font-size: 0.71rem;
      padding: 0.3rem 0rem;
    }
  }

  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        width: 100%;

        span:not(:first-of-type) {
          display: inline;
        }

        &:nth-of-type(1) {
          min-width: 5% !important;
          width: 5%;
          text-align: left;
        }

        &:nth-of-type(2) {
          min-width: 25% !important;
          text-align: left;
          margin-right: 0.5rem;
        }

        &:nth-of-type(3) {
          min-width: 30% !important;
          text-align: left;
        }

        &:nth-of-type(4) {
          min-width: 10% !important;
          text-align: center;
        }

        &:nth-of-type(5) {
          min-width: 15%;
          text-align: left;

          span {
            font-size: 17px;
            align-items: center;

            i {
              margin-inline: 9px;
            }
          }
        }
      }
    }
  }

  &.--row {
    background-color: #fff;

    &:hover {
      box-shadow: 0 2px 5px rgba(69, 101, 173, 0.1);
      transform: none;
      background-color: rgba(179, 192, 223, 0.1);
    }

    @media (min-width: 1140px) {
      flex-direction: row;
      font-size: 0.9rem;

      div {
        margin: 0;
      }
    }
  }
}

.div-table.--swaps-table {
  &.--heading,
  &.--row {
    @media (min-width: $breakpoint-desktop) {
      div {
        &:nth-of-type(1) {
          width: 16%;
        }

        &:nth-of-type(2) {
          width: 18%;
        }

        &:nth-of-type(3) {
          width: 18%;
        }

        &:nth-of-type(4) {
          width: 16%;
        }

        &:nth-of-type(5) {
          width: 16%;
        }

        &:nth-of-type(6) {
          width: 16%;
        }
      }
    }
  }
}

.div-table.--merchant-currency-table {
  &.--heading div,
  & .--column {
    @media (min-width: $breakpoint-desktop) {
      &:nth-of-type(1) {
        width: 20%;
      }

      &:nth-of-type(2) {
        width: 20%;
      }

      &:nth-of-type(3) {
        width: 15%;
      }

      &:nth-of-type(4) {
        width: 15%;
      }

      &:nth-of-type(5) {
        width: 15%;
      }

      &:nth-of-type(6) {
        width: 15%;
      }
    }
  }
}

.div-table.--merchant-currency-table-bulk {
  &.--heading div,
  & .--column {
    @media (min-width: $breakpoint-desktop) {
      &:nth-of-type(1) {
        width: 4%;
      }

      &:nth-of-type(2) {
        width: 20%;
      }

      &:nth-of-type(3) {
        width: 20%;
      }

      &:nth-of-type(4) {
        width: 15%;
      }

      &:nth-of-type(5) {
        width: 15%;
      }

      &:nth-of-type(6) {
        width: 15%;
      }

      &:nth-of-type(7) {
        width: 15%;
      }
    }
  }
}

.currency-pair {
  :last-child {
    margin: 0 0 -0.4rem -0.4rem;
  }
}

.div-table.--partner-balance-history-table {
  &.--heading div,
  & .--column {
    @media (min-width: $breakpoint-desktop) {
      &:nth-of-type(1) {
        width: 20%;
      }

      &:nth-of-type(2) {
        width: 20%;
      }

      &:nth-of-type(3) {
        width: 25%;
      }

      &:nth-of-type(4) {
        width: 15%;
      }

      &:nth-of-type(5) {
        width: 20%;
        text-align: right;
        display: block !important;
      }
    }
  }

  & .--column {
    &:nth-of-type(5) {
      font-weight: 500;

      & .credit {
        color: #24b314;
      }

      & .debit {
        color: #f44336;
      }
    }
  }
}

.div-table.--paused-payments-table {
  @extend .div-table;

  &.--heading {
    @media (min-width: $breakpoint-desktop) {
      display: flex;
      font-size: 0.71rem;
      padding: 0.3rem 1.1rem;
    }

    &.bulk-action-heading {
      &.--heading {
        @media (min-width: $breakpoint-desktop) {
          padding: 0.3rem 0;

          div {
            &:nth-of-type(1) {
              width: 3.5%;
            }

            &:nth-of-type(2) {
              width: 16%;
            }

            &:nth-of-type(3) {
              width: 17%;
            }

            &:nth-of-type(4) {
              width: 16%;
            }

            &:nth-of-type(5) {
              width: 16%;
            }

            &:nth-of-type(6) {
              width: 13%;
            }

            &:nth-of-type(7) {
              width: 10%;
            }
          }
        }
      }
    }
  }

  &.bulk-action-row {
    &.--row {
      display: flex;
      align-items: center;

      div {
        &:nth-of-type(1) {
          width: 2%;
          height: 2%;
        }

        &:nth-of-type(2) {
          min-width: 15%;

          .trxn-id {
            flex: 1;
          }
        }

        &:nth-of-type(3) {
          min-width: 15%;
        }

        &:nth-of-type(4) {
          min-width: 12%;
        }

        &:nth-of-type(5) {
          min-width: 13%;
        }

        &:nth-of-type(6) {
          min-width: 13%;
        }

        &:nth-of-type(7) {
          min-width: 15%;
        }
      }
    }

    @media (min-width: $breakpoint-desktop) {
      div {
        height: 20px;
      }
    }
  }

  &.--heading {
    background-color: #f9fbfd;
  }
}

.div-table.issuance-merchants-access-table {
  @media (min-width: $breakpoint-desktop) {
    &.--heading > div,
    & > .--column {
      flex-basis: 0;
      flex-grow: 1;
      flex-shrink: 0;
      overflow-x: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &.--heading > div:last-child,
    & > .--column:last-child {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.div-table.--users-table {
  &.--heading,
  &.--row {
    div {
      &:nth-of-type(1) {
        min-width: 20%;
      }

      &:nth-of-type(2) {
        min-width: 40%;
        span {
          max-width: 300px;
          text-overflow: inherit !important;
          white-space: normal !important;
        }
      }

      &:nth-of-type(3) {
        min-width: 25%;
      }

      &:nth-of-type(4) {
        min-width: 15%;
      }
    }
  }
}
