import { create } from 'zustand';
import { combine, createJSONStorage, devtools, persist } from 'zustand/middleware';

import { IProcessorReportMapping, KeyMappingType, MappingType, ReconciliationDataType } from '+types';
import { StoreStorage } from '+utils';

interface IStartReconciliationState {
  startReconciliationData: ReconciliationDataType;
  primaryKeyMappings: KeyMappingType;
  comparisonKeyMappings: KeyMappingType;
  referenceKeyMappings: KeyMappingType;
  statusKeyMappings: KeyMappingType;
  autoMatchColumns: boolean;
  bulkDeleteIds: {
    primaryKeyMappings: string[];
    comparisonKeyMappings: string[];
    referenceKeyMappings: string[];
    statusKeyMappings: string[];
  };
}

interface IStartReconciliationAction {
  setStartReconciliationData: (data: Omit<ReconciliationDataType, 'field_mapping'>) => void;
  clearStartReconciliationData: () => void;
  setKeyMappings: (data: KeyMappingType, type: MappingType) => void;
  updateKeyMappings: (data: { id: string; value: Partial<IProcessorReportMapping> }, type: MappingType) => void;
  deleteKeyMapping: (id: string, type: MappingType) => void;
  clearKeyMappings: (type: MappingType | 'all', clearBulkDeleteIds?: boolean) => void;
  setAutoMatchColumns: () => void;
  setBulkDeleteIds: (ids: string[], mappingType: MappingType) => void;
}
const initialState = {
  startReconciliationData: {} as ReconciliationDataType,
  primaryKeyMappings: [] as KeyMappingType,
  comparisonKeyMappings: [] as KeyMappingType,
  referenceKeyMappings: [] as KeyMappingType,
  statusKeyMappings: [] as KeyMappingType,
  autoMatchColumns: true,
  bulkDeleteIds: {
    primaryKeyMappings: [],
    comparisonKeyMappings: [],
    referenceKeyMappings: [],
    statusKeyMappings: []
  }
};

const createActions = (set: any): IStartReconciliationAction & Pick<IStartReconciliationState, 'startReconciliationData'> => ({
  startReconciliationData: initialState.startReconciliationData,
  setBulkDeleteIds: (ids: string[], mappingType: MappingType) => {
    set((state: IStartReconciliationState) => ({
      ...state,
      bulkDeleteIds: {
        ...state.bulkDeleteIds,
        [mappingType]: ids
      }
    }));
  },
  setStartReconciliationData: (data: Omit<ReconciliationDataType, 'field_mapping'>) => {
    set((state: IStartReconciliationState) => ({
      ...state,
      startReconciliationData: {
        ...state.startReconciliationData,
        ...data
      }
    }));
  },
  clearStartReconciliationData: () => {
    set((state: IStartReconciliationState) => ({
      ...state,
      startReconciliationData: {} as ReconciliationDataType
    }));
  },
  setKeyMappings: (data: KeyMappingType, type: MappingType) => {
    set((state: IStartReconciliationState) => {
      const existingKeys = new Set(state[type].map(mapping => `${mapping.processor_report}:${mapping.internal_report}`));
      const uniqueData = data.filter(mapping => !existingKeys.has(`${mapping.processor_report}:${mapping.internal_report}`));
      return {
        ...state,
        [type]: [...state[type], ...uniqueData]
      } as IStartReconciliationState;
    });
  },
  updateKeyMappings: (data: { id: string; value: Partial<IProcessorReportMapping> }, type) => {
    set((state: IStartReconciliationState) => ({
      ...state,
      [type]: state[type].map(mapping => (data.id === mapping.id ? { ...mapping, ...data.value } : mapping))
    }));
  },
  deleteKeyMapping: (id: string, type: MappingType) => {
    set((state: IStartReconciliationState) => ({
      ...state,
      [type]: state[type].filter(mapping => mapping.id !== id)
    }));
  },
  clearKeyMappings: (type: MappingType | 'all', clearBulkDeleteIds = true) =>
    set((state: IStartReconciliationState) => {
      if (type === 'all') {
        const baseState = {
          ...state,
          primaryKeyMappings: [],
          comparisonKeyMappings: [],
          referenceKeyMappings: [],
          statusKeyMappings: [],
          startReconciliationData: { ...initialState.startReconciliationData }
        } as IStartReconciliationState;
        return clearBulkDeleteIds ? { ...baseState, bulkDeleteIds: { ...initialState.bulkDeleteIds } } : baseState;
      } else {
        const baseState = { ...state, [type]: [] } as IStartReconciliationState;
        return clearBulkDeleteIds
          ? {
              ...baseState,
              bulkDeleteIds: {
                ...state.bulkDeleteIds,
                [type]: []
              }
            }
          : baseState;
      }
    }),
  setAutoMatchColumns: () =>
    set((state: IStartReconciliationState) => ({
      ...state,
      autoMatchColumns: !state.autoMatchColumns
    }))
});

const isTestEnv =
  typeof process !== 'undefined' && (process.env.NODE_ENV === 'test' || !!process.env.VITEST_WORKER_ID || process.env.VITEST === 'true');

const useReconciliationStore = isTestEnv
  ? create(
      devtools(
        combine<IStartReconciliationState, IStartReconciliationAction>(initialState, set => ({
          ...createActions(set)
        }))
      )
    )
  : create(
      devtools(
        persist(
          combine<IStartReconciliationState, IStartReconciliationAction>(initialState, set => ({
            ...createActions(set)
          })),
          {
            name: 'reconciliation',
            storage: createJSONStorage(() => StoreStorage('session'))
          }
        )
      )
    );

export default useReconciliationStore;
