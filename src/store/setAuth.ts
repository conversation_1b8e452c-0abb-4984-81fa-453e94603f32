import { createJSONStorage, persist } from 'zustand/middleware';

import { Storage } from '+services/storage-services';
import { sessionKeys, StoreStorage } from '+utils';

export type AuthSlice = {
  clientToken: string;
  userToken: string;
  refreshToken: string;
  userTokenExpiration: string;
  isAuthenticated: boolean;
  isAuthorized: boolean;
  authorizeData: Record<string, unknown>;
  isLoading: boolean;
  error: boolean;
  profile: {
    email?: string;
    avatar?: string;
  };
  errors: Record<string, unknown>;
  permissions?: Record<string, boolean> | null;
};

const createAuthSlice = persist<AuthSlice>(
  () => ({
    clientToken: Storage.clientToken() || '',
    userToken: Storage.checkAuthentication() || '',
    refreshToken: Storage.getRefreshToken() || '',
    userTokenExpiration: Storage.checkExpiration() || '',
    isAuthenticated: !!Storage.checkAuthentication(),
    isAuthorized: false,
    authorizeData: {} as Record<string, unknown>,
    isLoading: false,
    error: false,
    profile:
      typeof Storage.getItem(sessionKeys.ADMIN_USER_PROFILE) === 'object'
        ? (Storage.getItem(sessionKeys.ADMIN_USER_PROFILE) as any) || { email: '', avatar: '' }
        : { email: '', avatar: '' },
    errors: {} as Record<string, unknown>,
    permissions: null
  }),
  {
    name: 'auth',
    storage: createJSONStorage(() => StoreStorage('local')),
  }
);

export default createAuthSlice;
