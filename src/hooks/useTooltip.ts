import { useEffect, useRef, useState } from 'react';

type PositionType = 'top' | 'bottom' | 'left' | 'right';

export type UseTooltipOptions = {
  position?: PositionType;
};
function useTooltip({ position = 'right' }: UseTooltipOptions = {}) {
  const [visible, setVisible] = useState<boolean>(false);
  const [currentPosition, setCurrentPosition] = useState(position);

  const [portalParent, setPortalParent] = useState<HTMLElement | null>(null);

  const tooltipRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!visible || !tooltipRef.current || !triggerRef.current) return;

    const tooltipElement = tooltipRef.current;
    const triggerElement = triggerRef.current;
    const triggerRect = triggerElement.getBoundingClientRect();
    const tooltipRect = tooltipElement.getBoundingClientRect();

    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
      scrollX: window.scrollX,
      scrollY: window.scrollY
    };

    const SPACING = 10;

    const container = portalParent;
    const isBodyContainer = !container || container === document.body;
    const containerRect = !isBodyContainer ? container!.getBoundingClientRect() : { left: 0, top: 0 };

    const triggerCenterX = triggerRect.left + triggerRect.width / 2;
    const triggerCenterY = triggerRect.top + triggerRect.height / 2;

    const centerHorizontal = isBodyContainer
      ? triggerCenterX - tooltipRect.width / 2
      : triggerCenterX - containerRect.left - tooltipRect.width / 2;

    const centerVertical = isBodyContainer
      ? triggerCenterY - tooltipRect.height / 2
      : triggerCenterY - containerRect.top - tooltipRect.height / 2;

    const positionCoordinates = {
      top: {
        top: isBodyContainer
          ? viewport.scrollY + triggerRect.top - tooltipRect.height - SPACING
          : triggerRect.top - containerRect.top - tooltipRect.height - SPACING,
        left: isBodyContainer ? viewport.scrollX + centerHorizontal : centerHorizontal
      },
      bottom: {
        top: isBodyContainer ? viewport.scrollY + triggerRect.bottom + SPACING : triggerRect.bottom - containerRect.top + SPACING,
        left: isBodyContainer ? viewport.scrollX + centerHorizontal : centerHorizontal
      },
      left: {
        top: isBodyContainer ? viewport.scrollY + centerVertical : centerVertical,
        left: isBodyContainer
          ? viewport.scrollX + triggerRect.left - tooltipRect.width - SPACING
          : triggerRect.left - containerRect.left - tooltipRect.width - SPACING
      },
      right: {
        top: isBodyContainer ? viewport.scrollY + centerVertical : centerVertical,
        left: isBodyContainer ? viewport.scrollX + triggerRect.right + SPACING : triggerRect.right - containerRect.left + SPACING
      }
    } as const;

    const isPositionInBounds = (pos: PositionType) => {
      const coords = positionCoordinates[pos];
      const minBounds = viewport.scrollX + SPACING;
      const maxHorizontalBound = viewport.scrollX + viewport.width - SPACING;
      const minVerticalBound = viewport.scrollY + SPACING;
      const maxVerticalBound = viewport.scrollY + viewport.height - SPACING;

      return (
        coords.top >= minVerticalBound &&
        coords.left >= minBounds &&
        coords.top + tooltipRect.height <= maxVerticalBound &&
        coords.left + tooltipRect.width <= maxHorizontalBound
      );
    };

    let finalPosition = position;

    if (!isPositionInBounds(finalPosition)) {
      const positionAlternatives: Record<PositionType, PositionType[]> = {
        top: ['bottom', 'left', 'right'],
        bottom: ['top', 'left', 'right'],
        left: ['right', 'top', 'bottom'],
        right: ['left', 'top', 'bottom']
      };

      for (const alternative of positionAlternatives[position]) {
        if (isPositionInBounds(alternative)) {
          finalPosition = alternative;
          break;
        }
      }
    }

    const finalCoords = positionCoordinates[finalPosition];
    tooltipElement.style.top = `${finalCoords.top}px`;
    tooltipElement.style.left = `${finalCoords.left}px`;
    tooltipElement.setAttribute('data-position', finalPosition);
    setCurrentPosition(finalPosition);
  }, [visible, position, portalParent]);

  useEffect(() => {
    const triggerEl = triggerRef && (triggerRef as any).current;
    if (!triggerEl || !triggerEl.closest) {
      setPortalParent(document.body);
      return;
    }
    const modalAncestor = triggerEl.closest('.modal');
    setPortalParent((modalAncestor as HTMLElement) || document.body);
  }, [triggerRef]);

  const showTooltip = () => setVisible(true);
  const hideTooltip = () => setVisible(false);

  const eventHandlers = {
    onMouseEnter: showTooltip,
    onFocus: showTooltip,
    onMouseLeave: hideTooltip,
    onBlur: hideTooltip
  };

  return {
    visible,
    showTooltip,
    hideTooltip,
    tooltipRef,
    triggerRef,
    eventHandlers,
    position: currentPosition,
    portalParent
  };
}

export default useTooltip;
