import { renderHook, waitFor } from '@testing-library/react';
import createHookWrapper from '+mock/reactQueryHookWrapper';
import { http, HttpResponse } from 'msw';
import { Mock } from 'vitest';

import { useSearchQuery } from '+hooks';
import useGetWhiteListeIP from '+hooks/useGetWhiteListeIP';
import { mockWhiteListedIPData } from '+mock/mockData';
import { server } from '+mock/mockServers';

vi.mock('react-router-dom', async () => ({
  ...(await vi.importActual('react-router-dom')),
  useParams: () => ({ id: '1' })
}));

vi.mock('+hooks/useSearchQuery', () => ({
  default: vi.fn()
}));

describe('useGetWhiteListeIP', () => {
  const mockUseSearchQuery = useSearchQuery as Mock;
  mockUseSearchQuery.mockReturnValue({
    value: {
      keyword: ''
    },
    setQuery: vi.fn(),
    clearAll: vi.fn(),
    get: () => ''
  });
  test('should initialize with correct initial state', async () => {
    const { result } = renderHook(() => useGetWhiteListeIP(), {
      wrapper: createHookWrapper()
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(true);
    });
    expect(result.current.data).toEqual(mockWhiteListedIPData.data.ip_addresses);
  });

  test('should filter IPs based on keyword search', async () => {
    mockUseSearchQuery.mockReturnValue({
      value: {
        keyword: ''
      },
      setQuery: vi.fn(),
      clearAll: vi.fn(),
      get: () => '32'
    });

    const { result } = renderHook(() => useGetWhiteListeIP(), {
      wrapper: createHookWrapper()
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
    expect(result.current.data).toEqual([
      {
        created_at: '2025-01-28T08:46:35.373Z',
        description: 'Error IP',
        ip_address: '************'
      },
      {
        created_at: '2025-01-21T14:38:46.909Z',
        ip_address: '************',
        description: 'testing sanity'
      }
    ]);
  });

  test('should handle empty response data', async () => {
    server.use(
      http.get('/admin/merchants/1/ip-addresses', () => {
        return HttpResponse.json({ data: { ip_addresses: [] } });
      })
    );

    const { result } = renderHook(() => useGetWhiteListeIP(), {
      wrapper: createHookWrapper()
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toEqual([]);
  });

  test('should handle no search results', async () => {
    mockUseSearchQuery.mockReturnValue({
      value: {
        keyword: ''
      },
      setQuery: vi.fn(),
      clearAll: vi.fn(),
      get: () => '012'
    });
    const { result } = renderHook(() => useGetWhiteListeIP(), {
      wrapper: createHookWrapper()
    });

    expect(result.current.isLoading).toBe(true);

    expect(result.current.data).toEqual([]);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
  });
});
