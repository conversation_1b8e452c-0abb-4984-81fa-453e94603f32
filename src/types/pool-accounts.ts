export type PoolAccountReferenceType = {
  account_id: number;
  account_kora_id: number;
  account_name: string;
  created_at: string;
  customer_email: string;
  customer_name: string;
  id: number;
  reference: string;
  updated_at: string;
  currency: string;
};

export type PoolAccountTransactionType = {
  id: number;
  pool_account_id: number;
  currency: string;
  amount_paid: string;
  transaction_reference: string;
  transaction_date: string;
  status: string;
  source_details: unknown;
  created_at: string;
  updated_at: string;
  pool_account_customer_name: string;
  pool_account_reference: string;
};

export type PoolAccountTransactionDetailsType = {
  id: number;
  pool_account_id: number;
  fee: string;
  net_amount: string;
  currency: string;
  amount_paid: string;
  transaction_reference: string;
  transaction_date: string;
  status: string;
  source_details: {
    [key: string]: string;
  };
  created_at: string;
  updated_at: string;
  pool_account_customer_name: string;
  pool_account_reference: string;
};
