import { TIconNames } from '+containers/Dashboard/Shared/Icons/IconNames';

export interface ICurrencyPair {
  from_currency: string;
  enabled: boolean;
  to_currency: string;
  base_markup: number;
  last_updated: string;
  icon?: TIconNames;
  icon_color?: string;
  status?: string;
  provider_rate?: number;
  kora_rate?: number;
}

export interface ICurrencyPairTable {
  className: string;
  emptyStateHeading: string;
  emptyStateMessage: string;
  annotations: string;
  fields: (each: ICurrencyPair) => {
    data: {
      currency_pair: React.ReactNode;
      markup: React.ReactNode;
      pair_status: React.ReactNode;
      last_modified: React.ReactNode;
    };
  };
}

export interface ICurrencyPairActionModal {
  action: 'disable' | 'enable' | 'edit' | 'confirm';
  close: () => void;
  currencyPair?: ICurrencyPair | null;
  isMarkupLoading?: boolean;
}
export type TConversionsPayload = {
  enable?: boolean;
  from_currency?: string;
  to_currency?: string;
  markup?: number;
  markup_limit?: number;
};
export type TConversionsError = { response: { data: { data: { [key: string]: { customErrorMessage: string } } } & { message?: string } } };

export type TCurrencyMarkup = {
  [key: string]: {
    activated: boolean;
    enabled: boolean;
    markup: {
      kora: {
        value: number;
      };
      merchant: {
        value: number;
        limit: number;
      };
    };
  };
};

export type TConversionsState = {
  activeModal: string;
  settlementCurrency: string;
  currencyList: string[];
  rateMarkup: string;
  markupLimit: string;
  enabled: boolean;
};

export type TCurrencyStructure = { [key: string]: { activated: boolean; rate: string; limit: string } };
export type TError = { response: { data: { data: { message: string } } } };
export type conversionsTableDataType = {
  merchant: string;
  transaction_id: string;
  source_amount: number;
  source_currency: string;
  transaction_date: string;
  reference: string;
  account: {
    name: string;
  };
  destination_currency: number;
  converted_amount: number;
};

export interface IKoraRateResponse {
  data: {
    from_currency: string;
    to_currency: string;
    provider_rate: number;
    provider_rate_expiry: number;
    from_currency_rate: number;
    to_currency_rate: number;
    provider: string;
    current_markup: {
      rate: number;
      percent_value: number;
      from_currency_rate: number;
      to_currency_rate: number;
    };
  };
}

export interface IMarkupResponse {
  data: {
    value: number;
    last_updated_at: string;
  };
}

export interface IUpdateMarkUpData {
  from_currency?: string | undefined;
  to_currency?: string | undefined;
  base_markup: number;
}

export interface IConversionLimit {
  max_amount: number;
  min_amount?: number;
  currency?: string | undefined;
}

export interface IFetchConversionLimitResponse {
  data: IConversionLimit;
}

export interface IUpdateCurrencyPairStatusData {
  from_currency?: string | undefined;
  to_currency?: string | undefined;
  enable: boolean;
}
export interface ErrorResponse {
  message: string;
}

export interface IFetchCurrencyPairsResponse {
  data: ICurrencyPair[];
}
