import { TableTypes } from './tables';

interface FilterBase {
  type: 'reference' | 'status' | 'text' | 'amount' | 'select' | 'date-range' | 'processor' | string;
  default?: string;
  hide?: (data: any) => boolean;
}

interface WithStatus extends FilterBase {
  elementType: 'status';
  statusOptions: string[];
  label: string;
}

interface WithText extends FilterBase {
  elementType: 'text';
  inputPlaceholder: string;
  inputPostScript?: string;
  minLength?: number;
  maxLength?: number;
  validate: (value: string) => string;
  label: string;
}

interface WithAmount extends FilterBase {
  elementType: 'amount';
  amountRangeOptions?: string[];
  subFilterKey: string;
  convertFromCamelCase: boolean;
  label: string;
}

interface WithSelect extends FilterBase {
  elementType: 'select';
  externalStateData?: string;
  selectOptions?: { name: string; value: string }[];
  label: string;
}

interface WithDate extends FilterBase {
  elementType: 'date' | 'date-range';
  processDate: boolean;
  dateRangeOptions: string[];
  datePickerPlaceholder: string;
  datePickerProps: Record<string, string>;
  label: string;
}

interface WithReference extends FilterBase {
  elementType: 'reference';
  referenceLabel: string;
  referencePlaceholder: string;
}

export type FilterOption = WithStatus | WithAmount | WithSelect | WithDate | WithReference | WithText;
export type ParamType = Record<string, string | boolean | Date | unknown>;
export type ErrorType = Partial<Record<string, string>>;

export type filterButton = {
  text: string;
  image: React.ReactNode;
  action: () => void;
  permissionsArray?: string[];
  show?: boolean;
  dataTestId?: string;
} | null;

export interface IDateRange {
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
}
export interface IAllDateTimeProps extends IDateRange {
  meridiemStart: string | null;
  meridiemEnd: string | null;
  startTime: string;
  endTime: string;
}

export interface IDateFilterProps {
  selectedDate: IDateRange;
  onDateChange: (date: IDateRange) => void;
  isCleared?: boolean;
  tableType?: TableTypes;
  hideCalendarIcon?: boolean;
  customDateOptions?: readonly string[];
  className?: string;
}
