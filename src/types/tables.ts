import { ReactElement, ReactNode } from 'react';

import { IBatchSearch } from './searchInput';
import { FileFormatType } from './fileFormat';
import { moduleType, Nullable } from './utils';

export type TableTypes = moduleType;

export type BulkActionType<T> = {
  id: number;
  type: string;
  user: { name: string };
  references: T[keyof T][];
  status: string;
  createdAt: string;
  updatedAt: string;
};
export interface ITableProps<T> {
  type: TableTypes;
  filterType?: TableTypes;
  subType?: string; // sub types of the table
  header: string | React.ReactNode;
  className: string;
  tableHeadings: string[]; // previously 'headings' on merchant dashboard
  loading: boolean;
  totalItems: Nullable<number>;
  pageSize: Nullable<number>;
  current: Nullable<number>;
  limitAction?: (x: number) => void;
  actionFn?: (x: number) => void;
  annotation: string;
  emptyStateHeading: string;
  emptyStateMessage: string | React.ReactNode;
  loaderBackground: string;
  tableWrapperClassName?: string;
  hideTableHeadings?: boolean; // previously 'hidden' on merchant dashboard
  isTableBordered?: boolean; // previously 'borderedTable' on merchant dashboard
  hideTable?: boolean; // previously 'doNotShowIf' on merchnat dashboard
  hasPagination?: boolean;
  hasFilter?: boolean;
  bulkAction?: string;
  filterTotalCount?: number;

  // New Table Field Render
  data?: T[]; // the api data to be iterated over
  rowURL?: string;
  rowKey?: string;
  rowUrlQuery?: string;
  rowFn?: (value: T) => void;
  cursors?: { next_cursor: number; previous_cursor?: number; total_items: number | null; page_size: number };
  totalCount?: number;
  isLoadingCount?: boolean;

  // Table with persisted state
  storedState: any;

  // Table with bulkActions
  hasBulkAction?: boolean;
  filterExportAction: (format: FileFormatType, close: () => void, fieldsToExport: string | string[], references?: string[]) => void;
  filterOptions?: { label: string; value: string | number }[];
  filterActiveCurrency: string;
  filterDefaultStatus: string | null;
  filterKeywordPlaceholder: string;
  filterHandleFilterQuery?: (values: unknown) => void;
  filterShowExport: boolean;
  filterQueryIDPlaceholder?: string;
  filterHasAdvancedFilter?: boolean;
  filterHasBasicFilter?: boolean;
  filterAmountPlaceholder?: string;
  filterName?: string;
  isRowClickable?: boolean;
  showDateFilter?: boolean;
  moreFilterBtn?: ReactElement;
  hideSelectedExport?: boolean;
  showCheckbox?: boolean;
  checkboxStatusesToShow?: string[] | string[][];
  checkBoxStatusesKey?: string | string[];
  checkBoxKey?: string;
  showBulkActionModal?: boolean;
  onSuccessBulkAction?: () => void;
  showBulkActionSpinnerAction?: () => void;
  bulkState?: BulkActionType<T>;
  storeSelectedItem?: (item: (T[keyof T] | null)[]) => void;
  tableHeaderIcon?: Record<string, ReactNode>;
  showExportModalDateRange?: boolean;
  exportModalHeaderBottomBorder?: boolean;
  exportModalScrollable?: boolean;
  batchSearch?: IBatchSearch;
  openSummary?: boolean;
}

interface ITablePropsLegacy<T> extends ITableProps<T> {
  renderFields: false;
  children: React.ReactNode;
}

export type TableField<T> = (iter: T) => {
  style?: Record<string, string | undefined>;
  data: Record<string, string | React.ReactNode>;
};
export interface ITablePropsRenderedChildren<T> extends Partial<ITableProps<T>> {
  renderFields: true;
  children: TableField<T>;
  data: T[]; // the api data to be iterated over
  rowURL?: string;
  rowKey?: string;
  rowFn?: (value: T) => void;
}

export type ITableTotalProps<T> = ITablePropsLegacy<T> | ITablePropsRenderedChildren<T>;
export interface ITableData<T> {
  type: string;
  className: string;
  annotations: string;
  filterTitle?: string;
  header?: string;
  rowURL?: string;
  rowKey?: string;
  emptyStateHeading: string;
  emptyStateMessage?: string;
  hasBasicFilter?: boolean;
  fields: TableField<T>;
}

export type TableSubtypes = 'pending_settlements' | 'ready_settlements' | 'approved_settlements' | 'retry_generate_report';

export type PagingType = {
  total_items: number;
  page_size: number;
  current: number;
  count: number;
  next_cursor: number;
  prev_cursor: number;
};

export type BulkInfoType<T> = {
  data: BulkActionType<T>[];
  paging: PagingType;
};

export type BulkActionProps = {
  type: string;
  subType: string;
  isCompleted: boolean;
  toggleActionCompleted: (value?: boolean) => void;
  exportAction: (field?: any) => void;
  totalCount: number;
  clearSelection: () => void;
  selectedRows: string[];
  disableBulkAction: boolean;
  action: string;
  hideExport?: boolean;
  showModal?: boolean;
  onSuccessBulkAction?: () => void;
  showSpinnerIconAction?: () => void;
};
export const actionBulkActionType = {
  retry_generate_report: 'retry_generate_report',
  refund: 'refund',
  settlement_approval: 'settlement_approval',
  cancel_multiple: 'cancel_multiple',
  process_multiple: 'process_multiple',
  settlement_processing: 'settlement_processing',
  payout_reversal_approve: 'payout_reversal_approve',
  payout_reversal_decline: 'payout_reversal_decline',
  payins_transaction_update: 'payins_transaction_update',
  payout_transaction_update: 'payout_transaction_update'
} as const;

type ActionType = keyof typeof actionBulkActionType;
export type ActionTypeValueT = (typeof actionBulkActionType)[ActionType];
export type MapTypes = {
  type: string;
  modalHeading: string | null;
  actionType: ActionTypeValueT;
  modalDescription: React.FC<any> | string;
  action: (data: { type?: string; action?: string; references: string[] }) => Promise<{ message: string }>;
  btnClass?: string;
  isMaxCheckBoxSelected?: boolean;
  secondBottonColorCode?: string;
};

export type TTypes = {
  type: string;
  actionKey: string;
  action: (data: any) => Promise<{ message: string }>;
  errorMessage: string;
  actionType: string;
  btnClass: string;
};
export interface IPayInsUpdateBulkActionFormStates {
  reasonText: string;
  newStatus: string;
  checked: boolean;
}
export interface IBulkActionsStates {
  payInsUpdateFormProps: {
    isFormValidated: boolean;
  };
}
export type BulkActionStoreType<T = unknown> = {
  bulkInfo: BulkInfoType<T>;
  completedAction: string;

  // Action functions
  setBulkInfo: (bulkInfo: BulkInfoType<T>) => void;
  setCompletedAction: (completedAction: string) => void;
};

