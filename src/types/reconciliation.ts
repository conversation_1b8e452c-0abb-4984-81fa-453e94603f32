export interface IProcessorReportMapping {
  processor_report: string;
  internal_report: string;
}

export type KeyMappingType = (IProcessorReportMapping & { color?: string; id?: string; existing?: boolean })[];

export interface IProcessorFieldMapping {
  processor: string;
  payment_type: string;
  primary_key_mappings: IProcessorReportMapping[];
  comparison_key_mappings: IProcessorReportMapping[];
  reference_key_mapping?: IProcessorReportMapping;
}
export type ReconciliationHistoryType = {
  id: number;
  kora_id: string;
  title: string;
  processor: string;
  payment_type: 'payin' | 'payout';
  processor_file_id: string;
  processor_file_details: {
    key: string;
  };
  report_start_date: string;
  report_end_date: string;
  status: 'pending' | 'successful' | 'processing' | 'failed' | 'cancelled';
  updatedAt: string;
  createdAt: string;
  field_mapping?: IProcessorFieldMapping;
  result_file_id?: number | null;
  result_details?: {
    uploadFilePath: string;
    presignedUrl: string;
    fileName: string;
  } | null;
  completed_at?: string | null;
};

export type ReconciliationDataType = {
  processor: string;
  report_start_date: string;
  report_end_date: string;
  payment_type: 'payin' | 'payout';
  processor_file_id: string;
  processor_file_details: {
    key: string;
  };
  field_mapping: IProcessorFieldMapping;
};

export type ProcessorConfigDataType = {
  processor: string;
  payment_type: 'payin' | 'payout';
  primary_key_mappings: Array<{
    processor_report: string;
    internal_report: string;
  }>;
  comparison_key_mappings: Array<{
    processor_report: string;
    internal_report: string;
  }>;
  reference_key_mapping: {
    processor_report: string;
    internal_report: string;
  };
  metadata: {
    status: string[];
    statusMap: Record<string, string>;
  };
  id: number;
  kora_id: number;
  updatedAt: string;
  createdAt: string;
};

export type UploadSettlementDataType = {
  file_name: string;
  bucket_name: string;
  category: 'settlement_file';
};

export type FileUploadDataValues = {
  category: string;
  createdAt: string;
  encoding: string;
  id: number;
  identifier: string;
  mime: string;
  original_name: string;
  path: string;
  updatedAt: string;
};

export type FileUploadResponseType = {
  data: {
    dataValues: FileUploadDataValues;
    isNewRecord: boolean;
    key: string;
    uniqno: number;
    _changed: Record<string, any>;
    _options: {
      isNewRecord: boolean;
      _schema: string | null;
      _schemaDelimiter: string;
    };
    _previousDataValues: FileUploadDataValues;
  };
  message: string;
};

export type ReportProfileCardType = {
  hidePreview?: boolean;
  processor?: string;
  numberOfColumns?: number;
  label: string;
  displayPreview?: () => void;
};

type optionT = {
  label: string;
  value: string;
};

export type ReconciliationOptionRowType = {
  options: optionT[][];
  value: IProcessorReportMapping;
  onChange: (value: string | number | (string | number)[], field: keyof IProcessorReportMapping) => void;
  onDelete?: () => void;
  fieldType?: 'text' | 'select';
};

export type ReconcileColumnSectionType = {
  comparisonKeyMappings: KeyMappingType;
  primaryKeyMappings: KeyMappingType;
  handleOptionChange: (
    value: string | number | (string | number)[],
    field: keyof IProcessorReportMapping,
    id: string,
    type: MappingType
  ) => void;
  handleDelete: (id: string) => void;
  handleAddNewColumn: () => void;
  handleAutoMatchColumns: (value: boolean) => void;
  autoMatchColumns: boolean;
  removingItems: Set<string>;
  handleCancel: () => void;
  handleStartReconciliation: () => void;
  disableStartReconciliationButton: () => boolean;
  createReconciliation: { isLoading: boolean };
  displayPreview: boolean;
  handlePreviewDisplay: () => void;
  processorReportOptions: string[];
  isLoading: boolean;
  referenceKeyMappings: KeyMappingType;
};

export interface IManageColumnFormData {
  processor: string;
  primary_key_mappings: IProcessorReportMapping[];
  comparison_key_mappings: IProcessorReportMapping[];
  reference_key_mapping: IProcessorReportMapping;
  metadata?: {
    statusMap?: { [key: string]: string };
    status?: string[];
  };
  payment_type: 'payin' | 'payout' | 'both';
}

export type ProcessorFormMappingType = {
  processor: IManageColumnFormData['processor'];
  payment_type: Exclude<IManageColumnFormData['payment_type'], 'both'>;
};

export type MappingType = 'primaryKeyMappings' | 'comparisonKeyMappings' | 'referenceKeyMappings' | 'statusKeyMappings';
