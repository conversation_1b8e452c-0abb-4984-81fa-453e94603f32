import { regions } from '+utils';

import { FileFormatType } from './fileFormat';

export type ValidationT = {
  [key in 'first_name' | 'last_name' | 'date_of_birth' | 'selfie']: ValidationValueT;
};

export type ValidationValueT = {
  value: string;
  match: boolean;
  confidence_rating?: number;
};

export type DocumentDetailsT = {
  key: string;
  description: string;
};

export type MetaDataT = {
  [key in 'document_details' | 'files' | 'validation']: Array<DocumentDetailsT> | ValidationT;
};

export type MerchantT = {
  reference: string;
  name: string;
  email: string;
};

export type VerificationDataT = {
  reference: string;
  id: string;
  type: string;
  class: string;
  identity_type: string;
  identity_type_description: string;
  country: string;
  requested_by: string;
  merchant: MerchantT;
  status: string;
  date_created: string;
  metadata: MetaDataT;
  image: string;
  validation: ValidationT;
};

export type VerificationEventResultModalT = {
  close: () => void;
  idName: string;
  data: VerificationDataT;
  setFileToView?: (file: string) => void;
};

export type CountryAccessT = {
  enabled: boolean;
  kyb?: KYBType;
  kyc?: KYCTYPE;
};

export type AccessDataType = {
  kora_id?: number | string;
  enable?: boolean;
  ng?: CountryAccessT;
  gh?: CountryAccessT;
  za?: CountryAccessT;
  ke?: CountryAccessT;
};

export interface IAddMerchantsModal {
  close: () => void;
  refetchMerchant: () => void;
  countries?: Array<string>;
  merchantName?: string;
  koraId?: number;
}

export type CheckboxType = {
  label: string;
  value: string;
};

export type FilterMerchantType = {
  kora_id: number;
  name: string;
};

export interface IInput {
  label?: React.ReactNode;
  placeholder?: string;
  id: string;
  value: string | string[] | number | undefined;
  onChange: (e: string | string[] | number) => void;
}

export interface ICheckboxInput extends IInput {
  data?: CheckboxType[];
  borderTop?: boolean;
  setChecked: (e: Array<string>) => void;
  disabled?: boolean;
  ableToDisableAllCheckboxes?: boolean;
  disableAllChecboxesLabel?: string;
  onChangeDisableAllCheckboxes?: (e: boolean) => void;
}

export interface IIdentityMerchant {
  reference: string;
  name: string;
  active: string;
  date_created: string;
  enabled_countries: Array<string>;
  has_custom_setting: boolean;
}

export type IdentityTablePropsT<T> = {
  resolvedData: {
    data: T[];
    paging: {
      total_items: number;
      page_size: number;
    };
  };
  isFetching: boolean;
  refetch: () => void;
  exportData: (format: FileFormatType, close: () => void, fieldsToExport: Array<string>) => void;
};

export interface IBillingData {
  amount: number;
  currency: string;
  date_created: string;
  identity_type: string;
  narration: string;
  reference: string;
  status: string;
  verification_class: string;
  type: string;
  verification_reference: string;
  verification_type: string;
}

export type ValidationObject = {
  [key: string]: boolean | string | number | ValidationObject | null;
};

export type IdentityTabsType = {
  name: string;
  key: string;
};

export type AccessRequestCountryT = {
  country: keyof typeof regions;
  useCase: Array<string>;
};

export type AccessRequestCountriesT = Array<AccessRequestCountryT>;

export type IdentityErrorType = {
  response: {
    data: {
      message: string;
    };
  };
};

type VerificationType = {
  enabled: boolean;
  billing: BillingType;
};

type KYBType = {
  enabled: boolean;
  ng_cac?: VerificationType;
};

type KYCTYPE = {
  enabled: boolean;
  ng_passport?: VerificationType;
  ng_bvn?: VerificationType;
  ng_nin?: VerificationType;
  ng_vnin?: VerificationType;
  ng_nin_phone?: VerificationType;
  ng_phone?: VerificationType;
  ng_pvc?: VerificationType;
  gh_passport?: VerificationType;
  gh_ssnit?: VerificationType;
  gh_voters_card?: VerificationType;
  gh_drivers_license?: VerificationType;
  ke_passport?: VerificationType;
  ke_national_id?: VerificationType;
  ke_tax_pin?: VerificationType;
  ke_phone?: VerificationType;
  ke_get_phone?: VerificationType;
  za_said?: VerificationType;
};

export type BillingType = {
  currency?: string;
  vat_inclusive?: boolean;
  kyc?: {
    [key: string]: number | null;
  };
  kyb?: {
    [key: string]: number | null;
  };
  selfie_validation?: number;
  selfie_fee?: number;
};

export type KAASAccessDataType = {
  kyb: { [key: string]: { enabled: boolean } };
  kyc: { [key: string]: { enabled: boolean } };
};

export type KAASFeesDataType = {
  enabled: boolean;
  billing: BillingType;
  access: KAASAccessDataType;
};

export interface IConfigFee {
  title: string;
  currentFee: number;
  newFee?: number;
  currency?: string;
  onChangeFee?: (e: number | undefined) => void;
  newCurrency?: string;
  tooltip?: string;
  modified?: boolean;
  configType?: 'reset' | 'default';
  checked?: boolean;
  disabled?: boolean;
  setChecked?: (e: boolean) => void;
}

export type KAASFeeType = {
  key: string;
  title: string;
  currentFee: number;
  newFee?: number;
  currency?: string;
  modified?: boolean;
  selected?: boolean;
};

export type KAASBillingFeesDataType = {
  kyc: KAASFeeType[];
  kyb: KAASFeeType[];
};

export type HasDifferencesWithInitialType = {
  currency?: string;
  vat_inclusive: boolean;
} & KAASBillingFeesDataType;

export type MerchantCountryAccessAPIPayloadType = {
  ng?: {
    enable: boolean;
  };
  ke?: {
    enable: boolean;
  };
  gh?: {
    enable: boolean;
  };
  za?: {
    enable: boolean;
  };
};

export type MerchantIDTypeAccessAPIPayloadType = {
  kyc?: {
    enable?: true;
    ng_bvn?: true;
    ng_nin?: true;
    ng_vnin?: true;
    ng_passport?: true;
    ng_pvc?: true;
    ng_phone?: true;
  };
  kyb?: {
    enable?: true;
    ng_cac?: true;
  };
};

export type IdentityDocumentType = {
  type: string;
  label: string;
  description: string;
};
