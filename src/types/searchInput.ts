import { FilterMerchantType, IInput } from './identity';

export interface ISearchInput extends IInput {
  dropdownData?: FilterMerchantType[];
  setDebouncedSearchInput?: (e: string) => void;
  setShouldRunQuery?: (e: boolean) => void;
  isFetching: boolean;
  disabled?: boolean;
  wrapperClassName?: string;
  onBlur?: React.EventHandler<React.FocusEvent>;
  accessorKey?: keyof FilterMerchantType | null;
  onChange: (e: string | string[] | number | FilterMerchantType) => void;
  name: string;
}
export interface IBatchSearch extends Pick<IBatchQueryProps, 'idCountLimit'| 'showBatchViewer' | 'viewerText'>  {
  showBatchQuery: boolean;
  fieldsToDisplayBatchQuery: string[];
  handleAddIds?: (id: string) => void;
}
export interface IBatchQueryProps{
  placeholder?: string;
  ids: string[];
  idCountLimit?: number;
  idRefLabel?:string;
  idTrimLength?:number;
  showBatchViewer?:boolean;
  viewerText?:string;
}
