/* eslint-disable import/prefer-default-export */
import { setupServer } from 'msw/node';

import {
  billingConfigHandlers,
  cardIssuanceHandlers,
  chargebacksHandlers,
  conversionsHandlers,
  handlers,
  identityConfigHandlers,
  merchantHandlers,
  merchantRegistrationHandlers,
  partnerFundingHandlers,
  pausedPaymentHandlers,
  paymentPreference,
  poolAccountsHandlers,
  processorQueryHandlers,
  productConfigHandlers,
  reconciliationHandlers,
  settlementConversionsHandler,
  spoolingReportHandlers,
  transactionLimitHandlers,
  usersHandlers,
  vbaHanders,
} from './handlers';

const server = setupServer(
  ...handlers,
  ...usersHandlers,
  ...chargebacksHandlers,
  ...cardIssuanceHandlers,
  ...productConfigHandlers,
  ...spoolingReportHandlers,
  ...merchantHandlers,
  ...settlementConversionsHandler,
  ...paymentPreference,
  ...partnerFundingHandlers,
  ...pausedPaymentHandlers,
  ...conversionsHandlers,
  ...transactionLimitHandlers,
  ...vbaHanders,
  ...billingConfigHandlers,
  ...processorQueryHandlers,
  ...reconciliationHandlers,
  ...identityConfigHandlers,
  ...poolAccountsHandlers,
  ...merchantRegistrationHandlers
);

export { server };
